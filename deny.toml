[advisories]
version = 2
db-path = "~/.cargo/advisory-db"
db-urls = ["https://github.com/rustsec/advisory-db"]
yanked = "deny"

ignore = [
  # yaml-rust is required for syntect
  "RUSTSEC-2024-0320",
  # proc-macro-error is a transitive dependency of a number of dependencies, 
  # this is allowed for now til the ecosystem migrates away
  "RUSTSEC-2024-0370",
  # blocked on portable-pty migrating away from serial
  "RUSTSEC-2017-0008",
  # gtk3-rs is used by tao/wry, we can not remove these til the migrate
  "RUSTSEC-2024-0411",
  "RUSTSEC-2024-0412",
  "RUSTSEC-2024-0413",
  "RUSTSEC-2024-0414",
  "RUSTSEC-2024-0415",
  "RUSTSEC-2024-0416",
  "RUSTSEC-2024-0417",
  "RUSTSEC-2024-0418",
  "RUSTSEC-2024-0419",
  "RUSTSEC-2024-0420",
  # glib is required by gtk via tao, depends on upgrading those dependencies
  "RUSTSEC-2024-0429",
  # paste is used in core deps
  "RUSTSEC-2024-0436",
]

[licenses]
version = 2
private = { ignore = true }
# licenses should only be approved licences by Amazon, if you
# are unsure please contact a maintainer otherwise other licences
# will be denied
allow = [
  "Apache-2.0",
  "Apache-2.0 WITH LLVM-exception",
  "BSD-2-Clause",
  "BSD-3-Clause",
  "BSL-1.0",
  "CC-PDDC",
  "CC0-1.0",
  "ISC",
  "MIT",
  "MIT-0",
  "MPL-2.0",
  "NCSA",
  "OpenSSL",
  "Unicode-3.0",
  "Zlib",
  "CDLA-Permissive-2.0",
]

[[licenses.clarify]]
name = "ring"
version = "*"
expression = "MIT AND ISC AND OpenSSL"
license-files = [{ path = "LICENSE", hash = 0xbd0eed23 }]

[bans]
multiple-versions = "allow"
wildcards = "deny"
allow-wildcard-paths = true
highlight = "all"

[bans.workspace-dependencies]
duplicates = "allow"
include-path-dependencies = true
unused = "deny"

[[bans.deny]]
name = "openssl"

[[bans.deny]]
name = "git2"

[[bans.deny]]
name = "tempdir"

[sources]
unknown-registry = "deny"
unknown-git = "deny"
allow-registry = ["https://github.com/rust-lang/crates.io-index"]
