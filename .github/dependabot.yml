# Please see the documentation for all configuration options:
# https://docs.github.com/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"
    assignees:
      - "chaynabors"
    open-pull-requests-limit: 100
    commit-message:
      prefix: ci
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    assignees:
      - "chaynabors"
    open-pull-requests-limit: 100
    commit-message:
      prefix: fix
      prefix-development: chore
      include: scope
    groups:
      aws:
        patterns: ["aws-*"]
      clap:
        patterns: ["clap", "clap_*"]
