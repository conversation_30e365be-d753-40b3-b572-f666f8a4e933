type: :app # no need to change this for .pkg, .dmg and executables
os: :osx
name: Amazon Q.app
outputs:
  - label: macos
    path: Amazon Q.app
app:
  :identifier: com.amazon.codewhisperer # this has to be same as Bundle identifier value in info.plist
  signing_requirements:
    certificate_type: :developerIDAppDistribution
    app_id_prefix: 94KV3E626L # AMZN Mobile LLC
    signing_args:
      entitlements_path: SIGNING_METADATA/entitlements.plist
  embedded_requirements:
    Contents/MacOS/q:
      :identifier: com.amazon.q
      signing_args:
    Contents/MacOS/qterm:
      :identifier: com.amazon.qterm
      signing_args:
