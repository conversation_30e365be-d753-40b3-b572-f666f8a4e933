{"types": [{"name": "amazonQProfileRegion", "type": "string", "description": "Region of the Q Profile associated with a metric\n- \"n/a\" if metric is not associated with a profile or region.\n- \"not-set\" if metric is associated with a profile, but profile is unknown."}, {"name": "ssoRegion", "type": "string", "description": "Region of the current SSO connection. Typically associated with credentialStartUrl\n- \"n/a\" if metric is not associated with a region.\n- \"not-set\" if metric is associated with a region, but region is unknown."}, {"name": "profileCount", "type": "int", "description": "The number of profiles that were available to choose from"}, {"name": "source", "type": "string", "description": "Identifies the source component where the telemetry event originated."}, {"name": "amazonqConversationId", "type": "string", "description": "Uniquely identifies a message with which the user interacts."}, {"name": "codewhispererterminal_command", "type": "string", "description": "The CLI tool a completion was for"}, {"name": "codewhispererterminal_duration", "type": "int", "description": "A duration in milliseconds"}, {"name": "codewhispererterminal_timeToSuggestion", "type": "int", "description": "How long the AI model roundtrip took"}, {"name": "codewhispererterminal_accepted", "type": "boolean", "description": "Whether the user accepted a completion"}, {"name": "codewhispererterminal_suggestionState", "type": "string", "description": "The result of a suggestion from the service"}, {"name": "codewhispererterminal_typedCount", "type": "int", "description": "A count of characters in the user edit buffer prior to accepting a suggestion"}, {"name": "codewhispererterminal_suggestedCount", "type": "int", "description": "A count of characters in an accepted suggestion"}, {"name": "codewhispererterminal_subcommand", "type": "string", "description": "A codewhisperer CLI subcommand"}, {"name": "codewhispererterminal_doctor<PERSON><PERSON><PERSON>", "type": "string", "description": "A codewhisperer CLI doctor check"}, {"name": "codewhispererterminal_menuBarItem", "type": "string", "description": "A dashboard menu bar item"}, {"name": "codewhispererterminal_route", "type": "string", "description": "The local route of a dashboard page"}, {"name": "codewhispererterminal_terminal", "type": "string", "description": "The user terminal"}, {"name": "codewhispererterminal_terminalVersion", "type": "string", "description": "The user terminal version"}, {"name": "codewhispererterminal_shell", "type": "string", "description": "The user shell"}, {"name": "codewhispererterminal_inCloudshell", "type": "boolean", "description": "Whether the CLI is running in the AWS CloudShell environment"}, {"name": "codewhispererterminal_shellVersion", "type": "string", "description": "The user shell version"}, {"name": "codewhispererterminal_oldClientId", "type": "string", "description": "The old client id that was generated incorrectly and is used to correlate old events"}, {"name": "credentialStartUrl", "type": "string", "description": "The start URL of current SSO connection"}, {"name": "requestId", "type": "string", "description": "The id assigned to an AWS request"}, {"name": "oauthFlow", "type": "string", "description": "The oauth authentication flow executed by the user, e.g. device code or PKCE"}, {"name": "result", "type": "string", "description": "Whether or not the operation succeeded"}, {"name": "reason", "type": "string", "description": "Description of what caused an error, if any"}, {"name": "codewhispererterminal_toolUseId", "type": "string", "description": "The id assigned to the client by the model representing a tool use event"}, {"name": "codewhispererterminal_toolName", "type": "string", "description": "The name associated with a tool"}, {"name": "codewhispererterminal_isToolUseAccepted", "type": "boolean", "description": "Denotes if a tool use event has been fulfilled"}, {"name": "codewhispererterminal_toolUseIsSuccess", "type": "boolean", "description": "The outcome of a tool use"}, {"name": "codewhispererterminal_utteranceId", "type": "string", "description": "Id associated with a given response from the model"}, {"name": "codewhispererterminal_userInputId", "type": "string", "description": "Id associated with a given user input. This is used to differentiate responses to user input and that of retries from tool uses. This id is the utterance id of the first response following an user input"}, {"name": "codewhispererterminal_isToolValid", "type": "boolean", "description": "If the use of tool as instructed by the model is valid"}, {"name": "codewhispererterminal_contextFileLength", "type": "int", "description": "The length of the files included as part of context management"}, {"name": "codewhispererterminal_mcpServerInitFailureReason", "type": "string", "description": "Reason for which a mcp server has failed to be initialized"}, {"name": "codewhispererterminal_toolsPerMcpServer", "type": "int", "description": "The number of tools provided by a mcp server"}, {"name": "codewhispererterminal_isCustomTool", "type": "boolean", "description": "Denoting whether or not the tool is a custom tool"}, {"name": "codewhispererterminal_customToolInputTokenSize", "type": "int", "description": "Number of tokens used on invoking the custom tool"}, {"name": "codewhispererterminal_customToolOutputTokenSize", "type": "int", "description": "Number of tokens received from invoking the custom tool"}, {"name": "codewhispererterminal_customToolLatency", "type": "int", "description": "Custom tool call latency in seconds"}], "metrics": [{"name": "amazonq_startChat", "description": "Captures start of the conversation with amazonq /dev", "metadata": [{"type": "amazonqConversationId"}, {"type": "credentialStartUrl", "required": false}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_addChatMessage", "description": "Captures active usage with Q Chat in shell", "metadata": [{"type": "amazonqConversationId"}, {"type": "credentialStartUrl", "required": false}, {"type": "codewhispererterminal_inCloudshell"}, {"type": "codewhispererterminal_contextFileLength", "required": false}]}, {"name": "amazonq_endChat", "description": "Captures end of the conversation with amazonq /dev", "metadata": [{"type": "amazonqConversationId"}, {"type": "credentialStartUrl", "required": false}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_userLoggedIn", "description": "Emitted when users log in", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_refreshCredentials", "description": "Emitted when users refresh their credentials", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "requestId"}, {"type": "oauthFlow"}, {"type": "result"}, {"type": "reason", "required": false}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_completionInserted", "description": "Emitted on Autocomplete user decisions", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_terminal"}, {"type": "codewhispererterminal_terminalVersion"}, {"type": "codewhispererterminal_shell"}, {"type": "codewhispererterminal_shellVersion"}, {"type": "codewhispererterminal_command"}, {"type": "codewhispererterminal_duration"}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_inlineShellActioned", "description": "Emitted on InlineShell user decisions", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_terminal"}, {"type": "codewhispererterminal_terminalVersion"}, {"type": "codewhispererterminal_shell"}, {"type": "codewhispererterminal_shellVersion"}, {"type": "codewhispererterminal_duration"}, {"type": "codewhispererterminal_accepted"}, {"type": "codewhispererterminal_typedCount"}, {"type": "codewhispererterminal_suggestedCount"}, {"type": "codewhispererterminal_suggestionState"}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_translationActioned", "description": "Emitted on Translation user decisions", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_terminal"}, {"type": "codewhispererterminal_terminalVersion"}, {"type": "codewhispererterminal_shell"}, {"type": "codewhispererterminal_shellVersion"}, {"type": "codewhispererterminal_duration"}, {"type": "codewhispererterminal_timeToSuggestion"}, {"type": "codewhispererterminal_accepted"}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_cliSubcommandExecuted", "description": "Emitted on CW CLI subcommand executed", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_terminal"}, {"type": "codewhispererterminal_terminalVersion"}, {"type": "codewhispererterminal_shell"}, {"type": "codewhispererterminal_shellVersion"}, {"type": "codewhispererterminal_subcommand"}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_doctorCheckFailed", "description": "Emitted when CW CLI user fails a doctor check", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_terminal"}, {"type": "codewhispererterminal_terminalVersion"}, {"type": "codewhispererterminal_shell"}, {"type": "codewhispererterminal_shellVersion"}, {"type": "codewhispererterminal_doctor<PERSON><PERSON><PERSON>"}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_dashboardPageViewed", "description": "Emitted when a user views a dashboard page", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_route"}]}, {"name": "codewhispererterminal_menuBarActioned", "description": "Emitted when a user clicks the menu bar", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_menuBarItem"}]}, {"name": "codewhispererterminal_figUserMigrated", "description": "Emitted when a user migrates from fig", "passive": false, "metadata": [{"type": "credentialStartUrl"}]}, {"name": "codewhispererterminal_migrateOldClientId", "description": "Emitted once for users who generated an invalid client id", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_oldClientId"}]}, {"name": "codewhispererterminal_toolUseSuggested", "description": "Emitted once per tool use to report outcome of tool use suggested", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "amazonqConversationId"}, {"type": "codewhispererterminal_utteranceId"}, {"type": "codewhispererterminal_userInputId"}, {"type": "codewhispererterminal_toolUseId"}, {"type": "codewhispererterminal_toolName"}, {"type": "codewhispererterminal_isToolUseAccepted"}, {"type": "codewhispererterminal_isToolValid"}, {"type": "codewhispererterminal_toolUseIsSuccess", "required": false}, {"type": "codewhispererterminal_isCustomTool"}, {"type": "codewhispererterminal_customToolInputTokenSize", "required": false}, {"type": "codewhispererterminal_customToolOutputTokenSize", "required": false}, {"type": "codewhispererterminal_customToolLatency", "required": false}]}, {"name": "codewhispererterminal_mcpServerInit", "description": "Emitted once per mcp server on start up", "passive": false, "metadata": [{"type": "amazonqConversationId"}, {"type": "codewhispererterminal_mcpServerInitFailureReason", "required": false}, {"type": "codewhispererterminal_toolsPerMcpServer"}]}, {"name": "amazonq_didSelectProfile", "description": "Emitted after the user's Q Profile has been set, whether the user was prompted with a dialog, or a profile was automatically assigned after signing in.", "metadata": [{"type": "source"}, {"type": "amazonQProfileRegion"}, {"type": "result"}, {"type": "ssoRegion", "required": false}, {"type": "credentialStartUrl", "required": false}, {"type": "profileCount", "required": false}], "passive": true}, {"name": "amazonq_profileState", "description": "Indicates a change in the user's Q Profile state", "metadata": [{"type": "source"}, {"type": "amazonQProfileRegion"}, {"type": "result"}, {"type": "ssoRegion", "required": false}, {"type": "credentialStartUrl", "required": false}], "passive": true}]}