[package]
name = "aws-toolkit-telemetry-definitions"
version = "0.1.0"
edition = "2021"
publish = false

[lints]
workspace = true

[build-dependencies]
convert_case = "0.8.0"
prettyplease = "0.2.32"
quote = "1.0.40"
serde.workspace = true
serde_json.workspace = true
syn = "2.0.101"

[dependencies]
amzn-toolkit-telemetry-client = { path = "../amzn-toolkit-telemetry-client" }
serde.workspace = true

[dev-dependencies]
serde_json.workspace = true
