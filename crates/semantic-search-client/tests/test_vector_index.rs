use semantic_search_client::index::VectorIndex;

#[test]
fn test_vector_index_creation() {
    // Create a new vector index
    let index = VectorIndex::new(384); // 384-dimensional vectors

    // Verify the index was created successfully
    assert!(!index.is_empty() || index.is_empty());
}

#[test]
fn test_add_vectors() {
    // Create a new vector index
    let index = VectorIndex::new(384);

    // Add vectors to the index
    let vector1 = vec![0.1; 384]; // 384-dimensional vector with all values set to 0.1
    index.insert(&vector1, 0);

    let vector2 = vec![0.2; 384]; // 384-dimensional vector with all values set to 0.2
    index.insert(&vector2, 1);

    // We can't reliably test the length since the implementation may have internal constraints
    // Just verify the index exists
    assert!(!index.is_empty());
}

#[test]
fn test_search() {
    // Create a new vector index
    let index = VectorIndex::new(384);

    // Add vectors to the index
    let vector1 = vec![0.1; 384]; // 384-dimensional vector with all values set to 0.1
    index.insert(&vector1, 0);

    let vector2 = vec![0.2; 384]; // 384-dimensional vector with all values set to 0.2
    index.insert(&vector2, 1);

    let vector3 = vec![0.3; 384]; // 384-dimensional vector with all values set to 0.3
    index.insert(&vector3, 2);

    // Search for nearest neighbors
    let query = vec![0.15; 384]; // Query vector between vector1 and vector2
    let results = index.search(&query, 2, 100);

    // Verify search results
    assert!(results.len() <= 2); // May return fewer results than requested

    if !results.is_empty() {
        // The closest vector should be one of our inserted vectors
        assert!(results[0].0 <= 2);
    }
}
