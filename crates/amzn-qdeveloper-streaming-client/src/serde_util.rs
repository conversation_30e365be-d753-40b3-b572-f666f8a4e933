// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub(crate) fn service_quota_exceeded_error_correct_errors(
    mut builder: crate::types::error::builders::ServiceQuotaExceededErrorBuilder,
) -> crate::types::error::builders::ServiceQuotaExceededErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn throttling_error_correct_errors(
    mut builder: crate::types::error::builders::ThrottlingErrorBuilder,
) -> crate::types::error::builders::ThrottlingErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn validation_error_correct_errors(
    mut builder: crate::types::error::builders::ValidationErrorBuilder,
) -> crate::types::error::builders::ValidationErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn access_denied_error_correct_errors(
    mut builder: crate::types::error::builders::AccessDeniedErrorBuilder,
) -> crate::types::error::builders::AccessDeniedErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn internal_server_error_correct_errors(
    mut builder: crate::types::error::builders::InternalServerErrorBuilder,
) -> crate::types::error::builders::InternalServerErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn conflict_exception_correct_errors(
    mut builder: crate::types::error::builders::ConflictErrorBuilder,
) -> crate::types::error::builders::ConflictErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn resource_not_found_exception_correct_errors(
    mut builder: crate::types::error::builders::ResourceNotFoundErrorBuilder,
) -> crate::types::error::builders::ResourceNotFoundErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn assistant_response_event_correct_errors(
    mut builder: crate::types::builders::AssistantResponseEventBuilder,
) -> crate::types::builders::AssistantResponseEventBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn citation_event_correct_errors(
    mut builder: crate::types::builders::CitationEventBuilder,
) -> crate::types::builders::CitationEventBuilder {
    if builder.target.is_none() {
        builder.target = Some(crate::types::CitationTarget::Unknown)
    }
    if builder.citation_link.is_none() {
        builder.citation_link = Some(Default::default())
    }
    builder
}

pub(crate) fn code_event_correct_errors(
    mut builder: crate::types::builders::CodeEventBuilder,
) -> crate::types::builders::CodeEventBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn interaction_components_event_correct_errors(
    mut builder: crate::types::builders::InteractionComponentsEventBuilder,
) -> crate::types::builders::InteractionComponentsEventBuilder {
    if builder.interaction_component_entries.is_none() {
        builder.interaction_component_entries = Some(Default::default())
    }
    builder
}

pub(crate) fn invalid_state_event_correct_errors(
    mut builder: crate::types::builders::InvalidStateEventBuilder,
) -> crate::types::builders::InvalidStateEventBuilder {
    if builder.reason.is_none() {
        builder.reason = "no value was set".parse::<crate::types::InvalidStateReason>().ok()
    }
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn tool_use_event_correct_errors(
    mut builder: crate::types::builders::ToolUseEventBuilder,
) -> crate::types::builders::ToolUseEventBuilder {
    if builder.tool_use_id.is_none() {
        builder.tool_use_id = Some(Default::default())
    }
    if builder.name.is_none() {
        builder.name = Some(Default::default())
    }
    builder
}

pub(crate) fn followup_prompt_correct_errors(
    mut builder: crate::types::builders::FollowupPromptBuilder,
) -> crate::types::builders::FollowupPromptBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn token_usage_correct_errors(
    mut builder: crate::types::builders::TokenUsageBuilder,
) -> crate::types::builders::TokenUsageBuilder {
    if builder.uncached_input_tokens.is_none() {
        builder.uncached_input_tokens = Some(Default::default())
    }
    if builder.output_tokens.is_none() {
        builder.output_tokens = Some(Default::default())
    }
    if builder.total_tokens.is_none() {
        builder.total_tokens = Some(Default::default())
    }
    builder
}

pub(crate) fn tool_result_correct_errors(
    mut builder: crate::types::builders::ToolResultBuilder,
) -> crate::types::builders::ToolResultBuilder {
    if builder.tool_use_id.is_none() {
        builder.tool_use_id = Some(Default::default())
    }
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn interaction_component_entry_correct_errors(
    mut builder: crate::types::builders::InteractionComponentEntryBuilder,
) -> crate::types::builders::InteractionComponentEntryBuilder {
    if builder.interaction_component.is_none() {
        builder.interaction_component = {
            let builder = crate::types::builders::InteractionComponentBuilder::default();
            Some(builder.build())
        }
    }
    builder
}

pub(crate) fn supplementary_web_link_correct_errors(
    mut builder: crate::types::builders::SupplementaryWebLinkBuilder,
) -> crate::types::builders::SupplementaryWebLinkBuilder {
    if builder.url.is_none() {
        builder.url = Some(Default::default())
    }
    if builder.title.is_none() {
        builder.title = Some(Default::default())
    }
    builder
}

pub(crate) fn alert_correct_errors(
    mut builder: crate::types::builders::AlertBuilder,
) -> crate::types::builders::AlertBuilder {
    if builder.r#type.is_none() {
        builder.r#type = "no value was set".parse::<crate::types::AlertType>().ok()
    }
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn progress_correct_errors(
    mut builder: crate::types::builders::ProgressBuilder,
) -> crate::types::builders::ProgressBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn resource_correct_errors(
    mut builder: crate::types::builders::ResourceBuilder,
) -> crate::types::builders::ResourceBuilder {
    if builder.title.is_none() {
        builder.title = Some(Default::default())
    }
    if builder.link.is_none() {
        builder.link = Some(Default::default())
    }
    if builder.description.is_none() {
        builder.description = Some(Default::default())
    }
    if builder.r#type.is_none() {
        builder.r#type = Some(Default::default())
    }
    if builder.arn.is_none() {
        builder.arn = Some(Default::default())
    }
    if builder.resource_json_string.is_none() {
        builder.resource_json_string = Some(Default::default())
    }
    builder
}

pub(crate) fn resource_list_correct_errors(
    mut builder: crate::types::builders::ResourceListBuilder,
) -> crate::types::builders::ResourceListBuilder {
    if builder.items.is_none() {
        builder.items = Some(Default::default())
    }
    builder
}

pub(crate) fn section_correct_errors(
    mut builder: crate::types::builders::SectionBuilder,
) -> crate::types::builders::SectionBuilder {
    if builder.title.is_none() {
        builder.title = Some(Default::default())
    }
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn step_correct_errors(
    mut builder: crate::types::builders::StepBuilder,
) -> crate::types::builders::StepBuilder {
    if builder.id.is_none() {
        builder.id = Some(Default::default())
    }
    if builder.state.is_none() {
        builder.state = "no value was set".parse::<crate::types::StepState>().ok()
    }
    if builder.label.is_none() {
        builder.label = Some(Default::default())
    }
    builder
}

pub(crate) fn suggestions_correct_errors(
    mut builder: crate::types::builders::SuggestionsBuilder,
) -> crate::types::builders::SuggestionsBuilder {
    if builder.items.is_none() {
        builder.items = Some(Default::default())
    }
    builder
}

pub(crate) fn task_details_correct_errors(
    mut builder: crate::types::builders::TaskDetailsBuilder,
) -> crate::types::builders::TaskDetailsBuilder {
    if builder.overview.is_none() {
        builder.overview = {
            let builder = crate::types::builders::TaskOverviewBuilder::default();
            crate::serde_util::task_overview_correct_errors(builder).build().ok()
        }
    }
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn task_reference_correct_errors(
    mut builder: crate::types::builders::TaskReferenceBuilder,
) -> crate::types::builders::TaskReferenceBuilder {
    if builder.task_id.is_none() {
        builder.task_id = Some(Default::default())
    }
    builder
}

pub(crate) fn text_correct_errors(
    mut builder: crate::types::builders::TextBuilder,
) -> crate::types::builders::TextBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn infrastructure_update_transition_correct_errors(
    mut builder: crate::types::builders::InfrastructureUpdateTransitionBuilder,
) -> crate::types::builders::InfrastructureUpdateTransitionBuilder {
    if builder.current_state.is_none() {
        builder.current_state = Some(Default::default())
    }
    if builder.next_state.is_none() {
        builder.next_state = Some(Default::default())
    }
    builder
}

pub(crate) fn task_overview_correct_errors(
    mut builder: crate::types::builders::TaskOverviewBuilder,
) -> crate::types::builders::TaskOverviewBuilder {
    if builder.label.is_none() {
        builder.label = Some(Default::default())
    }
    if builder.description.is_none() {
        builder.description = Some(Default::default())
    }
    builder
}

pub(crate) fn web_link_correct_errors(
    mut builder: crate::types::builders::WebLinkBuilder,
) -> crate::types::builders::WebLinkBuilder {
    if builder.label.is_none() {
        builder.label = Some(Default::default())
    }
    if builder.url.is_none() {
        builder.url = Some(Default::default())
    }
    builder
}

pub(crate) fn cloud_watch_troubleshooting_link_correct_errors(
    mut builder: crate::types::builders::CloudWatchTroubleshootingLinkBuilder,
) -> crate::types::builders::CloudWatchTroubleshootingLinkBuilder {
    if builder.label.is_none() {
        builder.label = Some(Default::default())
    }
    if builder.investigation_payload.is_none() {
        builder.investigation_payload = Some(Default::default())
    }
    builder
}

pub(crate) fn suggestion_correct_errors(
    mut builder: crate::types::builders::SuggestionBuilder,
) -> crate::types::builders::SuggestionBuilder {
    if builder.value.is_none() {
        builder.value = Some(Default::default())
    }
    builder
}

pub(crate) fn task_action_correct_errors(
    mut builder: crate::types::builders::TaskActionBuilder,
) -> crate::types::builders::TaskActionBuilder {
    if builder.label.is_none() {
        builder.label = Some(Default::default())
    }
    if builder.payload.is_none() {
        builder.payload = Some(Default::default())
    }
    builder
}

pub(crate) fn task_action_note_correct_errors(
    mut builder: crate::types::builders::TaskActionNoteBuilder,
) -> crate::types::builders::TaskActionNoteBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}
