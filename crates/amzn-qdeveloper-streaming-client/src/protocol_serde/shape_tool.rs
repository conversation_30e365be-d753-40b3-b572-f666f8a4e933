// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_tool(
    object_28: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::Tool,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    match input {
        crate::types::Tool::ToolSpecification(inner) => {
            #[allow(unused_mut)]
            let mut object_1 = object_28.key("toolSpecification").start_object();
            crate::protocol_serde::shape_tool_specification::ser_tool_specification(&mut object_1, inner)?;
            object_1.finish();
        },
        crate::types::Tool::CachePoint(inner) => {
            #[allow(unused_mut)]
            let mut object_2 = object_28.key("cachePoint").start_object();
            crate::protocol_serde::shape_cache_point::ser_cache_point(&mut object_2, inner)?;
            object_2.finish();
        },
        crate::types::Tool::Unknown => {
            return Err(::aws_smithy_types::error::operation::SerializationError::unknown_variant("Tool"));
        },
    }
    Ok(())
}
