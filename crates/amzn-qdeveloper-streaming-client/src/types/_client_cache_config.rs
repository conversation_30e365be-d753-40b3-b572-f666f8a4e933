// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ClientCacheConfig {
    #[allow(missing_docs)] // documentation missing in model
    pub use_client_caching_only: ::std::option::Option<bool>,
}
impl ClientCacheConfig {
    #[allow(missing_docs)] // documentation missing in model
    pub fn use_client_caching_only(&self) -> ::std::option::Option<bool> {
        self.use_client_caching_only
    }
}
impl ClientCacheConfig {
    /// Creates a new builder-style object to manufacture
    /// [`ClientCacheConfig`](crate::types::ClientCacheConfig).
    pub fn builder() -> crate::types::builders::ClientCacheConfigBuilder {
        crate::types::builders::ClientCacheConfigBuilder::default()
    }
}

/// A builder for [`ClientCacheConfig`](crate::types::ClientCacheConfig).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ClientCacheConfigBuilder {
    pub(crate) use_client_caching_only: ::std::option::Option<bool>,
}
impl ClientCacheConfigBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn use_client_caching_only(mut self, input: bool) -> Self {
        self.use_client_caching_only = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_use_client_caching_only(mut self, input: ::std::option::Option<bool>) -> Self {
        self.use_client_caching_only = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_use_client_caching_only(&self) -> &::std::option::Option<bool> {
        &self.use_client_caching_only
    }

    /// Consumes the builder and constructs a
    /// [`ClientCacheConfig`](crate::types::ClientCacheConfig).
    pub fn build(self) -> crate::types::ClientCacheConfig {
        crate::types::ClientCacheConfig {
            use_client_caching_only: self.use_client_caching_only,
        }
    }
}
