// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Represents the image source itself and the format of the image.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct ImageBlock {
    #[allow(missing_docs)] // documentation missing in model
    pub format: crate::types::ImageFormat,
    /// Image bytes
    pub source: crate::types::ImageSource,
}
impl ImageBlock {
    #[allow(missing_docs)] // documentation missing in model
    pub fn format(&self) -> &crate::types::ImageFormat {
        &self.format
    }

    /// Image bytes
    pub fn source(&self) -> &crate::types::ImageSource {
        &self.source
    }
}
impl ::std::fmt::Debug for ImageBlock {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("ImageBlock");
        formatter.field("format", &self.format);
        formatter.field("source", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
impl ImageBlock {
    /// Creates a new builder-style object to manufacture [`ImageBlock`](crate::types::ImageBlock).
    pub fn builder() -> crate::types::builders::ImageBlockBuilder {
        crate::types::builders::ImageBlockBuilder::default()
    }
}

/// A builder for [`ImageBlock`](crate::types::ImageBlock).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct ImageBlockBuilder {
    pub(crate) format: ::std::option::Option<crate::types::ImageFormat>,
    pub(crate) source: ::std::option::Option<crate::types::ImageSource>,
}
impl ImageBlockBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn format(mut self, input: crate::types::ImageFormat) -> Self {
        self.format = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_format(mut self, input: ::std::option::Option<crate::types::ImageFormat>) -> Self {
        self.format = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_format(&self) -> &::std::option::Option<crate::types::ImageFormat> {
        &self.format
    }

    /// Image bytes
    /// This field is required.
    pub fn source(mut self, input: crate::types::ImageSource) -> Self {
        self.source = ::std::option::Option::Some(input);
        self
    }

    /// Image bytes
    pub fn set_source(mut self, input: ::std::option::Option<crate::types::ImageSource>) -> Self {
        self.source = input;
        self
    }

    /// Image bytes
    pub fn get_source(&self) -> &::std::option::Option<crate::types::ImageSource> {
        &self.source
    }

    /// Consumes the builder and constructs a [`ImageBlock`](crate::types::ImageBlock).
    /// This method will fail if any of the following fields are not set:
    /// - [`format`](crate::types::builders::ImageBlockBuilder::format)
    /// - [`source`](crate::types::builders::ImageBlockBuilder::source)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::ImageBlock, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::ImageBlock {
            format: self.format.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "format",
                    "format was not specified but it is required when building ImageBlock",
                )
            })?,
            source: self.source.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "source",
                    "source was not specified but it is required when building ImageBlock",
                )
            })?,
        })
    }
}
impl ::std::fmt::Debug for ImageBlockBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("ImageBlockBuilder");
        formatter.field("format", &self.format);
        formatter.field("source", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
