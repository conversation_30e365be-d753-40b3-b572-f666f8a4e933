// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Streaming Response Event for Metadata
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct MetadataEvent {
    #[allow(missing_docs)] // documentation missing in model
    pub token_usage: ::std::option::Option<crate::types::TokenUsage>,
}
impl MetadataEvent {
    #[allow(missing_docs)] // documentation missing in model
    pub fn token_usage(&self) -> ::std::option::Option<&crate::types::TokenUsage> {
        self.token_usage.as_ref()
    }
}
impl MetadataEvent {
    /// Creates a new builder-style object to manufacture
    /// [`MetadataEvent`](crate::types::MetadataEvent).
    pub fn builder() -> crate::types::builders::MetadataEventBuilder {
        crate::types::builders::MetadataEventBuilder::default()
    }
}

/// A builder for [`MetadataEvent`](crate::types::MetadataEvent).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct MetadataEventBuilder {
    pub(crate) token_usage: ::std::option::Option<crate::types::TokenUsage>,
}
impl MetadataEventBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn token_usage(mut self, input: crate::types::TokenUsage) -> Self {
        self.token_usage = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_token_usage(mut self, input: ::std::option::Option<crate::types::TokenUsage>) -> Self {
        self.token_usage = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_token_usage(&self) -> &::std::option::Option<crate::types::TokenUsage> {
        &self.token_usage
    }

    /// Consumes the builder and constructs a [`MetadataEvent`](crate::types::MetadataEvent).
    pub fn build(self) -> crate::types::MetadataEvent {
        crate::types::MetadataEvent {
            token_usage: self.token_usage,
        }
    }
}
