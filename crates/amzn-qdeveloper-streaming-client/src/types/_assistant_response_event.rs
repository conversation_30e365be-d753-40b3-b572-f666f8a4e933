// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Streaming Response Event for Assistant Markdown text message.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct AssistantResponseEvent {
    /// The content of the text message in markdown format.
    pub content: ::std::string::String,
    /// Unique identifier for the model used to generate this response
    pub model_id: ::std::option::Option<::std::string::String>,
}
impl AssistantResponseEvent {
    /// The content of the text message in markdown format.
    pub fn content(&self) -> &str {
        use std::ops::Deref;
        self.content.deref()
    }

    /// Unique identifier for the model used to generate this response
    pub fn model_id(&self) -> ::std::option::Option<&str> {
        self.model_id.as_deref()
    }
}
impl ::std::fmt::Debug for AssistantResponseEvent {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("AssistantResponseEvent");
        formatter.field("content", &"*** Sensitive Data Redacted ***");
        formatter.field("model_id", &self.model_id);
        formatter.finish()
    }
}
impl AssistantResponseEvent {
    /// Creates a new builder-style object to manufacture
    /// [`AssistantResponseEvent`](crate::types::AssistantResponseEvent).
    pub fn builder() -> crate::types::builders::AssistantResponseEventBuilder {
        crate::types::builders::AssistantResponseEventBuilder::default()
    }
}

/// A builder for [`AssistantResponseEvent`](crate::types::AssistantResponseEvent).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct AssistantResponseEventBuilder {
    pub(crate) content: ::std::option::Option<::std::string::String>,
    pub(crate) model_id: ::std::option::Option<::std::string::String>,
}
impl AssistantResponseEventBuilder {
    /// The content of the text message in markdown format.
    /// This field is required.
    pub fn content(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.content = ::std::option::Option::Some(input.into());
        self
    }

    /// The content of the text message in markdown format.
    pub fn set_content(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.content = input;
        self
    }

    /// The content of the text message in markdown format.
    pub fn get_content(&self) -> &::std::option::Option<::std::string::String> {
        &self.content
    }

    /// Unique identifier for the model used to generate this response
    pub fn model_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.model_id = ::std::option::Option::Some(input.into());
        self
    }

    /// Unique identifier for the model used to generate this response
    pub fn set_model_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.model_id = input;
        self
    }

    /// Unique identifier for the model used to generate this response
    pub fn get_model_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.model_id
    }

    /// Consumes the builder and constructs a
    /// [`AssistantResponseEvent`](crate::types::AssistantResponseEvent). This method will fail
    /// if any of the following fields are not set:
    /// - [`content`](crate::types::builders::AssistantResponseEventBuilder::content)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::AssistantResponseEvent, ::aws_smithy_types::error::operation::BuildError>
    {
        ::std::result::Result::Ok(crate::types::AssistantResponseEvent {
            content: self.content.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "content",
                    "content was not specified but it is required when building AssistantResponseEvent",
                )
            })?,
            model_id: self.model_id,
        })
    }
}
impl ::std::fmt::Debug for AssistantResponseEventBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("AssistantResponseEventBuilder");
        formatter.field("content", &"*** Sensitive Data Redacted ***");
        formatter.field("model_id", &self.model_id);
        formatter.finish()
    }
}
