{"types": [{"name": "amazonQProfileRegion", "type": "string", "description": "Region of the Q Profile associated with a metric\n- \"n/a\" if metric is not associated with a profile or region.\n- \"not-set\" if metric is associated with a profile, but profile is unknown."}, {"name": "ssoRegion", "type": "string", "description": "Region of the current SSO connection. Typically associated with credentialStartUrl\n- \"n/a\" if metric is not associated with a region.\n- \"not-set\" if metric is associated with a region, but region is unknown."}, {"name": "profileCount", "type": "int", "description": "The number of profiles that were available to choose from"}, {"name": "source", "type": "string", "description": "Identifies the source component where the telemetry event originated."}, {"name": "amazonqConversationId", "type": "string", "description": "Uniquely identifies a message with which the user interacts."}, {"name": "codewhispererterminal_command", "type": "string", "description": "The CLI tool a completion was for"}, {"name": "codewhispererterminal_subcommand", "type": "string", "description": "A codewhisperer CLI subcommand"}, {"name": "codewhispererterminal_chatSlashCommand", "type": "string", "description": "The slash command that was executed (e.g., 'context', 'compact', 'model')"}, {"name": "codewhispererterminal_chatSlashSubcommand", "type": "string", "description": "The subcommand of the slash command, if applicable (e.g., 'list' for '/prompts list')"}, {"name": "codewhispererterminal_inCloudshell", "type": "boolean", "description": "Whether the CLI is running in the AWS CloudShell environment"}, {"name": "credentialStartUrl", "type": "string", "description": "The start URL of current SSO connection"}, {"name": "requestId", "type": "string", "description": "The id assigned to an AWS request. If referring to multiple requests, then the request id's are comma-delimited."}, {"name": "oauthFlow", "type": "string", "description": "The oauth authentication flow executed by the user, e.g. device code or PKCE"}, {"name": "result", "type": "string", "description": "Whether or not the operation succeeded"}, {"name": "reason", "type": "string", "description": "Description of what caused an error, if any. Should be a stable/predictable name."}, {"name": "reasonDesc", "type": "string", "description": "Error message detail. May contain arbitrary message details (unlike the `reason` field)."}, {"name": "statusCode", "type": "int", "description": "The HTTP status code of the request, e.g. 200, 400, etc."}, {"name": "codewhispererterminal_toolUseId", "type": "string", "description": "Comma-delimited id(s) of the tool uses requested by the model."}, {"name": "codewhispererterminal_toolName", "type": "string", "description": "Comma-delimited tool name(s) of the tool uses requested by the model."}, {"name": "codewhispererterminal_isToolUseAccepted", "type": "boolean", "description": "Denotes if a tool use event has been fulfilled"}, {"name": "codewhispererterminal_isToolUseTrusted", "type": "boolean", "description": "Whether or not the tool use was automatically trusted (instead of manually approved)"}, {"name": "codewhispererterminal_toolUseIsSuccess", "type": "boolean", "description": "The outcome of a tool use"}, {"name": "codewhispererterminal_utteranceId", "type": "string", "description": "Id associated with a given response from the model. If multiple responses are included, then the id's are comma-delimited in sequential order."}, {"name": "codewhispererterminal_userInputId", "type": "string", "description": "Id associated with a given user input. This is used to differentiate responses to user input and that of retries from tool uses. This id is the utterance id of the first response following an user input"}, {"name": "codewhispererterminal_isToolValid", "type": "boolean", "description": "If the use of tool as instructed by the model is valid"}, {"name": "codewhispererterminal_contextFileLength", "type": "int", "description": "The length of the files included as part of context management"}, {"name": "codewhispererterminal_mcpServerName", "type": "string", "description": "Name of the MCP server"}, {"name": "codewhispererterminal_mcpServerInitFailureReason", "type": "string", "description": "Reason for which a mcp server has failed to be initialized"}, {"name": "codewhispererterminal_toolsPerMcpServer", "type": "int", "description": "The number of tools provided by a mcp server"}, {"name": "codewhispererterminal_isCustomTool", "type": "boolean", "description": "Denoting whether or not the tool is a custom tool"}, {"name": "codewhispererterminal_customToolInputTokenSize", "type": "int", "description": "Number of tokens used on invoking the custom tool"}, {"name": "codewhispererterminal_customToolOutputTokenSize", "type": "int", "description": "Number of tokens received from invoking the custom tool"}, {"name": "codewhispererterminal_customToolLatency", "type": "int", "description": "Custom tool call latency in seconds"}, {"name": "codewhispererterminal_toolExecutionDurationMs", "type": "int", "description": "The amount of time taken to execute a tool. In milliseconds (ms)."}, {"name": "codewhispererterminal_toolTurnDurationMs", "type": "int", "description": "Total amount of time the tool turn took, including waiting for user input if required. In milliseconds (ms)."}, {"name": "codewhispererterminal_model", "type": "string", "description": "The underlying LLM used by the service, set by the client"}, {"name": "codewhispererterminal_timeToFirstChunksMs", "type": "string", "description": "Time from sending the request to reading the first chunk in the stream. If referring to multiple requests (e.g. when recording a user turn), then each time is comma-delimited. In milliseconds (ms)."}, {"name": "codewhispererterminal_timeBetweenChunksMs", "type": "string", "description": "Comma-delimited times between reading each chunk, starting from the first chunk. In milliseconds (ms)."}, {"name": "codewhispererterminal_chatMessageMetaTags", "type": "string", "description": "Comma-delimited tags to optionally assign to a message. If recorded in the context of a user turn, then this refers to the last message in the turn."}, {"name": "codewhispererterminal_chatConversationType", "type": "string", "allowedValues": ["NotToolUse", "ToolUse"], "description": "Identifies the role of the message in the conversation. If recorded in the context of a user turn, then this refers to the last message in the turn."}, {"name": "codewhispererterminal_userPromptLength", "type": "int", "description": "Total length of the user prompt, in bytes."}, {"name": "codewhispererterminal_assistantResponseLength", "type": "int", "description": "Total length of the assistant response, in bytes. When used to represent multiple responses (e.g. when recording a user turn), then this is the total length of all assistant responses."}, {"name": "codewhispererterminal_followUpCount", "type": "int", "description": "Number of cycles in the user turn (ie, number of tool use and tool use result pairs)."}, {"name": "codewhispererterminal_userTurnDurationSeconds", "type": "int", "description": "Total time spent in the user turn, starting from when the first request is sent."}, {"name": "codewhispererterminal_agentsLoadedCount", "type": "int", "description": "The number of agents loaded"}, {"name": "codewhispererterminal_agentsFailedToLoadCount", "type": "int", "description": "The number of agents that failed to load"}, {"name": "codewhispererterminal_legacyProfileMigrationExecuted", "type": "boolean", "description": "Whether or not the legacy profile configuration migration was executed"}, {"name": "codewhispererterminal_legacyProfileMigratedCount", "type": "int", "description": "The number of agent configs created from a legacy profile migration"}, {"name": "codewhispererterminal_launchedAgent", "type": "string", "description": "The name of the agent launched by the user on startup"}], "metrics": [{"name": "amazonq_startChat", "description": "Captures start of the conversation with amazonq /dev", "metadata": [{"type": "amazonqConversationId"}, {"type": "credentialStartUrl", "required": false}, {"type": "codewhispererterminal_inCloudshell"}, {"type": "codewhispererterminal_model"}]}, {"name": "codewhispererterminal_addChatMessage", "description": "Captures active usage with Q Chat in shell", "metadata": [{"type": "amazonqConversationId"}, {"type": "codewhispererterminal_utteranceId"}, {"type": "credentialStartUrl", "required": false}, {"type": "ssoRegion", "required": false}, {"type": "codewhispererterminal_inCloudshell"}, {"type": "codewhispererterminal_contextFileLength", "required": false}, {"type": "requestId"}, {"type": "result", "required": true}, {"type": "reason", "required": false}, {"type": "reasonDesc", "required": false}, {"type": "statusCode", "required": false}, {"type": "codewhispererterminal_model"}, {"type": "codewhispererterminal_timeToFirstChunksMs", "required": false}, {"type": "codewhispererterminal_timeBetweenChunksMs", "required": false}, {"type": "codewhispererterminal_chatConversationType", "required": false}, {"type": "codewhispererterminal_toolUseId", "required": false}, {"type": "codewhispererterminal_toolName", "required": false}, {"type": "codewhispererterminal_assistantResponseLength", "required": false}, {"type": "codewhispererterminal_chatMessageMetaTags", "required": false}]}, {"name": "codewhispererterminal_recordUserTurnCompletion", "description": "Captures information regarding a user turn (the chain of messages beginning with a user prompt, and ending with either an assistant stop event, tool use denial, or a non-retryable error). Emitted once at the end of every user turn.", "metadata": [{"type": "amazonqConversationId"}, {"type": "credentialStartUrl", "required": false}, {"type": "ssoRegion", "required": false}, {"type": "codewhispererterminal_inCloudshell"}, {"type": "requestId"}, {"type": "codewhispererterminal_utteranceId"}, {"type": "result", "required": true}, {"type": "reason", "required": false}, {"type": "reasonDesc", "required": false}, {"type": "statusCode", "required": false}, {"type": "codewhispererterminal_chatConversationType", "required": false}, {"type": "codewhispererterminal_timeToFirstChunksMs"}, {"type": "codewhispererterminal_userPromptLength"}, {"type": "codewhispererterminal_assistantResponseLength"}, {"type": "codewhispererterminal_userTurnDurationSeconds"}, {"type": "codewhispererterminal_followUpCount"}, {"type": "codewhispererterminal_chatMessageMetaTags", "required": false}]}, {"name": "amazonq_endChat", "description": "Captures end of the conversation with amazonq /dev", "metadata": [{"type": "amazonqConversationId"}, {"type": "credentialStartUrl", "required": false}, {"type": "codewhispererterminal_inCloudshell"}, {"type": "codewhispererterminal_model"}]}, {"name": "codewhispererterminal_userLoggedIn", "description": "Emitted when users log in", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_refreshCredentials", "description": "Emitted when users refresh their credentials", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "requestId"}, {"type": "oauthFlow"}, {"type": "result"}, {"type": "reason", "required": false}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_cliSubcommandExecuted", "description": "Emitted on CW CLI subcommand executed", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "codewhispererterminal_subcommand"}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_chatSlashCommandExecuted", "description": "Emitted when a slash command is executed in Q Chat", "passive": false, "metadata": [{"type": "credentialStartUrl", "required": false}, {"type": "ssoRegion", "required": false}, {"type": "amazonqConversationId"}, {"type": "codewhispererterminal_chatSlashCommand"}, {"type": "codewhispererterminal_chatSlashSubcommand", "required": false}, {"type": "result"}, {"type": "reason", "required": false}, {"type": "codewhispererterminal_inCloudshell"}]}, {"name": "codewhispererterminal_toolUseSuggested", "description": "Emitted once per tool use to report outcome of tool use suggested", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "amazonqConversationId"}, {"type": "codewhispererterminal_utteranceId"}, {"type": "codewhispererterminal_userInputId"}, {"type": "codewhispererterminal_toolUseId"}, {"type": "codewhispererterminal_toolName"}, {"type": "codewhispererterminal_isToolUseAccepted"}, {"type": "codewhispererterminal_isToolValid"}, {"type": "codewhispererterminal_toolUseIsSuccess", "required": false}, {"type": "reasonDesc", "required": false}, {"type": "codewhispererterminal_isCustomTool"}, {"type": "codewhispererterminal_customToolInputTokenSize", "required": false}, {"type": "codewhispererterminal_customToolOutputTokenSize", "required": false}, {"type": "codewhispererterminal_customToolLatency", "required": false}, {"type": "codewhispererterminal_model"}, {"type": "codewhispererterminal_toolExecutionDurationMs", "required": false}, {"type": "codewhispererterminal_toolTurnDurationMs", "required": false}, {"type": "codewhispererterminal_isToolUseTrusted", "required": false}]}, {"name": "codewhispererterminal_mcpServerInit", "description": "Emitted once per mcp server on start up", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "amazonqConversationId"}, {"type": "codewhispererterminal_mcpServerName"}, {"type": "codewhispererterminal_mcpServerInitFailureReason", "required": false}, {"type": "codewhispererterminal_toolsPerMcpServer"}]}, {"name": "codewhispererterminal_agentConfigInit", "description": "Emitted once when starting a new q chat session after agent configuration is initialized", "passive": false, "metadata": [{"type": "credentialStartUrl"}, {"type": "amazonqConversationId"}, {"type": "codewhispererterminal_agentsLoadedCount"}, {"type": "codewhispererterminal_agentsFailedToLoadCount"}, {"type": "codewhispererterminal_legacyProfileMigrationExecuted", "required": false}, {"type": "codewhispererterminal_legacyProfileMigratedCount", "required": false}, {"type": "codewhispererterminal_launchedAgent", "required": false}]}, {"name": "amazonq_didSelectProfile", "description": "Emitted after the user's Q Profile has been set, whether the user was prompted with a dialog, or a profile was automatically assigned after signing in.", "metadata": [{"type": "source"}, {"type": "amazonQProfileRegion"}, {"type": "result"}, {"type": "ssoRegion", "required": false}, {"type": "credentialStartUrl", "required": false}, {"type": "profileCount", "required": false}], "passive": true}, {"name": "amazonq_profileState", "description": "Indicates a change in the user's Q Profile state", "metadata": [{"type": "source"}, {"type": "amazonQProfileRegion"}, {"type": "result"}, {"type": "ssoRegion", "required": false}, {"type": "credentialStartUrl", "required": false}], "passive": true}, {"name": "amazonq_messageResponseError", "description": "When an error has occurred in response to a prompt", "metadata": [{"type": "credentialStartUrl", "required": false}, {"type": "ssoRegion", "required": false}, {"type": "amazonqConversationId"}, {"type": "requestId", "required": false}, {"type": "codewhispererterminal_utteranceId", "required": false}, {"type": "codewhispererterminal_contextFileLength", "required": false}, {"type": "result"}, {"type": "reason", "required": false}, {"type": "reasonDesc", "required": false}, {"type": "statusCode", "required": false}]}]}