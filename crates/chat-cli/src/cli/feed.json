{"$schema": "./feed-schema.json", "entries": [{"$comment": "This is the next release, any changes here", "type": "release", "date": "2999-01-01", "version": "0.0.0", "title": "Version 0.0.0", "hidden": true, "changes": []}, {"type": "release", "date": "2025-07-09", "version": "1.12.4", "title": "Version 1.12.4", "changes": [{"type": "added", "description": "Extra options for history compaction in `q chat` - see `/compact --help` - [#402](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/402)"}, {"type": "fixed", "description": "Issues with `/compact` - [#407](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/407)"}]}, {"type": "release", "date": "2025-07-03", "version": "1.12.3", "title": "Version 1.12.3", "changes": [{"type": "fixed", "description": "Piping input into `q chat` - [#363](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/363)"}, {"type": "fixed", "description": "An issue with `/compact` not being executed on context window overflow - [#365](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/365)"}, {"type": "changed", "description": "The `-a` flag to alias to `--trust-all-tools` - [#372](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/372)"}, {"type": "added", "description": "Terminal bell support for the emacs eat terminal - [#357](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/357)"}]}, {"type": "release", "date": "2025-06-30", "version": "1.12.2", "title": "Version 1.12.2", "changes": [{"type": "added", "description": "Autocompletion with ghost text in `q chat`. Accept completions using right arrow or `Ctrl + f` - [#288](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/288)"}, {"type": "added", "description": "A purpose tag for the `fs_write` tool that summarizes the change being made - [#279](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/279)"}, {"type": "changed", "description": "The `/context hooks` slash command to `/hooks` in `q chat` - [#223](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/223)"}, {"type": "fixed", "description": "`--trust-tools` to work with MCP tools in `q chat` - [#206](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/206)"}, {"type": "fixed", "description": "Formatting for error messages on prompt retrieval from MCP servers - [#216](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/216)"}, {"type": "fixed", "description": "File name extension inconsistency with `/save` and `/load` in `q chat` - [#289](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/289)"}]}, {"type": "release", "date": "2025-06-17", "version": "1.12.1", "title": "Version 1.12.1", "changes": [{"type": "changed", "description": "The default `q chat` model to Sonnet 4 for users in the IAD region - [#219](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/219)"}, {"type": "added", "description": "Support for passing arguments to MCP server configuration in the CLI - [#262](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/262)"}, {"type": "added", "description": "Support for disabling MCP servers with `disabled: true` - [#257](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/257)"}, {"type": "added", "description": "Instructions on using `/prompts` in `q chat` - [#208](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/208)"}, {"type": "fixed", "description": "Validation errors with `/compact` in `q chat` - [#224](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/224)"}, {"type": "fixed", "description": "Issues resolving relative paths with `fs_write` in `q chat` - [#256](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/256)"}]}, {"type": "release", "date": "2025-06-11", "version": "1.12.0", "title": "Version 1.12.0", "changes": [{"type": "added", "description": "Support for upgrading to a pro subscription with `/subscribe` in `q chat` - [#143](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/143)"}, {"type": "fixed", "description": "`q chat` failing to launch in some Linux installations - [#169](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/169)"}]}, {"type": "release", "date": "2025-06-04", "version": "1.11.0", "title": "Version 1.11.0", "changes": [{"type": "added", "description": "Model selection and Sonnet v4 support with `/model` in `q chat` - [#157](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/157)"}, {"type": "fixed", "description": "Formatting issues with the `fs_read` tool in `q chat` - [#122](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/122)"}, {"type": "fixed", "description": "Various issues in `q chat`"}]}, {"type": "release", "date": "2025-05-21", "version": "1.10.2", "title": "Version 1.10.2", "changes": [{"type": "added", "description": "Server load timeout for no interactive mode - [#1851](https://github.com/aws/amazon-q-developer-cli/pull/1851)"}, {"type": "added", "description": "`chat.editMode` to tips - [#1900](https://github.com/aws/amazon-q-developer-cli/pull/1900)"}, {"type": "fixed", "description": "Unqualified tools from being included in the tools schema - [#84](https://github.com/aws/amazon-q-developer-cli-autocomplete/pull/84)"}, {"type": "fixed", "description": "An issue where an early error from an mcp server will cause loading status to be terminated prematurely - [#1919](https://github.com/aws/amazon-q-developer-cli/pull/1919)"}, {"type": "fixed", "description": "Tools and prompts not being sorted - [#1809](https://github.com/aws/amazon-q-developer-cli/pull/1809)"}, {"type": "fixed", "description": "Various issues with `q chat`"}]}, {"type": "release", "date": "2025-05-16", "version": "1.10.1", "title": "Version 1.10.1", "changes": [{"type": "fixed", "description": "An issue where `q chat` would prompt for login while already logged in - [#1862](https://github.com/aws/amazon-q-developer-cli/pull/1862)"}]}, {"type": "release", "date": "2025-05-15", "version": "1.10.0", "title": "Version 1.10.0", "changes": [{"type": "added", "description": "Support for FRA (region `eu-central-1`) for pro users - [#1333](https://github.com/aws/amazon-q-developer-cli/pull/1333)"}, {"type": "added", "description": "Persistent conversations by working directory with `q chat --resume` - [#1767](https://github.com/aws/amazon-q-developer-cli/pull/1767)"}, {"type": "added", "description": "Background MCP server loading in `q chat` - [#1775](https://github.com/aws/amazon-q-developer-cli/pull/1775)"}, {"type": "added", "description": "Image support in `q chat` - [#1489](https://github.com/aws/amazon-q-developer-cli/pull/1489)"}, {"type": "added", "description": "Git-aware file selection in the `q chat` fuzzy finder - [#1649](https://github.com/aws/amazon-q-developer-cli/pull/1649)"}, {"type": "added", "description": "Commands `/save` and `/load` for saving and loading conversation state in `q chat` - [#1762](https://github.com/aws/amazon-q-developer-cli/pull/1762)"}, {"type": "added", "description": "A new subcommand `q mcp` for updating MCP server configuration - [#1836](https://github.com/aws/amazon-q-developer-cli/pull/1836)"}, {"type": "added", "description": "An improved `/context show --expand` in `q chat` - [#1762](https://github.com/aws/amazon-q-developer-cli/pull/1762)"}, {"type": "added", "description": "A new flag `--changelog` to `q version` for displaying the change log from the CLI - [#1375](https://github.com/aws/amazon-q-developer-cli/pull/1375)"}, {"type": "fixed", "description": "Context files causing overflow in `q chat` - [#1331](https://github.com/aws/amazon-q-developer-cli/pull/1331)"}, {"type": "fixed", "description": "An issue with token overflow in the `/usage` command in `q chat` - [#1762](https://github.com/aws/amazon-q-developer-cli/pull/1762)"}, {"type": "fixed", "description": "An issue where non-interactive chat sessions displayed welcome banner - [#1437](https://github.com/aws/amazon-q-developer-cli/pull/1437)"}, {"type": "fixed", "description": "An issue where the `/context` chat command reference was duplicated in `/help` - [#1438](https://github.com/aws/amazon-q-developer-cli/pull/1438)"}, {"type": "fixed", "description": "Preferred default of fuzzy find to `ctrl+s` in `q chat` - [#1442](https://github.com/aws/amazon-q-developer-cli/pull/1442)"}, {"type": "fixed", "description": "Various issues in `q chat`"}]}, {"type": "release", "date": "2025-04-28", "version": "1.9.1", "title": "Version 1.9.1", "changes": [{"type": "fixed", "description": "A bug where chat would sometimes become unresponsive"}]}, {"type": "release", "date": "2025-04-28", "version": "1.9.0", "title": "Version 1.9.0", "changes": [{"type": "added", "description": "Model Context Protocol (MCP) support in `q chat` - [#1365](https://github.com/aws/amazon-q-developer-cli/pull/1365)"}, {"type": "added", "description": "Fuzzy finding support for selecting profiles in `q chat` - [#1388](https://github.com/aws/amazon-q-developer-cli/pull/1388)"}, {"type": "changed", "description": "`/compact` to display the summary by default in `q chat` - [#1313](https://github.com/aws/amazon-q-developer-cli/pull/1313)"}, {"type": "fixed", "description": "Various issues in `q chat`"}]}, {"type": "release", "date": "2025-04-25", "version": "1.8.1", "title": "Version 1.8.1", "changes": [{"type": "fixed", "description": "An issue with `/compact` failing in `q chat`"}]}, {"type": "release", "date": "2025-04-22", "version": "1.8.0", "title": "Version 1.8.0", "changes": [{"type": "added", "description": "Fuzzy search for slash commands in `q chat` with `Ctrl + k` - [#1181](https://github.com/aws/amazon-q-developer-cli/pull/1181)"}, {"type": "added", "description": "A new capability for dynamically adding context to messages in `q chat` with \"context hooks\" - [#1218](https://github.com/aws/amazon-q-developer-cli/pull/1218)"}, {"type": "added", "description": "A new command `/usage` in `q chat` to display an estimate of the context window usage - [#1177](https://github.com/aws/amazon-q-developer-cli/pull/1177)"}, {"type": "added", "description": "A new greeting UI with rotating tips in `q chat` - [#1262](https://github.com/aws/amazon-q-developer-cli/pull/1262)"}, {"type": "added", "description": "An extra confirmation prompt before clearing the history in `q chat` with `/clear` - [#1180](https://github.com/aws/amazon-q-developer-cli/pull/1180)"}, {"type": "added", "description": "Support for using a system proxy with the `HTTP_PROXY` environment variable - [#1199](https://github.com/aws/amazon-q-developer-cli/pull/1199)"}, {"type": "added", "description": "The ability to reset a single tool's permissions in `q chat` with `/tools reset` - [#1102](https://github.com/aws/amazon-q-developer-cli/pull/1102)"}, {"type": "added", "description": "Various UI improvements in `q chat`"}, {"type": "fixed", "description": "Arguments for `EDITOR` not being correctly parsed by the `/editor` command in `q chat` - [#1209](https://github.com/aws/amazon-q-developer-cli/pull/1209)"}, {"type": "fixed", "description": "The `/compact` command failing to compact history after the context window has been filled in `q chat` - [#1275](https://github.com/aws/amazon-q-developer-cli/pull/1275)"}, {"type": "fixed", "description": "Various issues in `q chat`"}]}, {"type": "release", "date": "2025-04-10", "version": "1.7.3", "title": "Version 1.7.3", "changes": [{"type": "added", "description": "A new command `/tools` in `q chat` for granular tool permissioning - [#1054](https://github.com/aws/amazon-q-developer-cli/pull/1054)"}, {"type": "added", "description": "A new command `/compact` in `q chat` for summarizing the conversation history - [#1128](https://github.com/aws/amazon-q-developer-cli/pull/1128)"}, {"type": "added", "description": "A new command `/editor` in `q chat` for composing prompts using the configured editor - [#1035](https://github.com/aws/amazon-q-developer-cli/pull/1035)"}, {"type": "added", "description": "A new command `/issue` in `q chat` for creating new GitHub issues - [#990](https://github.com/aws/amazon-q-developer-cli/pull/990)"}, {"type": "added", "description": "A new tool `report_issue` in `q chat` for creating new GitHub issues - [#974](https://github.com/aws/amazon-q-developer-cli/pull/974)"}, {"type": "added", "description": "An improved context file display in `q chat` - [#983](https://github.com/aws/amazon-q-developer-cli/pull/983)"}, {"type": "added", "description": "Optional terminal notifications on completed responses - [#1135](https://github.com/aws/amazon-q-developer-cli/pull/1135)"}, {"type": "added", "description": "An option `--non-interactive` to invoke `q chat` headlessly - [#878](https://github.com/aws/amazon-q-developer-cli/pull/878)"}, {"type": "added", "description": "File name autocompletion in `q chat` - [#930](https://github.com/aws/amazon-q-developer-cli/pull/930)"}, {"type": "added", "description": "Improved `execute_bash` pipe safety checking in `q chat` - [#927](https://github.com/aws/amazon-q-developer-cli/pull/927)"}, {"type": "changed", "description": "Executing `q` now enters `q chat` instead of opening the dashboard - [#987](https://github.com/aws/amazon-q-developer-cli/pull/987)"}, {"type": "fixed", "description": "A missing Match criteria error in the ssh integration - [#1134](https://github.com/aws/amazon-q-developer-cli/pull/1134)"}, {"type": "fixed", "description": "Various issues in `q chat`"}]}, {"type": "release", "date": "2025-03-20", "version": "1.7.2", "title": "Version 1.7.2", "changes": [{"type": "added", "description": "Support for automatically adding files as context in `q chat` - [#834](https://github.com/aws/amazon-q-developer-cli/pull/834)"}, {"type": "added", "description": "A search ability when reading the filesystem in `q chat` - [#811](https://github.com/aws/amazon-q-developer-cli/pull/811)"}, {"type": "added", "description": "A larger maximum tool response size in `q chat` - [#850](https://github.com/aws/amazon-q-developer-cli/pull/850)"}, {"type": "fixed", "description": "Detecting libc on certain Linux distributions during install - [#851](https://github.com/aws/amazon-q-developer-cli/pull/851)"}]}, {"type": "release", "date": "2025-03-14", "version": "1.7.1", "title": "Version 1.7.1", "changes": [{"type": "added", "description": "An improved file diff in `q chat` - [#799](https://github.com/aws/amazon-q-developer-cli/pull/799)"}, {"type": "added", "description": "Multi-line input support in `q chat` - [#831](https://github.com/aws/amazon-q-developer-cli/pull/831)"}, {"type": "added", "description": "An append functionality with fs_write in `q chat` - [#764](https://github.com/aws/amazon-q-developer-cli/pull/764)"}, {"type": "added", "description": "Support for WindSurf Next - [#814](https://github.com/aws/amazon-q-developer-cli/pull/814)"}, {"type": "changed", "description": "`ctrl+c` so that it exits `q chat` on first attempt - [#830](https://github.com/aws/amazon-q-developer-cli/pull/830)"}, {"type": "fixed", "description": "Minor issues in `q chat`"}]}, {"type": "release", "date": "2025-03-05", "version": "1.7.0", "title": "Version 1.7.0", "changes": [{"type": "added", "description": "An enhanced `q chat` with agentic capabilities. See the [announcement post](https://aws.amazon.com/blogs/devops/introducing-the-enhanced-command-line-interface-in-amazon-q-developer/) for more information."}]}, {"type": "release", "date": "2025-02-20", "version": "1.6.3", "title": "Version 1.6.3", "changes": [{"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2025-02-19", "version": "1.6.2", "title": "Version 1.6.2", "changes": [{"type": "added", "description": "Support for Trae [#488](https://github.com/aws/amazon-q-developer-cli/pull/488)"}, {"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2025-02-03", "version": "1.6.1", "title": "Version 1.6.1", "changes": [{"type": "added", "description": "Support for Positron [#406](https://github.com/aws/amazon-q-developer-cli/pull/406)"}, {"type": "added", "description": "Self-updating for Linux AppImage builds [#410](https://github.com/aws/amazon-q-developer-cli/pull/410)"}, {"type": "fixed", "description": "Extra lines in inline recommendations [#446](https://github.com/aws/amazon-q-developer-cli/pull/446)"}, {"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2025-01-06", "version": "1.6.0", "title": "Version 1.6.0", "changes": [{"type": "added", "description": "Support for Ghostty [#335](https://github.com/aws/amazon-q-developer-cli/pull/335)"}, {"type": "added", "description": "Support for Guake [#260](https://github.com/aws/amazon-q-developer-cli/pull/260)"}, {"type": "fixed", "description": "Accept text through pipes in `q chat` [#259](https://github.com/aws/amazon-q-developer-cli/pull/259)"}]}, {"type": "release", "date": "2024-12-13", "version": "1.5.1", "title": "Version 1.5.1", "changes": [{"type": "added", "description": "Support for WindSurf [#125](https://github.com/aws/amazon-q-developer-cli/pull/125)"}, {"type": "fixed", "description": "Linux SSH installations unable to update [#235](https://github.com/aws/amazon-q-developer-cli/issues/235)"}, {"type": "fixed", "description": "`q translate` providing macOS specific responses on Linux"}]}, {"type": "release", "date": "2024-11-21", "version": "1.5.0", "title": "Version 1.5.0", "changes": [{"type": "added", "description": "Support for Linux desktop. See the [AWS documentation](https://docs.aws.amazon.com/amazonq/latest/qdeveloper-ug/command-line.html) for more information."}]}, {"type": "release", "date": "2024-11-15", "version": "1.4.6", "title": "Version 1.4.6", "changes": [{"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2024-09-26", "version": "1.4.5", "title": "Version 1.4.5", "changes": [{"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2024-09-16", "version": "1.4.4", "title": "Version 1.4.4", "changes": [{"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2024-09-13", "version": "1.4.3", "title": "Version 1.4.3", "changes": [{"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2024-09-10", "version": "1.4.2", "title": "Version 1.4.2", "changes": [{"type": "fixed", "description": "Linux install script error"}]}, {"type": "release", "date": "2024-09-09", "version": "1.4.1", "title": "Version 1.4.1", "changes": [{"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2024-08-27", "version": "1.4.0", "title": "Version 1.4.0", "changes": [{"type": "fixed", "description": "Frequent timeout errors in `q chat`"}, {"type": "fixed", "description": "Bash integrations messing up prompt in ZSH child processes"}, {"type": "fixed", "description": "Zsh integration errors when sourced via a non-interactive script"}]}, {"type": "release", "date": "2024-07-29", "version": "1.3.3", "title": "Version 1.3.3", "changes": [{"type": "fixed", "description": "Minor issues"}]}, {"type": "release", "date": "2024-07-10", "version": "1.3.2", "title": "Version 1.3.2", "changes": [{"type": "added", "description": "Setting to disable greeting in `q chat`, see [Chat](/chat) for the setting."}, {"type": "fixed", "description": "Using `tmux` is now supported in ssh."}, {"type": "fixed", "description": "Logging via the CLI would not show the \"Use with Pro license\" option."}]}, {"type": "release", "date": "2024-06-28", "version": "1.3.1", "title": "Version 1.3.1", "changes": [{"type": "added", "description": "Support for readline style input in `q chat`."}, {"type": "fixed", "description": "Pressing enter multiple times triggered `q translate` to immediately execute."}]}, {"type": "release", "date": "2024-06-24", "version": "1.3.0", "title": "Version 1.3.0", "changes": [{"type": "added", "description": "[PKCE-based authorization](https://aws.amazon.com/about-aws/whats-new/2024/05/aws-iam-identity-pkce-authorizations-aws-applications/) for login."}, {"type": "changed", "description": "Menubar icon now indicates when there are issues with authentication or the app is not setup."}, {"type": "changed", "description": "Added `quit` command to `q chat` ([#211](https://github.com/aws/q-command-line-discussions/discussions/211))."}]}, {"type": "release", "date": "2024-06-04", "version": "1.2.0", "title": "Version 1.2.0", "changes": [{"type": "added", "description": "AI-powered inline completions, see [Inline](/inline) for more info."}, {"type": "fixed", "description": "Login infinite redirect for first time users."}]}, {"type": "release", "date": "2024-05-20", "version": "1.1.0", "title": "Version 1.1.0", "changes": [{"type": "added", "description": "The \"What's New\" page with the latest release notes and announcements."}, {"type": "added", "description": "History loading for CLI Completions, press `ctrl+r` to switch to history mode. ([#57](https://github.com/aws/q-command-line-discussions/discussions/57))."}, {"type": "added", "description": "Support for [`generateSpecCacheKey`](https://github.com/withfig/autocomplete-tools/pull/105) for CLI Completions."}]}, {"type": "announcement", "date": "2024-04-30", "version": "1.0.0", "title": "CodeWhisperer is now Amazon Q", "description": "Amazon Q Developer is a generative artificial intelligence (AI) powered conversational assistant that can help you understand, build, extend, and operate AWS applications.", "link": "https://aws.amazon.com/blogs/aws/amazon-q-developer-now-generally-available-includes-new-capabilities-to-reimagine-developer-experience/"}]}