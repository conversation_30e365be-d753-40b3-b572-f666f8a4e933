{"$schema": "http://json-schema.org/draft-07/schema", "title": "Amazon Q for CLI Feed", "type": "object", "properties": {"entries": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["release", "announcement"], "description": "The type of entry"}, "date": {"type": "string", "description": "The date of the entry, must be valid RFC3339 date", "format": "date"}, "version": {"type": "string", "description": "The version of the entry, must be valid semver", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "title": {"type": "string", "description": "The title of the entry", "minLength": 1}, "description": {"type": "string", "description": "The description of the entry", "minLength": 1}, "link": {"type": "string", "description": "A url to more information about the entry", "format": "uri"}, "changes": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["added", "changed", "deprecated", "removed", "fixed", "security"]}, "description": {"type": "string", "description": "The description of the change", "minLength": 1}}, "required": ["type", "description"]}}, "hidden": {"type": "boolean", "description": "Whether to hide this entry from the UI"}}, "required": ["type", "title", "date", "version"]}}}, "required": ["entries"]}