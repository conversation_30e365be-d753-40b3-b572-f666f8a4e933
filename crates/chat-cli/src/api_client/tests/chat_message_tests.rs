use crate::api_client::model::{
    AssistantResponseMessage, ChatMessage, UserInputMessage
};

#[test]
fn test_chat_message_conversion() {
    // 创建一个UserInputMessage
    let user_message = UserInputMessage {
        content: "Hello, AI!".to_string(),
        user_input_message_context: None,
        user_intent: None,
        images: None,
        model_id: None,
    };
    
    // 将其转换为ChatMessage
    let chat_message = ChatMessage::UserInputMessage(user_message.clone());
    
    // 检查转换是否正确
    match chat_message {
        ChatMessage::UserInputMessage(msg) => {
            assert_eq!(msg.content, "Hello, AI!");
            assert_eq!(msg.user_input_message_context, None);
            assert_eq!(msg.user_intent, None);
            assert_eq!(msg.images, None);
            assert_eq!(msg.model_id, None);
        },
        _ => panic!("Expected UserInputMessage"),
    }
    
    // 创建一个AssistantResponseMessage
    let assistant_message = AssistantResponseMessage {
        message_id: Some("msg-123".to_string()),
        content: "I'm an AI assistant.".to_string(),
        tool_uses: None,
    };
    
    // 将其转换为ChatMessage
    let chat_message = ChatMessage::AssistantResponseMessage(assistant_message.clone());
    
    // 检查转换是否正确
    match chat_message {
        ChatMessage::AssistantResponseMessage(msg) => {
            assert_eq!(msg.message_id, Some("msg-123".to_string()));
            assert_eq!(msg.content, "I'm an AI assistant.");
            assert_eq!(msg.tool_uses, None);
        },
        _ => panic!("Expected AssistantResponseMessage"),
    }
}