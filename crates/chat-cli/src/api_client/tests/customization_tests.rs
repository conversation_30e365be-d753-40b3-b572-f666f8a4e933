use amzn_codewhisperer_client::types::Customization as CodewhispererCustomization;
use amzn_consolas_client::types::{CustomizationStatus, CustomizationSummary as ConsolasCustomization};
use aws_smithy_types::DateTime;

use crate::api_client::customization::Customization;

#[test]
fn test_customization_from_codewhisperer() {
    // 测试完整的转换
    let cw_customization = CodewhispererCustomization::builder()
        .arn("test-arn")
        .name("test-name")
        .description("test-description")
        .build()
        .unwrap();
    
    let custom: Customization = cw_customization.into();
    
    assert_eq!(custom.arn, "test-arn");
    assert_eq!(custom.name, Some("test-name".into()));
    assert_eq!(custom.description, Some("test-description".into()));
    
    // 测试部分字段为空的转换
    let cw_customization = CodewhispererCustomization::builder()
        .arn("test-arn")
        .build()
        .unwrap();
    
    let custom: Customization = cw_customization.into();
    
    assert_eq!(custom.arn, "test-arn");
    assert_eq!(custom.name, None);
    assert_eq!(custom.description, None);
}

#[test]
fn test_customization_to_codewhisperer() {
    // 测试完整的转换
    let custom = Customization {
        arn: "test-arn".into(),
        name: Some("test-name".into()),
        description: Some("test-description".into()),
    };
    
    let cw: CodewhispererCustomization = custom.into();
    
    assert_eq!(cw.arn, "test-arn");
    assert_eq!(cw.name, Some("test-name".into()));
    assert_eq!(cw.description, Some("test-description".into()));
    
    // 测试部分字段为空的转换
    let custom = Customization {
        arn: "test-arn".into(),
        name: None,
        description: None,
    };
    
    let cw: CodewhispererCustomization = custom.into();
    
    assert_eq!(cw.arn, "test-arn");
    assert_eq!(cw.name, None);
    assert_eq!(cw.description, None);
}

#[test]
fn test_customization_from_consolas() {
    let consolas_customization = ConsolasCustomization::builder()
        .arn("test-arn")
        .customization_name("test-name")
        .description("test-description")
        .status(CustomizationStatus::Activated)
        .updated_at(DateTime::from_secs(0))
        .build()
        .unwrap();
    
    let custom: Customization = consolas_customization.into();
    
    assert_eq!(custom.arn, "test-arn");
    assert_eq!(custom.name, Some("test-name".into()));
    assert_eq!(custom.description, Some("test-description".into()));
    
    // 测试没有描述的情况
    let consolas_customization = ConsolasCustomization::builder()
        .arn("test-arn")
        .customization_name("test-name")
        .status(CustomizationStatus::Activated)
        .updated_at(DateTime::from_secs(0))
        .build()
        .unwrap();
    
    let custom: Customization = consolas_customization.into();
    
    assert_eq!(custom.arn, "test-arn");
    assert_eq!(custom.name, Some("test-name".into()));
    assert_eq!(custom.description, None);
}