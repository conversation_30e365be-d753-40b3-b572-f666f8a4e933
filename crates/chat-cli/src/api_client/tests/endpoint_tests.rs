use aws_config::Region;
use serde_json::json;

use crate::api_client::endpoints::Endpoint;
use crate::database::Database;
use crate::database::settings::Setting;

#[tokio::test]
async fn test_default_endpoint() {
    let default_endpoint = Endpoint::DEFAULT_ENDPOINT;
    
    assert_eq!(default_endpoint.url(), "https://q.us-east-1.amazonaws.com");
    assert_eq!(default_endpoint.region().as_ref(), "us-east-1");
}

#[tokio::test]
async fn test_fra_endpoint() {
    let fra_endpoint = Endpoint::FRA_ENDPOINT;
    
    assert_eq!(fra_endpoint.url(), "https://q.eu-central-1.amazonaws.com/");
    assert_eq!(fra_endpoint.region().as_ref(), "eu-central-1");
}

#[tokio::test]
async fn test_custom_endpoint() {
    let custom = Endpoint {
        region: Region::new("us-west-2"),
        url: "https://example.com".into(),
    };
    
    assert_eq!(custom.url(), "https://example.com");
    assert_eq!(custom.region().as_ref(), "us-west-2");
}

#[tokio::test]
async fn test_configured_value_with_settings() {
    let mut database = Database::new().await.unwrap();
    
    // 设置自定义endpoint
    let custom_settings = json!({
        "endpoint": "https://custom-endpoint.com",
        "region": "us-west-2"
    });
    
    database.settings.set(Setting::ApiCodeWhispererService, custom_settings);
    
    let endpoint = Endpoint::configured_value(&database);
    assert_eq!(endpoint.url(), "https://custom-endpoint.com");
    assert_eq!(endpoint.region().as_ref(), "us-west-2");
}