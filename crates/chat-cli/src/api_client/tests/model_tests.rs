use crate::api_client::model::{LanguageName, ProgrammingLanguage};

#[test]
fn test_language_name_serialization() {
    // 测试序列化
    let lang = LanguageName::Python;
    let serialized = serde_json::to_string(&lang).unwrap();
    assert_eq!(serialized, r#""python""#);

    let lang = LanguageName::Rust;
    let serialized = serde_json::to_string(&lang).unwrap();
    assert_eq!(serialized, r#""rust""#);
}

#[test]
fn test_language_name_deserialization() {
    // 测试反序列化
    let lang: LanguageName = serde_json::from_str(r#""python""#).unwrap();
    assert!(matches!(lang, LanguageName::Python));

    let lang: LanguageName = serde_json::from_str(r#""rust""#).unwrap();
    assert!(matches!(lang, LanguageName::Rust));
}

#[test]
fn test_programming_language_serialization() {
    let pl = ProgrammingLanguage {
        language_name: LanguageName::Python,
    };
    
    let serialized = serde_json::to_string(&pl).unwrap();
    assert_eq!(serialized, r#"{"languageName":"python"}"#);
}

#[test]
fn test_programming_language_deserialization() {
    let json = r#"{"languageName":"rust"}"#;
    let pl: ProgrammingLanguage = serde_json::from_str(json).unwrap();
    assert!(matches!(pl.language_name, LanguageName::Rust));
}