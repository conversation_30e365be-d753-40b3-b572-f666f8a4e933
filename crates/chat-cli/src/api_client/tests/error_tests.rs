use crate::api_client::error::ApiClientError;
use crate::telemetry::ReasonCode;

#[test]
fn test_api_client_error_reason_codes() {
    // 测试配额错误的reason_code
    let quota_error = ApiClientError::QuotaBreach {
        message: "quota exceeded",
        status_code: Some(429),
    };
    assert_eq!(quota_error.reason_code(), "QuotaBreach");

    // 测试月度限制错误的reason_code
    let monthly_error = ApiClientError::MonthlyLimitReached { status_code: Some(429) };
    assert_eq!(monthly_error.reason_code(), "MonthlyLimitReached");

    // 测试上下文窗口溢出错误的reason_code
    let context_error = ApiClientError::ContextWindowOverflow { status_code: Some(400) };
    assert_eq!(context_error.reason_code(), "ContextWindowOverflow");

    // 测试模型过载错误的reason_code
    let model_error = ApiClientError::ModelOverloadedError {
        request_id: Some("test-id".to_string()),
        status_code: Some(503),
    };
    assert_eq!(model_error.reason_code(), "ModelOverloadedError");
}

#[test]
fn test_api_client_error_status_code() {
    // 测试配额错误的status_code
    let quota_error = ApiClientError::QuotaBreach {
        message: "quota exceeded",
        status_code: Some(429),
    };
    assert_eq!(quota_error.status_code(), Some(429));

    // 测试月度限制错误的status_code
    let monthly_error = ApiClientError::MonthlyLimitReached { status_code: Some(429) };
    assert_eq!(monthly_error.status_code(), Some(429));

    // 测试上下文窗口溢出错误的status_code
    let context_error = ApiClientError::ContextWindowOverflow { status_code: Some(400) };
    assert_eq!(context_error.status_code(), Some(400));

    // 测试模型过载错误的status_code
    let model_error = ApiClientError::ModelOverloadedError {
        request_id: Some("test-id".to_string()),
        status_code: Some(503),
    };
    assert_eq!(model_error.status_code(), Some(503));
}