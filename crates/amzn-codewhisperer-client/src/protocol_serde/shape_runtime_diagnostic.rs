// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_runtime_diagnostic(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::RuntimeDiagnostic,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("source").string(input.source.as_str());
    }
    {
        object.key("severity").string(input.severity.as_str());
    }
    {
        object.key("message").string(input.message.as_str());
    }
    Ok(())
}
