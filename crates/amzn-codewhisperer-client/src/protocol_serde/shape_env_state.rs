// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_env_state(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::EnvState,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.operating_system {
        object.key("operatingSystem").string(var_1.as_str());
    }
    if let Some(var_2) = &input.current_working_directory {
        object.key("currentWorkingDirectory").string(var_2.as_str());
    }
    if let Some(var_3) = &input.environment_variables {
        let mut array_4 = object.key("environmentVariables").start_array();
        for item_5 in var_3 {
            {
                #[allow(unused_mut)]
                let mut object_6 = array_4.value().start_object();
                crate::protocol_serde::shape_environment_variable::ser_environment_variable(&mut object_6, item_5)?;
                object_6.finish();
            }
        }
        array_4.finish();
    }
    if let Some(var_7) = &input.timezone_offset {
        object.key("timezoneOffset").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_7).into()),
        );
    }
    Ok(())
}
