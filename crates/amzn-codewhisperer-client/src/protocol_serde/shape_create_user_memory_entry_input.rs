// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_create_user_memory_entry_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::create_user_memory_entry::CreateUserMemoryEntryInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.memory_entry_string {
        object.key("memoryEntryString").string(var_1.as_str());
    }
    if let Some(var_2) = &input.origin {
        object.key("origin").string(var_2.as_str());
    }
    if let Some(var_3) = &input.profile_arn {
        object.key("profileArn").string(var_3.as_str());
    }
    if let Some(var_4) = &input.client_token {
        object.key("clientToken").string(var_4.as_str());
    }
    Ok(())
}
