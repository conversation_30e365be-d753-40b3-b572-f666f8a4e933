// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_transformation_spec(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::TransformationSpec,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.transformation_type {
        object.key("transformationType").string(var_1.as_str());
    }
    if let Some(var_2) = &input.source {
        #[allow(unused_mut)]
        let mut object_3 = object.key("source").start_object();
        crate::protocol_serde::shape_transformation_project_state::ser_transformation_project_state(
            &mut object_3,
            var_2,
        )?;
        object_3.finish();
    }
    if let Some(var_4) = &input.target {
        #[allow(unused_mut)]
        let mut object_5 = object.key("target").start_object();
        crate::protocol_serde::shape_transformation_project_state::ser_transformation_project_state(
            &mut object_5,
            var_4,
        )?;
        object_5.finish();
    }
    Ok(())
}

pub(crate) fn de_transformation_spec<'a, I>(
    tokens: &mut ::std::iter::Peekable<I>,
) -> ::std::result::Result<
    Option<crate::types::TransformationSpec>,
    ::aws_smithy_json::deserialize::error::DeserializeError,
>
where
    I: Iterator<
        Item = Result<
            ::aws_smithy_json::deserialize::Token<'a>,
            ::aws_smithy_json::deserialize::error::DeserializeError,
        >,
    >,
{
    match tokens.next().transpose()? {
        Some(::aws_smithy_json::deserialize::Token::ValueNull { .. }) => Ok(None),
        Some(::aws_smithy_json::deserialize::Token::StartObject { .. }) => {
            #[allow(unused_mut)]
            let mut builder = crate::types::builders::TransformationSpecBuilder::default();
            loop {
                match tokens.next().transpose()? {
                    Some(::aws_smithy_json::deserialize::Token::EndObject { .. }) => break,
                    Some(::aws_smithy_json::deserialize::Token::ObjectKey { key, .. }) => {
                        match key.to_unescaped()?.as_ref() {
                            "transformationType" => {
                                builder = builder.set_transformation_type(
                                    ::aws_smithy_json::deserialize::token::expect_string_or_null(tokens.next())?
                                        .map(|s| {
                                            s.to_unescaped()
                                                .map(|u| crate::types::TransformationType::from(u.as_ref()))
                                        })
                                        .transpose()?,
                                );
                            },
                            "source" => {
                                builder = builder
                                .set_source(crate::protocol_serde::shape_transformation_project_state::de_transformation_project_state(tokens)?);
                            },
                            "target" => {
                                builder = builder
                                .set_target(crate::protocol_serde::shape_transformation_project_state::de_transformation_project_state(tokens)?);
                            },
                            _ => ::aws_smithy_json::deserialize::token::skip_value(tokens)?,
                        }
                    },
                    other => {
                        return Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
                            format!("expected object key or end object, found: {:?}", other),
                        ));
                    },
                }
            }
            Ok(Some(builder.build()))
        },
        _ => Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
            "expected start object or null",
        )),
    }
}
