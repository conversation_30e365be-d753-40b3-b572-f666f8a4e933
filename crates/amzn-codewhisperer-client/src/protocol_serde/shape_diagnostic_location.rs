// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_diagnostic_location(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::DiagnosticLocation,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("uri").string(input.uri.as_str());
    }
    {
        #[allow(unused_mut)]
        let mut object_1 = object.key("range").start_object();
        crate::protocol_serde::shape_range::ser_range(&mut object_1, &input.range)?;
        object_1.finish();
    }
    Ok(())
}
