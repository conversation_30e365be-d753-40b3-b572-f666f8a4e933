// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_start_code_analysis_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::start_code_analysis::StartCodeAnalysisInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.artifacts {
        #[allow(unused_mut)]
        let mut object_2 = object.key("artifacts").start_object();
        for (key_3, value_4) in var_1 {
            {
                object_2.key(key_3.as_str()).string(value_4.as_str());
            }
        }
        object_2.finish();
    }
    if let Some(var_5) = &input.programming_language {
        #[allow(unused_mut)]
        let mut object_6 = object.key("programmingLanguage").start_object();
        crate::protocol_serde::shape_programming_language::ser_programming_language(&mut object_6, var_5)?;
        object_6.finish();
    }
    if let Some(var_7) = &input.client_token {
        object.key("clientToken").string(var_7.as_str());
    }
    if let Some(var_8) = &input.scope {
        object.key("scope").string(var_8.as_str());
    }
    if let Some(var_9) = &input.code_scan_name {
        object.key("codeScanName").string(var_9.as_str());
    }
    if let Some(var_10) = &input.code_diff_metadata {
        #[allow(unused_mut)]
        let mut object_11 = object.key("codeDiffMetadata").start_object();
        crate::protocol_serde::shape_code_diff_metadata::ser_code_diff_metadata(&mut object_11, var_10)?;
        object_11.finish();
    }
    if let Some(var_12) = &input.profile_arn {
        object.key("profileArn").string(var_12.as_str());
    }
    Ok(())
}
