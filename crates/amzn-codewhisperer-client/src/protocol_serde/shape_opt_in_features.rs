// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub(crate) fn de_opt_in_features<'a, I>(
    tokens: &mut ::std::iter::Peekable<I>,
) -> ::std::result::Result<Option<crate::types::OptInFeatures>, ::aws_smithy_json::deserialize::error::DeserializeError>
where
    I: Iterator<
        Item = Result<
            ::aws_smithy_json::deserialize::Token<'a>,
            ::aws_smithy_json::deserialize::error::DeserializeError,
        >,
    >,
{
    match tokens.next().transpose()? {
        Some(::aws_smithy_json::deserialize::Token::ValueNull { .. }) => Ok(None),
        Some(::aws_smithy_json::deserialize::Token::StartObject { .. }) => {
            #[allow(unused_mut)]
            let mut builder = crate::types::builders::OptInFeaturesBuilder::default();
            loop {
                match tokens.next().transpose()? {
                    Some(::aws_smithy_json::deserialize::Token::EndObject { .. }) => break,
                    Some(::aws_smithy_json::deserialize::Token::ObjectKey { key, .. }) => match key
                        .to_unescaped()?
                        .as_ref()
                    {
                        "promptLogging" => {
                            builder = builder.set_prompt_logging(
                                crate::protocol_serde::shape_prompt_logging::de_prompt_logging(tokens)?,
                            );
                        },
                        "byUserAnalytics" => {
                            builder = builder.set_by_user_analytics(
                                crate::protocol_serde::shape_by_user_analytics::de_by_user_analytics(tokens)?,
                            );
                        },
                        "dashboardAnalytics" => {
                            builder = builder.set_dashboard_analytics(
                                crate::protocol_serde::shape_dashboard_analytics::de_dashboard_analytics(tokens)?,
                            );
                        },
                        "notifications" => {
                            builder = builder.set_notifications(
                                crate::protocol_serde::shape_notifications::de_notifications(tokens)?,
                            );
                        },
                        "workspaceContext" => {
                            builder = builder.set_workspace_context(
                                crate::protocol_serde::shape_workspace_context::de_workspace_context(tokens)?,
                            );
                        },
                        "overageConfiguration" => {
                            builder = builder.set_overage_configuration(
                                crate::protocol_serde::shape_overage_configuration::de_overage_configuration(tokens)?,
                            );
                        },
                        "mcpConfiguration" => {
                            builder = builder.set_mcp_configuration(
                                crate::protocol_serde::shape_mcp_configuration::de_mcp_configuration(tokens)?,
                            );
                        },
                        _ => ::aws_smithy_json::deserialize::token::skip_value(tokens)?,
                    },
                    other => {
                        return Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
                            format!("expected object key or end object, found: {:?}", other),
                        ));
                    },
                }
            }
            Ok(Some(builder.build()))
        },
        _ => Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
            "expected start object or null",
        )),
    }
}
