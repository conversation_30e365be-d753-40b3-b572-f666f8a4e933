// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_code_scan_remediations_event(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::CodeScanRemediationsEvent,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.programming_language {
        #[allow(unused_mut)]
        let mut object_2 = object.key("programmingLanguage").start_object();
        crate::protocol_serde::shape_programming_language::ser_programming_language(&mut object_2, var_1)?;
        object_2.finish();
    }
    if let Some(var_3) = &input.code_scan_remediations_event_type {
        object.key("CodeScanRemediationsEventType").string(var_3.as_str());
    }
    if let Some(var_4) = &input.timestamp {
        object
            .key("timestamp")
            .date_time(var_4, ::aws_smithy_types::date_time::Format::EpochSeconds)?;
    }
    if let Some(var_5) = &input.detector_id {
        object.key("detectorId").string(var_5.as_str());
    }
    if let Some(var_6) = &input.finding_id {
        object.key("findingId").string(var_6.as_str());
    }
    if let Some(var_7) = &input.rule_id {
        object.key("ruleId").string(var_7.as_str());
    }
    if let Some(var_8) = &input.component {
        object.key("component").string(var_8.as_str());
    }
    if let Some(var_9) = &input.reason {
        object.key("reason").string(var_9.as_str());
    }
    if let Some(var_10) = &input.result {
        object.key("result").string(var_10.as_str());
    }
    if let Some(var_11) = &input.includes_fix {
        object.key("includesFix").boolean(*var_11);
    }
    Ok(())
}
