// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_inline_chat_event(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::InlineChatEvent,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("requestId").string(input.request_id.as_str());
    }
    {
        object
            .key("timestamp")
            .date_time(&input.timestamp, ::aws_smithy_types::date_time::Format::EpochSeconds)?;
    }
    if input.input_length != 0 {
        object.key("inputLength").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.input_length).into()),
        );
    }
    if input.num_selected_lines != 0 {
        object.key("numSelectedLines").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.num_selected_lines).into()),
        );
    }
    if input.num_suggestion_add_chars != 0 {
        object.key("numSuggestionAddChars").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.num_suggestion_add_chars).into()),
        );
    }
    if input.num_suggestion_add_lines != 0 {
        object.key("numSuggestionAddLines").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.num_suggestion_add_lines).into()),
        );
    }
    if input.num_suggestion_del_chars != 0 {
        object.key("numSuggestionDelChars").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.num_suggestion_del_chars).into()),
        );
    }
    if input.num_suggestion_del_lines != 0 {
        object.key("numSuggestionDelLines").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.num_suggestion_del_lines).into()),
        );
    }
    if let Some(var_1) = &input.code_intent {
        object.key("codeIntent").boolean(*var_1);
    }
    if let Some(var_2) = &input.user_decision {
        object.key("userDecision").string(var_2.as_str());
    }
    if let Some(var_3) = &input.response_start_latency {
        object.key("responseStartLatency").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::Float((*var_3).into()),
        );
    }
    if let Some(var_4) = &input.response_end_latency {
        object.key("responseEndLatency").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::Float((*var_4).into()),
        );
    }
    if let Some(var_5) = &input.programming_language {
        #[allow(unused_mut)]
        let mut object_6 = object.key("programmingLanguage").start_object();
        crate::protocol_serde::shape_programming_language::ser_programming_language(&mut object_6, var_5)?;
        object_6.finish();
    }
    Ok(())
}
