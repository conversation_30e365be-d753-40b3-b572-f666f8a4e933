// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_user_trigger_decision_event(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::UserTriggerDecisionEvent,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("sessionId").string(input.session_id.as_str());
    }
    {
        object.key("requestId").string(input.request_id.as_str());
    }
    if let Some(var_1) = &input.customization_arn {
        object.key("customizationArn").string(var_1.as_str());
    }
    {
        #[allow(unused_mut)]
        let mut object_2 = object.key("programmingLanguage").start_object();
        crate::protocol_serde::shape_programming_language::ser_programming_language(
            &mut object_2,
            &input.programming_language,
        )?;
        object_2.finish();
    }
    {
        object.key("completionType").string(input.completion_type.as_str());
    }
    {
        object.key("suggestionState").string(input.suggestion_state.as_str());
    }
    {
        object.key("recommendationLatencyMilliseconds").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::Float((input.recommendation_latency_milliseconds).into()),
        );
    }
    {
        object
            .key("timestamp")
            .date_time(&input.timestamp, ::aws_smithy_types::date_time::Format::EpochSeconds)?;
    }
    if let Some(var_3) = &input.trigger_to_response_latency_milliseconds {
        object.key("triggerToResponseLatencyMilliseconds").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::Float((*var_3).into()),
        );
    }
    if input.suggestion_reference_count != 0 {
        object.key("suggestionReferenceCount").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.suggestion_reference_count).into()),
        );
    }
    if input.generated_line != 0 {
        object.key("generatedLine").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.generated_line).into()),
        );
    }
    if input.number_of_recommendations != 0 {
        object.key("numberOfRecommendations").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.number_of_recommendations).into()),
        );
    }
    if let Some(var_4) = &input.perceived_latency_milliseconds {
        object.key("perceivedLatencyMilliseconds").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::Float((*var_4).into()),
        );
    }
    if input.accepted_character_count != 0 {
        object.key("acceptedCharacterCount").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.accepted_character_count).into()),
        );
    }
    if let Some(var_5) = &input.added_ide_diagnostics {
        let mut array_6 = object.key("addedIdeDiagnostics").start_array();
        for item_7 in var_5 {
            {
                #[allow(unused_mut)]
                let mut object_8 = array_6.value().start_object();
                crate::protocol_serde::shape_ide_diagnostic::ser_ide_diagnostic(&mut object_8, item_7)?;
                object_8.finish();
            }
        }
        array_6.finish();
    }
    if let Some(var_9) = &input.removed_ide_diagnostics {
        let mut array_10 = object.key("removedIdeDiagnostics").start_array();
        for item_11 in var_9 {
            {
                #[allow(unused_mut)]
                let mut object_12 = array_10.value().start_object();
                crate::protocol_serde::shape_ide_diagnostic::ser_ide_diagnostic(&mut object_12, item_11)?;
                object_12.finish();
            }
        }
        array_10.finish();
    }
    if input.added_character_count != 0 {
        object.key("addedCharacterCount").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.added_character_count).into()),
        );
    }
    if input.deleted_character_count != 0 {
        object.key("deletedCharacterCount").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.deleted_character_count).into()),
        );
    }
    if input.streak_length != 0 {
        object.key("streakLength").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.streak_length).into()),
        );
    }
    Ok(())
}
