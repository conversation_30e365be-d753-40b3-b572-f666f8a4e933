// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_workspace_context_upload_context(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::WorkspaceContextUploadContext,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("workspaceId").string(input.workspace_id.as_str());
    }
    {
        object.key("relativePath").string(input.relative_path.as_str());
    }
    {
        #[allow(unused_mut)]
        let mut object_1 = object.key("programmingLanguage").start_object();
        crate::protocol_serde::shape_programming_language::ser_programming_language(
            &mut object_1,
            &input.programming_language,
        )?;
        object_1.finish();
    }
    Ok(())
}
