// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_transformation_upload_context(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::TransformationUploadContext,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("jobId").string(input.job_id.as_str());
    }
    {
        object
            .key("uploadArtifactType")
            .string(input.upload_artifact_type.as_str());
    }
    Ok(())
}
