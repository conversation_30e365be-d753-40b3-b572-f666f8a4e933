// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_text_document_diagnostic(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::TextDocumentDiagnostic,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        #[allow(unused_mut)]
        let mut object_1 = object.key("document").start_object();
        crate::protocol_serde::shape_text_document::ser_text_document(&mut object_1, &input.document)?;
        object_1.finish();
    }
    {
        #[allow(unused_mut)]
        let mut object_2 = object.key("range").start_object();
        crate::protocol_serde::shape_range::ser_range(&mut object_2, &input.range)?;
        object_2.finish();
    }
    {
        object.key("source").string(input.source.as_str());
    }
    {
        object.key("severity").string(input.severity.as_str());
    }
    {
        object.key("message").string(input.message.as_str());
    }
    if let Some(var_3) = &input.code {
        object.key("code").string(var_3.as_str());
    }
    if let Some(var_4) = &input.code_description {
        #[allow(unused_mut)]
        let mut object_5 = object.key("codeDescription").start_object();
        crate::protocol_serde::shape_code_description::ser_code_description(&mut object_5, var_4)?;
        object_5.finish();
    }
    if let Some(var_6) = &input.tags {
        let mut array_7 = object.key("tags").start_array();
        for item_8 in var_6 {
            {
                array_7.value().string(item_8.as_str());
            }
        }
        array_7.finish();
    }
    if let Some(var_9) = &input.related_information {
        let mut array_10 = object.key("relatedInformation").start_array();
        for item_11 in var_9 {
            {
                #[allow(unused_mut)]
                let mut object_12 = array_10.value().start_object();
                crate::protocol_serde::shape_diagnostic_related_information::ser_diagnostic_related_information(
                    &mut object_12,
                    item_11,
                )?;
                object_12.finish();
            }
        }
        array_10.finish();
    }
    if let Some(var_13) = &input.data {
        object.key("data").string(var_13.as_str());
    }
    Ok(())
}
