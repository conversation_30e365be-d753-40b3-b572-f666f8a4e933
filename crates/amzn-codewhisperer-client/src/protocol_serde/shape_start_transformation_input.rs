// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_start_transformation_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::start_transformation::StartTransformationInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.workspace_state {
        #[allow(unused_mut)]
        let mut object_2 = object.key("workspaceState").start_object();
        crate::protocol_serde::shape_workspace_state::ser_workspace_state(&mut object_2, var_1)?;
        object_2.finish();
    }
    if let Some(var_3) = &input.transformation_spec {
        #[allow(unused_mut)]
        let mut object_4 = object.key("transformationSpec").start_object();
        crate::protocol_serde::shape_transformation_spec::ser_transformation_spec(&mut object_4, var_3)?;
        object_4.finish();
    }
    if let Some(var_5) = &input.profile_arn {
        object.key("profileArn").string(var_5.as_str());
    }
    Ok(())
}
