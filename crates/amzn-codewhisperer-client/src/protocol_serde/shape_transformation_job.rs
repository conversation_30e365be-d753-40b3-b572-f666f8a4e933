// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub(crate) fn de_transformation_job<'a, I>(
    tokens: &mut ::std::iter::Peekable<I>,
) -> ::std::result::Result<
    Option<crate::types::TransformationJob>,
    ::aws_smithy_json::deserialize::error::DeserializeError,
>
where
    I: Iterator<
        Item = Result<
            ::aws_smithy_json::deserialize::Token<'a>,
            ::aws_smithy_json::deserialize::error::DeserializeError,
        >,
    >,
{
    match tokens.next().transpose()? {
        Some(::aws_smithy_json::deserialize::Token::ValueNull { .. }) => Ok(None),
        Some(::aws_smithy_json::deserialize::Token::StartObject { .. }) => {
            #[allow(unused_mut)]
            let mut builder = crate::types::builders::TransformationJobBuilder::default();
            loop {
                match tokens.next().transpose()? {
                    Some(::aws_smithy_json::deserialize::Token::EndObject { .. }) => break,
                    Some(::aws_smithy_json::deserialize::Token::ObjectKey { key, .. }) => {
                        match key.to_unescaped()?.as_ref() {
                            "jobId" => {
                                builder = builder.set_job_id(
                                    ::aws_smithy_json::deserialize::token::expect_string_or_null(tokens.next())?
                                        .map(|s| s.to_unescaped().map(|u| u.into_owned()))
                                        .transpose()?,
                                );
                            },
                            "transformationSpec" => {
                                builder = builder.set_transformation_spec(
                                    crate::protocol_serde::shape_transformation_spec::de_transformation_spec(tokens)?,
                                );
                            },
                            "status" => {
                                builder = builder.set_status(
                                    ::aws_smithy_json::deserialize::token::expect_string_or_null(tokens.next())?
                                        .map(|s| {
                                            s.to_unescaped()
                                                .map(|u| crate::types::TransformationStatus::from(u.as_ref()))
                                        })
                                        .transpose()?,
                                );
                            },
                            "reason" => {
                                builder = builder.set_reason(
                                    ::aws_smithy_json::deserialize::token::expect_string_or_null(tokens.next())?
                                        .map(|s| s.to_unescaped().map(|u| u.into_owned()))
                                        .transpose()?,
                                );
                            },
                            "creationTime" => {
                                builder = builder.set_creation_time(
                                    ::aws_smithy_json::deserialize::token::expect_timestamp_or_null(
                                        tokens.next(),
                                        ::aws_smithy_types::date_time::Format::EpochSeconds,
                                    )?,
                                );
                            },
                            "startExecutionTime" => {
                                builder = builder.set_start_execution_time(
                                    ::aws_smithy_json::deserialize::token::expect_timestamp_or_null(
                                        tokens.next(),
                                        ::aws_smithy_types::date_time::Format::EpochSeconds,
                                    )?,
                                );
                            },
                            "endExecutionTime" => {
                                builder = builder.set_end_execution_time(
                                    ::aws_smithy_json::deserialize::token::expect_timestamp_or_null(
                                        tokens.next(),
                                        ::aws_smithy_types::date_time::Format::EpochSeconds,
                                    )?,
                                );
                            },
                            _ => ::aws_smithy_json::deserialize::token::skip_value(tokens)?,
                        }
                    },
                    other => {
                        return Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
                            format!("expected object key or end object, found: {:?}", other),
                        ));
                    },
                }
            }
            Ok(Some(builder.build()))
        },
        _ => Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
            "expected start object or null",
        )),
    }
}
