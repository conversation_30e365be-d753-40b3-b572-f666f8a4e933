// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_tool_input_schema(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::ToolInputSchema,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.json {
        object.key("json").document(var_1);
    }
    Ok(())
}
