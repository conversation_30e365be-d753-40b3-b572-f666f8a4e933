// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_tool_specification(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::ToolSpecification,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        #[allow(unused_mut)]
        let mut object_1 = object.key("inputSchema").start_object();
        crate::protocol_serde::shape_tool_input_schema::ser_tool_input_schema(&mut object_1, &input.input_schema)?;
        object_1.finish();
    }
    {
        object.key("name").string(input.name.as_str());
    }
    if let Some(var_2) = &input.description {
        object.key("description").string(var_2.as_str());
    }
    Ok(())
}
