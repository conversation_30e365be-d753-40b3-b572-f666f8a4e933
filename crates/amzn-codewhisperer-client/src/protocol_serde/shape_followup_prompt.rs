// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_followup_prompt(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::FollowupPrompt,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("content").string(input.content.as_str());
    }
    if let Some(var_1) = &input.user_intent {
        object.key("userIntent").string(var_1.as_str());
    }
    Ok(())
}
