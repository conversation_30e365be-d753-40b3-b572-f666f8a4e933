// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_send_telemetry_event_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::send_telemetry_event::SendTelemetryEventInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.client_token {
        object.key("clientToken").string(var_1.as_str());
    }
    if let Some(var_2) = &input.telemetry_event {
        #[allow(unused_mut)]
        let mut object_3 = object.key("telemetryEvent").start_object();
        crate::protocol_serde::shape_telemetry_event::ser_telemetry_event(&mut object_3, var_2)?;
        object_3.finish();
    }
    if let Some(var_4) = &input.opt_out_preference {
        object.key("optOutPreference").string(var_4.as_str());
    }
    if let Some(var_5) = &input.user_context {
        #[allow(unused_mut)]
        let mut object_6 = object.key("userContext").start_object();
        crate::protocol_serde::shape_user_context::ser_user_context(&mut object_6, var_5)?;
        object_6.finish();
    }
    if let Some(var_7) = &input.profile_arn {
        object.key("profileArn").string(var_7.as_str());
    }
    if let Some(var_8) = &input.model_id {
        object.key("modelId").string(var_8.as_str());
    }
    Ok(())
}
