// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_code_scan_failed_event(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::CodeScanFailedEvent,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        #[allow(unused_mut)]
        let mut object_1 = object.key("programmingLanguage").start_object();
        crate::protocol_serde::shape_programming_language::ser_programming_language(
            &mut object_1,
            &input.programming_language,
        )?;
        object_1.finish();
    }
    {
        object.key("codeScanJobId").string(input.code_scan_job_id.as_str());
    }
    {
        object
            .key("timestamp")
            .date_time(&input.timestamp, ::aws_smithy_types::date_time::Format::EpochSeconds)?;
    }
    if let Some(var_2) = &input.code_analysis_scope {
        object.key("codeAnalysisScope").string(var_2.as_str());
    }
    Ok(())
}
