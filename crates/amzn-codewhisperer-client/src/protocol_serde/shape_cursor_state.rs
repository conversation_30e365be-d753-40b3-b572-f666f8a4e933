// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_cursor_state(
    object_4: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::CursorState,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    match input {
        crate::types::CursorState::Position(inner) => {
            #[allow(unused_mut)]
            let mut object_1 = object_4.key("position").start_object();
            crate::protocol_serde::shape_position::ser_position(&mut object_1, inner)?;
            object_1.finish();
        },
        crate::types::CursorState::Range(inner) => {
            #[allow(unused_mut)]
            let mut object_2 = object_4.key("range").start_object();
            crate::protocol_serde::shape_range::ser_range(&mut object_2, inner)?;
            object_2.finish();
        },
        crate::types::CursorState::Unknown => {
            return Err(::aws_smithy_types::error::operation::SerializationError::unknown_variant("CursorState"));
        },
    }
    Ok(())
}
