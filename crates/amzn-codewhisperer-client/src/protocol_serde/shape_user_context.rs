// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_user_context(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::UserContext,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("ideCategory").string(input.ide_category.as_str());
    }
    {
        object.key("operatingSystem").string(input.operating_system.as_str());
    }
    {
        object.key("product").string(input.product.as_str());
    }
    if let Some(var_1) = &input.client_id {
        object.key("clientId").string(var_1.as_str());
    }
    if let Some(var_2) = &input.ide_version {
        object.key("ideVersion").string(var_2.as_str());
    }
    Ok(())
}
