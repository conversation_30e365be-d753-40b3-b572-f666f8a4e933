// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_transform_event(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::TransformEvent,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("jobId").string(input.job_id.as_str());
    }
    if let Some(var_1) = &input.timestamp {
        object
            .key("timestamp")
            .date_time(var_1, ::aws_smithy_types::date_time::Format::EpochSeconds)?;
    }
    if let Some(var_2) = &input.ide_category {
        object.key("ideCategory").string(var_2.as_str());
    }
    if let Some(var_3) = &input.programming_language {
        #[allow(unused_mut)]
        let mut object_4 = object.key("programmingLanguage").start_object();
        crate::protocol_serde::shape_programming_language::ser_programming_language(&mut object_4, var_3)?;
        object_4.finish();
    }
    if let Some(var_5) = &input.lines_of_code_changed {
        object.key("linesOfCodeChanged").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_5).into()),
        );
    }
    if let Some(var_6) = &input.chars_of_code_changed {
        object.key("charsOfCodeChanged").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_6).into()),
        );
    }
    if let Some(var_7) = &input.lines_of_code_submitted {
        object.key("linesOfCodeSubmitted").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_7).into()),
        );
    }
    Ok(())
}
