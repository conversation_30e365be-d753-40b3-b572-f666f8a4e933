// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_chat_interact_with_message_event(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::ChatInteractWithMessageEvent,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("conversationId").string(input.conversation_id.as_str());
    }
    {
        object.key("messageId").string(input.message_id.as_str());
    }
    if let Some(var_1) = &input.customization_arn {
        object.key("customizationArn").string(var_1.as_str());
    }
    if let Some(var_2) = &input.interaction_type {
        object.key("interactionType").string(var_2.as_str());
    }
    if let Some(var_3) = &input.interaction_target {
        object.key("interactionTarget").string(var_3.as_str());
    }
    if let Some(var_4) = &input.accepted_character_count {
        object.key("acceptedCharacterCount").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_4).into()),
        );
    }
    if let Some(var_5) = &input.accepted_line_count {
        object.key("acceptedLineCount").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_5).into()),
        );
    }
    if let Some(var_6) = &input.accepted_snippet_has_reference {
        object.key("acceptedSnippetHasReference").boolean(*var_6);
    }
    if let Some(var_7) = &input.has_project_level_context {
        object.key("hasProjectLevelContext").boolean(*var_7);
    }
    if let Some(var_8) = &input.user_intent {
        object.key("userIntent").string(var_8.as_str());
    }
    if let Some(var_9) = &input.added_ide_diagnostics {
        let mut array_10 = object.key("addedIdeDiagnostics").start_array();
        for item_11 in var_9 {
            {
                #[allow(unused_mut)]
                let mut object_12 = array_10.value().start_object();
                crate::protocol_serde::shape_ide_diagnostic::ser_ide_diagnostic(&mut object_12, item_11)?;
                object_12.finish();
            }
        }
        array_10.finish();
    }
    if let Some(var_13) = &input.removed_ide_diagnostics {
        let mut array_14 = object.key("removedIdeDiagnostics").start_array();
        for item_15 in var_13 {
            {
                #[allow(unused_mut)]
                let mut object_16 = array_14.value().start_object();
                crate::protocol_serde::shape_ide_diagnostic::ser_ide_diagnostic(&mut object_16, item_15)?;
                object_16.finish();
            }
        }
        array_14.finish();
    }
    Ok(())
}
