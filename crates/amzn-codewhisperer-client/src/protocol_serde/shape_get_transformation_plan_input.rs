// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_get_transformation_plan_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::get_transformation_plan::GetTransformationPlanInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.transformation_job_id {
        object.key("transformationJobId").string(var_1.as_str());
    }
    if let Some(var_2) = &input.profile_arn {
        object.key("profileArn").string(var_2.as_str());
    }
    Ok(())
}
