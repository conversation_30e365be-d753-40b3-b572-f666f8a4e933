// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_push_telemetry_event_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::push_telemetry_event::PushTelemetryEventInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.client_token {
        object.key("clientToken").string(var_1.as_str());
    }
    if let Some(var_2) = &input.event_type {
        object.key("eventType").string(var_2.as_str());
    }
    if let Some(var_3) = &input.event {
        object.key("event").document(var_3);
    }
    Ok(())
}
