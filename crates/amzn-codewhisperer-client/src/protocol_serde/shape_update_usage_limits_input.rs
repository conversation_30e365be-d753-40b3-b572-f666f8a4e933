// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_update_usage_limits_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::update_usage_limits::UpdateUsageLimitsInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.account_id {
        object.key("accountId").string(var_1.as_str());
    }
    if let Some(var_2) = &input.accountless_user_id {
        object.key("accountlessUserId").string(var_2.as_str());
    }
    if let Some(var_3) = &input.directory_id {
        object.key("directoryId").string(var_3.as_str());
    }
    if let Some(var_4) = &input.feature_type {
        object.key("featureType").string(var_4.as_str());
    }
    if let Some(var_5) = &input.requested_limit {
        object.key("requestedLimit").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_5).into()),
        );
    }
    if let Some(var_6) = &input.justification {
        object.key("justification").string(var_6.as_str());
    }
    Ok(())
}
