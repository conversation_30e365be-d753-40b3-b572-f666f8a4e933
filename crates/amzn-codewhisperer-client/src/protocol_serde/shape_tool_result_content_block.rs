// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_tool_result_content_block(
    object_3: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::ToolResultContentBlock,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    match input {
        crate::types::ToolResultContentBlock::Text(inner) => {
            object_3.key("text").string(inner.as_str());
        },
        crate::types::ToolResultContentBlock::Json(inner) => {
            object_3.key("json").document(inner);
        },
        crate::types::ToolResultContentBlock::Unknown => {
            return Err(
                ::aws_smithy_types::error::operation::SerializationError::unknown_variant("ToolResultContentBlock"),
            );
        },
    }
    Ok(())
}
