// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_image_source(
    object_1: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::ImageSource,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    match input {
        crate::types::ImageSource::Bytes(inner) => {
            object_1
                .key("bytes")
                .string_unchecked(&::aws_smithy_types::base64::encode(inner));
        },
        crate::types::ImageSource::Unknown => {
            return Err(::aws_smithy_types::error::operation::SerializationError::unknown_variant("ImageSource"));
        },
    }
    Ok(())
}
