// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_create_subscription_token_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::create_subscription_token::CreateSubscriptionTokenInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.client_token {
        object.key("clientToken").string(var_1.as_str());
    }
    if let Some(var_2) = &input.status_only {
        object.key("statusOnly").boolean(*var_2);
    }
    if let Some(var_3) = &input.provider {
        object.key("provider").string(var_3.as_str());
    }
    if let Some(var_4) = &input.subscription_type {
        object.key("subscriptionType").string(var_4.as_str());
    }
    Ok(())
}
