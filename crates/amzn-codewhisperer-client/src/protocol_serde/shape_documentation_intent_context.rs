// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_documentation_intent_context(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::DocumentationIntentContext,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.scope {
        object.key("scope").string(var_1.as_str());
    }
    {
        object.key("type").string(input.r#type.as_str());
    }
    if let Some(var_2) = &input.change_log_options {
        #[allow(unused_mut)]
        let mut object_3 = object.key("changeLogOptions").start_object();
        crate::protocol_serde::shape_change_log_options::ser_change_log_options(&mut object_3, var_2)?;
        object_3.finish();
    }
    Ok(())
}
