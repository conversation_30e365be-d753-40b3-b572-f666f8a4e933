// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_additional_content_entry(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::AdditionalContentEntry,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("name").string(input.name.as_str());
    }
    {
        object.key("description").string(input.description.as_str());
    }
    if let Some(var_1) = &input.inner_context {
        object.key("innerContext").string(var_1.as_str());
    }
    Ok(())
}
