// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_range(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::Range,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        #[allow(unused_mut)]
        let mut object_1 = object.key("start").start_object();
        crate::protocol_serde::shape_position::ser_position(&mut object_1, &input.start)?;
        object_1.finish();
    }
    {
        #[allow(unused_mut)]
        let mut object_2 = object.key("end").start_object();
        crate::protocol_serde::shape_position::ser_position(&mut object_2, &input.end)?;
        object_2.finish();
    }
    Ok(())
}
