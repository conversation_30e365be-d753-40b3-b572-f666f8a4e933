// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_start_test_generation_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::start_test_generation::StartTestGenerationInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.upload_id {
        object.key("uploadId").string(var_1.as_str());
    }
    if let Some(var_2) = &input.target_code_list {
        let mut array_3 = object.key("targetCodeList").start_array();
        for item_4 in var_2 {
            {
                #[allow(unused_mut)]
                let mut object_5 = array_3.value().start_object();
                crate::protocol_serde::shape_target_code::ser_target_code(&mut object_5, item_4)?;
                object_5.finish();
            }
        }
        array_3.finish();
    }
    if let Some(var_6) = &input.user_input {
        object.key("userInput").string(var_6.as_str());
    }
    if let Some(var_7) = &input.test_generation_job_group_name {
        object.key("testGenerationJobGroupName").string(var_7.as_str());
    }
    if let Some(var_8) = &input.client_token {
        object.key("clientToken").string(var_8.as_str());
    }
    if let Some(var_9) = &input.profile_arn {
        object.key("profileArn").string(var_9.as_str());
    }
    if let Some(var_10) = &input.reference_tracker_configuration {
        #[allow(unused_mut)]
        let mut object_11 = object.key("referenceTrackerConfiguration").start_object();
        crate::protocol_serde::shape_reference_tracker_configuration::ser_reference_tracker_configuration(
            &mut object_11,
            var_10,
        )?;
        object_11.finish();
    }
    Ok(())
}
