// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_position(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::Position,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("line").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.line).into()),
        );
    }
    {
        object.key("character").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.character).into()),
        );
    }
    Ok(())
}
