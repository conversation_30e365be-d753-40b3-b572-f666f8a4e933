// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_diagnostic_related_information(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::DiagnosticRelatedInformation,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        #[allow(unused_mut)]
        let mut object_1 = object.key("location").start_object();
        crate::protocol_serde::shape_diagnostic_location::ser_diagnostic_location(&mut object_1, &input.location)?;
        object_1.finish();
    }
    {
        object.key("message").string(input.message.as_str());
    }
    Ok(())
}
