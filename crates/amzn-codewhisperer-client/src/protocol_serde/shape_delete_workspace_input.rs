// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_delete_workspace_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::delete_workspace::DeleteWorkspaceInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.workspace_id {
        object.key("workspaceId").string(var_1.as_str());
    }
    if let Some(var_2) = &input.profile_arn {
        object.key("profileArn").string(var_2.as_str());
    }
    Ok(())
}
