// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_shell_history_entry(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::ShellHistoryEntry,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("command").string(input.command.as_str());
    }
    if let Some(var_1) = &input.directory {
        object.key("directory").string(var_1.as_str());
    }
    if let Some(var_2) = &input.exit_code {
        object.key("exitCode").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_2).into()),
        );
    }
    if let Some(var_3) = &input.stdout {
        object.key("stdout").string(var_3.as_str());
    }
    if let Some(var_4) = &input.stderr {
        object.key("stderr").string(var_4.as_str());
    }
    Ok(())
}
