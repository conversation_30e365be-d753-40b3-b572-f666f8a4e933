// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(clippy::unnecessary_wraps)]
pub fn de_list_feature_evaluations_http_error(
    _response_status: u16,
    _response_headers: &::aws_smithy_runtime_api::http::Headers,
    _response_body: &[u8],
) -> std::result::Result<
    crate::operation::list_feature_evaluations::ListFeatureEvaluationsOutput,
    crate::operation::list_feature_evaluations::ListFeatureEvaluationsError,
> {
    #[allow(unused_mut)]
    let mut generic_builder =
        crate::protocol_serde::parse_http_error_metadata(_response_status, _response_headers, _response_body)
            .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?;
    generic_builder = ::aws_types::request_id::apply_request_id(generic_builder, _response_headers);
    let generic = generic_builder.build();
    let error_code = match generic.code() {
        Some(code) => code,
        None => {
            return Err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled(generic));
        },
    };

    let _error_message = generic.message().map(|msg| msg.to_owned());
    Err(match error_code {
        "ValidationException" => {
            crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::ValidationError({
                #[allow(unused_mut)]
                let mut tmp = {
                    #[allow(unused_mut)]
                    let mut output = crate::types::error::builders::ValidationErrorBuilder::default();
                    output = crate::protocol_serde::shape_validation_exception::de_validation_exception_json_err(
                        _response_body,
                        output,
                    )
                    .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?;
                    let output = output.meta(generic);
                    crate::serde_util::validation_exception_correct_errors(output)
                        .build()
                        .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?
                };
                tmp
            })
        },
        "AccessDeniedException" => {
            crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::AccessDeniedError({
                #[allow(unused_mut)]
                let mut tmp = {
                    #[allow(unused_mut)]
                    let mut output = crate::types::error::builders::AccessDeniedErrorBuilder::default();
                    output = crate::protocol_serde::shape_access_denied_exception::de_access_denied_exception_json_err(
                        _response_body,
                        output,
                    )
                    .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?;
                    let output = output.meta(generic);
                    crate::serde_util::access_denied_exception_correct_errors(output)
                        .build()
                        .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?
                };
                tmp
            })
        },
        "ThrottlingException" => {
            crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::ThrottlingError({
                #[allow(unused_mut)]
                let mut tmp = {
                    #[allow(unused_mut)]
                    let mut output = crate::types::error::builders::ThrottlingErrorBuilder::default();
                    output = crate::protocol_serde::shape_throttling_exception::de_throttling_exception_json_err(
                        _response_body,
                        output,
                    )
                    .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?;
                    let output = output.meta(generic);
                    crate::serde_util::throttling_exception_correct_errors(output)
                        .build()
                        .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?
                };
                tmp
            })
        },
        "InternalServerException" => {
            crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::InternalServerError({
                #[allow(unused_mut)]
                let mut tmp = {
                    #[allow(unused_mut)]
                    let mut output = crate::types::error::builders::InternalServerErrorBuilder::default();
                    output =
                        crate::protocol_serde::shape_internal_server_exception::de_internal_server_exception_json_err(
                            _response_body,
                            output,
                        )
                        .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?;
                    let output = output.meta(generic);
                    crate::serde_util::internal_server_exception_correct_errors(output)
                        .build()
                        .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?
                };
                tmp
            })
        },
        _ => crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::generic(generic),
    })
}

#[allow(clippy::unnecessary_wraps)]
pub fn de_list_feature_evaluations_http_response(
    _response_status: u16,
    _response_headers: &::aws_smithy_runtime_api::http::Headers,
    _response_body: &[u8],
) -> std::result::Result<
    crate::operation::list_feature_evaluations::ListFeatureEvaluationsOutput,
    crate::operation::list_feature_evaluations::ListFeatureEvaluationsError,
> {
    Ok({
        #[allow(unused_mut)]
        let mut output =
            crate::operation::list_feature_evaluations::builders::ListFeatureEvaluationsOutputBuilder::default();
        output =
            crate::protocol_serde::shape_list_feature_evaluations::de_list_feature_evaluations(_response_body, output)
                .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?;
        output._set_request_id(::aws_types::request_id::RequestId::request_id(_response_headers).map(str::to_string));
        crate::serde_util::list_feature_evaluations_output_output_correct_errors(output)
            .build()
            .map_err(crate::operation::list_feature_evaluations::ListFeatureEvaluationsError::unhandled)?
    })
}

pub fn ser_list_feature_evaluations_input(
    input: &crate::operation::list_feature_evaluations::ListFeatureEvaluationsInput,
) -> ::std::result::Result<::aws_smithy_types::body::SdkBody, ::aws_smithy_types::error::operation::SerializationError>
{
    let mut out = String::new();
    let mut object = ::aws_smithy_json::serialize::JsonObjectWriter::new(&mut out);
    crate::protocol_serde::shape_list_feature_evaluations_input::ser_list_feature_evaluations_input_input(
        &mut object,
        input,
    )?;
    object.finish();
    Ok(::aws_smithy_types::body::SdkBody::from(out))
}

pub(crate) fn de_list_feature_evaluations(
    value: &[u8],
    mut builder: crate::operation::list_feature_evaluations::builders::ListFeatureEvaluationsOutputBuilder,
) -> ::std::result::Result<
    crate::operation::list_feature_evaluations::builders::ListFeatureEvaluationsOutputBuilder,
    ::aws_smithy_json::deserialize::error::DeserializeError,
> {
    let mut tokens_owned =
        ::aws_smithy_json::deserialize::json_token_iter(crate::protocol_serde::or_empty_doc(value)).peekable();
    let tokens = &mut tokens_owned;
    ::aws_smithy_json::deserialize::token::expect_start_object(tokens.next())?;
    loop {
        match tokens.next().transpose()? {
            Some(::aws_smithy_json::deserialize::Token::EndObject { .. }) => break,
            Some(::aws_smithy_json::deserialize::Token::ObjectKey { key, .. }) => match key.to_unescaped()?.as_ref() {
                "featureEvaluations" => {
                    builder = builder.set_feature_evaluations(
                        crate::protocol_serde::shape_feature_evaluations_list::de_feature_evaluations_list(tokens)?,
                    );
                },
                _ => ::aws_smithy_json::deserialize::token::skip_value(tokens)?,
            },
            other => {
                return Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
                    format!("expected object key or end object, found: {:?}", other),
                ));
            },
        }
    }
    if tokens.next().is_some() {
        return Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
            "found more JSON tokens after completing parsing",
        ));
    }
    Ok(builder)
}
