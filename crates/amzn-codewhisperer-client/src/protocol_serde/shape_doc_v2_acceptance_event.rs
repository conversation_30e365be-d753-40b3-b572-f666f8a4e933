// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_doc_v2_acceptance_event(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::DocV2AcceptanceEvent,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("conversationId").string(input.conversation_id.as_str());
    }
    {
        object.key("numberOfAddedChars").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.number_of_added_chars).into()),
        );
    }
    {
        object.key("numberOfAddedLines").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.number_of_added_lines).into()),
        );
    }
    {
        object.key("numberOfAddedFiles").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.number_of_added_files).into()),
        );
    }
    {
        object.key("userDecision").string(input.user_decision.as_str());
    }
    {
        object.key("interactionType").string(input.interaction_type.as_str());
    }
    {
        object.key("numberOfNavigations").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((input.number_of_navigations).into()),
        );
    }
    {
        object.key("folderLevel").string(input.folder_level.as_str());
    }
    Ok(())
}
