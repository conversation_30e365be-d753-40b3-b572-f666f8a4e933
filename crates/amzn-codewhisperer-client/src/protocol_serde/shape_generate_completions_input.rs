// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_generate_completions_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::generate_completions::GenerateCompletionsInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.file_context {
        #[allow(unused_mut)]
        let mut object_2 = object.key("fileContext").start_object();
        crate::protocol_serde::shape_file_context::ser_file_context(&mut object_2, var_1)?;
        object_2.finish();
    }
    if let Some(var_3) = &input.editor_state {
        #[allow(unused_mut)]
        let mut object_4 = object.key("editorState").start_object();
        crate::protocol_serde::shape_editor_state::ser_editor_state(&mut object_4, var_3)?;
        object_4.finish();
    }
    if let Some(var_5) = &input.max_results {
        object.key("maxResults").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_5).into()),
        );
    }
    if let Some(var_6) = &input.prediction_types {
        let mut array_7 = object.key("predictionTypes").start_array();
        for item_8 in var_6 {
            {
                array_7.value().string(item_8.as_str());
            }
        }
        array_7.finish();
    }
    if let Some(var_9) = &input.next_token {
        object.key("nextToken").string(var_9.as_str());
    }
    if let Some(var_10) = &input.reference_tracker_configuration {
        #[allow(unused_mut)]
        let mut object_11 = object.key("referenceTrackerConfiguration").start_object();
        crate::protocol_serde::shape_reference_tracker_configuration::ser_reference_tracker_configuration(
            &mut object_11,
            var_10,
        )?;
        object_11.finish();
    }
    if let Some(var_12) = &input.supplemental_contexts {
        let mut array_13 = object.key("supplementalContexts").start_array();
        for item_14 in var_12 {
            {
                #[allow(unused_mut)]
                let mut object_15 = array_13.value().start_object();
                crate::protocol_serde::shape_supplemental_context::ser_supplemental_context(&mut object_15, item_14)?;
                object_15.finish();
            }
        }
        array_13.finish();
    }
    if let Some(var_16) = &input.customization_arn {
        object.key("customizationArn").string(var_16.as_str());
    }
    if let Some(var_17) = &input.opt_out_preference {
        object.key("optOutPreference").string(var_17.as_str());
    }
    if let Some(var_18) = &input.user_context {
        #[allow(unused_mut)]
        let mut object_19 = object.key("userContext").start_object();
        crate::protocol_serde::shape_user_context::ser_user_context(&mut object_19, var_18)?;
        object_19.finish();
    }
    if let Some(var_20) = &input.profile_arn {
        object.key("profileArn").string(var_20.as_str());
    }
    if let Some(var_21) = &input.workspace_id {
        object.key("workspaceId").string(var_21.as_str());
    }
    if let Some(var_22) = &input.model_id {
        object.key("modelId").string(var_22.as_str());
    }
    Ok(())
}
