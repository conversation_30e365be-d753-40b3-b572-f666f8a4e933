// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_terminal_user_interaction_event(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::TerminalUserInteractionEvent,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.terminal_user_interaction_event_type {
        object.key("terminalUserInteractionEventType").string(var_1.as_str());
    }
    if let Some(var_2) = &input.terminal {
        object.key("terminal").string(var_2.as_str());
    }
    if let Some(var_3) = &input.terminal_version {
        object.key("terminalVersion").string(var_3.as_str());
    }
    if let Some(var_4) = &input.shell {
        object.key("shell").string(var_4.as_str());
    }
    if let Some(var_5) = &input.shell_version {
        object.key("shellVersion").string(var_5.as_str());
    }
    if let Some(var_6) = &input.duration {
        object.key("duration").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_6).into()),
        );
    }
    if let Some(var_7) = &input.time_to_suggestion {
        object.key("timeToSuggestion").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_7).into()),
        );
    }
    if let Some(var_8) = &input.is_completion_accepted {
        object.key("isCompletionAccepted").boolean(*var_8);
    }
    if let Some(var_9) = &input.cli_tool_command {
        object.key("cliToolCommand").string(var_9.as_str());
    }
    Ok(())
}
