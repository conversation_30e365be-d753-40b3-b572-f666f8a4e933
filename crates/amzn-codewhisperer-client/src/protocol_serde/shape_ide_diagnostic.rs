// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_ide_diagnostic(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::IdeDiagnostic,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.range {
        #[allow(unused_mut)]
        let mut object_2 = object.key("range").start_object();
        crate::protocol_serde::shape_range::ser_range(&mut object_2, var_1)?;
        object_2.finish();
    }
    if let Some(var_3) = &input.source {
        object.key("source").string(var_3.as_str());
    }
    if let Some(var_4) = &input.severity {
        object.key("severity").string(var_4.as_str());
    }
    {
        object
            .key("ideDiagnosticType")
            .string(input.ide_diagnostic_type.as_str());
    }
    Ok(())
}
