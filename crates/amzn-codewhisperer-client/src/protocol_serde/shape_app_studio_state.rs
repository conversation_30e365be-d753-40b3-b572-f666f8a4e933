// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_app_studio_state(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::AppStudioState,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("namespace").string(input.namespace.as_str());
    }
    {
        object.key("propertyName").string(input.property_name.as_str());
    }
    if let Some(var_1) = &input.property_value {
        object.key("propertyValue").string(var_1.as_str());
    }
    {
        object.key("propertyContext").string(input.property_context.as_str());
    }
    Ok(())
}
