// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_image_block(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::ImageBlock,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("format").string(input.format.as_str());
    }
    {
        #[allow(unused_mut)]
        let mut object_1 = object.key("source").start_object();
        crate::protocol_serde::shape_image_source::ser_image_source(&mut object_1, &input.source)?;
        object_1.finish();
    }
    Ok(())
}
