// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_editor_state(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::EditorState,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.document {
        #[allow(unused_mut)]
        let mut object_2 = object.key("document").start_object();
        crate::protocol_serde::shape_text_document::ser_text_document(&mut object_2, var_1)?;
        object_2.finish();
    }
    if let Some(var_3) = &input.cursor_state {
        #[allow(unused_mut)]
        let mut object_4 = object.key("cursorState").start_object();
        crate::protocol_serde::shape_cursor_state::ser_cursor_state(&mut object_4, var_3)?;
        object_4.finish();
    }
    if let Some(var_5) = &input.relevant_documents {
        let mut array_6 = object.key("relevantDocuments").start_array();
        for item_7 in var_5 {
            {
                #[allow(unused_mut)]
                let mut object_8 = array_6.value().start_object();
                crate::protocol_serde::shape_relevant_text_document::ser_relevant_text_document(&mut object_8, item_7)?;
                object_8.finish();
            }
        }
        array_6.finish();
    }
    if let Some(var_9) = &input.use_relevant_documents {
        object.key("useRelevantDocuments").boolean(*var_9);
    }
    if let Some(var_10) = &input.workspace_folders {
        let mut array_11 = object.key("workspaceFolders").start_array();
        for item_12 in var_10 {
            {
                array_11.value().string(item_12.as_str());
            }
        }
        array_11.finish();
    }
    Ok(())
}
