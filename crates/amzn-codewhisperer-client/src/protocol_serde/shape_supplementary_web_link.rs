// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_supplementary_web_link(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::SupplementaryWebLink,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("url").string(input.url.as_str());
    }
    {
        object.key("title").string(input.title.as_str());
    }
    if let Some(var_1) = &input.snippet {
        object.key("snippet").string(var_1.as_str());
    }
    Ok(())
}
