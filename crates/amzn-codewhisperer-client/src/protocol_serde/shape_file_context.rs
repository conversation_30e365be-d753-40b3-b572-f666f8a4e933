// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_file_context(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::FileContext,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("leftFileContent").string(input.left_file_content.as_str());
    }
    {
        object.key("rightFileContent").string(input.right_file_content.as_str());
    }
    {
        object.key("filename").string(input.filename.as_str());
    }
    if let Some(var_1) = &input.file_uri {
        object.key("fileUri").string(var_1.as_str());
    }
    {
        #[allow(unused_mut)]
        let mut object_2 = object.key("programmingLanguage").start_object();
        crate::protocol_serde::shape_programming_language::ser_programming_language(
            &mut object_2,
            &input.programming_language,
        )?;
        object_2.finish();
    }
    Ok(())
}
