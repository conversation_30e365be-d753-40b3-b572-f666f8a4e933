// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_user_input_message_context(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::UserInputMessageContext,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.editor_state {
        #[allow(unused_mut)]
        let mut object_2 = object.key("editorState").start_object();
        crate::protocol_serde::shape_editor_state::ser_editor_state(&mut object_2, var_1)?;
        object_2.finish();
    }
    if let Some(var_3) = &input.shell_state {
        #[allow(unused_mut)]
        let mut object_4 = object.key("shellState").start_object();
        crate::protocol_serde::shape_shell_state::ser_shell_state(&mut object_4, var_3)?;
        object_4.finish();
    }
    if let Some(var_5) = &input.git_state {
        #[allow(unused_mut)]
        let mut object_6 = object.key("gitState").start_object();
        crate::protocol_serde::shape_git_state::ser_git_state(&mut object_6, var_5)?;
        object_6.finish();
    }
    if let Some(var_7) = &input.env_state {
        #[allow(unused_mut)]
        let mut object_8 = object.key("envState").start_object();
        crate::protocol_serde::shape_env_state::ser_env_state(&mut object_8, var_7)?;
        object_8.finish();
    }
    if let Some(var_9) = &input.app_studio_context {
        #[allow(unused_mut)]
        let mut object_10 = object.key("appStudioContext").start_object();
        crate::protocol_serde::shape_app_studio_state::ser_app_studio_state(&mut object_10, var_9)?;
        object_10.finish();
    }
    if let Some(var_11) = &input.diagnostic {
        #[allow(unused_mut)]
        let mut object_12 = object.key("diagnostic").start_object();
        crate::protocol_serde::shape_diagnostic::ser_diagnostic(&mut object_12, var_11)?;
        object_12.finish();
    }
    if let Some(var_13) = &input.console_state {
        #[allow(unused_mut)]
        let mut object_14 = object.key("consoleState").start_object();
        crate::protocol_serde::shape_console_state::ser_console_state(&mut object_14, var_13)?;
        object_14.finish();
    }
    if let Some(var_15) = &input.user_settings {
        #[allow(unused_mut)]
        let mut object_16 = object.key("userSettings").start_object();
        crate::protocol_serde::shape_user_settings::ser_user_settings(&mut object_16, var_15)?;
        object_16.finish();
    }
    if let Some(var_17) = &input.additional_context {
        let mut array_18 = object.key("additionalContext").start_array();
        for item_19 in var_17 {
            {
                #[allow(unused_mut)]
                let mut object_20 = array_18.value().start_object();
                crate::protocol_serde::shape_additional_content_entry::ser_additional_content_entry(
                    &mut object_20,
                    item_19,
                )?;
                object_20.finish();
            }
        }
        array_18.finish();
    }
    if let Some(var_21) = &input.tool_results {
        let mut array_22 = object.key("toolResults").start_array();
        for item_23 in var_21 {
            {
                #[allow(unused_mut)]
                let mut object_24 = array_22.value().start_object();
                crate::protocol_serde::shape_tool_result::ser_tool_result(&mut object_24, item_23)?;
                object_24.finish();
            }
        }
        array_22.finish();
    }
    if let Some(var_25) = &input.tools {
        let mut array_26 = object.key("tools").start_array();
        for item_27 in var_25 {
            {
                #[allow(unused_mut)]
                let mut object_28 = array_26.value().start_object();
                crate::protocol_serde::shape_tool::ser_tool(&mut object_28, item_27)?;
                object_28.finish();
            }
        }
        array_26.finish();
    }
    Ok(())
}
