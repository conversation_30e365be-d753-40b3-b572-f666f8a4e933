// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(clippy::unnecessary_wraps)]
pub fn de_get_usage_limits_http_error(
    _response_status: u16,
    _response_headers: &::aws_smithy_runtime_api::http::Headers,
    _response_body: &[u8],
) -> std::result::Result<
    crate::operation::get_usage_limits::GetUsageLimitsOutput,
    crate::operation::get_usage_limits::GetUsageLimitsError,
> {
    #[allow(unused_mut)]
    let mut generic_builder =
        crate::protocol_serde::parse_http_error_metadata(_response_status, _response_headers, _response_body)
            .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?;
    generic_builder = ::aws_types::request_id::apply_request_id(generic_builder, _response_headers);
    let generic = generic_builder.build();
    let error_code = match generic.code() {
        Some(code) => code,
        None => {
            return Err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled(
                generic,
            ));
        },
    };

    let _error_message = generic.message().map(|msg| msg.to_owned());
    Err(match error_code {
        "ValidationException" => crate::operation::get_usage_limits::GetUsageLimitsError::ValidationError({
            #[allow(unused_mut)]
            let mut tmp = {
                #[allow(unused_mut)]
                let mut output = crate::types::error::builders::ValidationErrorBuilder::default();
                output = crate::protocol_serde::shape_validation_exception::de_validation_exception_json_err(
                    _response_body,
                    output,
                )
                .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?;
                let output = output.meta(generic);
                crate::serde_util::validation_exception_correct_errors(output)
                    .build()
                    .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?
            };
            tmp
        }),
        "AccessDeniedException" => crate::operation::get_usage_limits::GetUsageLimitsError::AccessDeniedError({
            #[allow(unused_mut)]
            let mut tmp = {
                #[allow(unused_mut)]
                let mut output = crate::types::error::builders::AccessDeniedErrorBuilder::default();
                output = crate::protocol_serde::shape_access_denied_exception::de_access_denied_exception_json_err(
                    _response_body,
                    output,
                )
                .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?;
                let output = output.meta(generic);
                crate::serde_util::access_denied_exception_correct_errors(output)
                    .build()
                    .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?
            };
            tmp
        }),
        "ThrottlingException" => crate::operation::get_usage_limits::GetUsageLimitsError::ThrottlingError({
            #[allow(unused_mut)]
            let mut tmp = {
                #[allow(unused_mut)]
                let mut output = crate::types::error::builders::ThrottlingErrorBuilder::default();
                output = crate::protocol_serde::shape_throttling_exception::de_throttling_exception_json_err(
                    _response_body,
                    output,
                )
                .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?;
                let output = output.meta(generic);
                crate::serde_util::throttling_exception_correct_errors(output)
                    .build()
                    .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?
            };
            tmp
        }),
        "InternalServerException" => crate::operation::get_usage_limits::GetUsageLimitsError::InternalServerError({
            #[allow(unused_mut)]
            let mut tmp = {
                #[allow(unused_mut)]
                let mut output = crate::types::error::builders::InternalServerErrorBuilder::default();
                output = crate::protocol_serde::shape_internal_server_exception::de_internal_server_exception_json_err(
                    _response_body,
                    output,
                )
                .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?;
                let output = output.meta(generic);
                crate::serde_util::internal_server_exception_correct_errors(output)
                    .build()
                    .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?
            };
            tmp
        }),
        _ => crate::operation::get_usage_limits::GetUsageLimitsError::generic(generic),
    })
}

#[allow(clippy::unnecessary_wraps)]
pub fn de_get_usage_limits_http_response(
    _response_status: u16,
    _response_headers: &::aws_smithy_runtime_api::http::Headers,
    _response_body: &[u8],
) -> std::result::Result<
    crate::operation::get_usage_limits::GetUsageLimitsOutput,
    crate::operation::get_usage_limits::GetUsageLimitsError,
> {
    Ok({
        #[allow(unused_mut)]
        let mut output = crate::operation::get_usage_limits::builders::GetUsageLimitsOutputBuilder::default();
        output = crate::protocol_serde::shape_get_usage_limits::de_get_usage_limits(_response_body, output)
            .map_err(crate::operation::get_usage_limits::GetUsageLimitsError::unhandled)?;
        output._set_request_id(::aws_types::request_id::RequestId::request_id(_response_headers).map(str::to_string));
        output.build()
    })
}

pub fn ser_get_usage_limits_input(
    input: &crate::operation::get_usage_limits::GetUsageLimitsInput,
) -> ::std::result::Result<::aws_smithy_types::body::SdkBody, ::aws_smithy_types::error::operation::SerializationError>
{
    let mut out = String::new();
    let mut object = ::aws_smithy_json::serialize::JsonObjectWriter::new(&mut out);
    crate::protocol_serde::shape_get_usage_limits_input::ser_get_usage_limits_input_input(&mut object, input)?;
    object.finish();
    Ok(::aws_smithy_types::body::SdkBody::from(out))
}

pub(crate) fn de_get_usage_limits(
    value: &[u8],
    mut builder: crate::operation::get_usage_limits::builders::GetUsageLimitsOutputBuilder,
) -> ::std::result::Result<
    crate::operation::get_usage_limits::builders::GetUsageLimitsOutputBuilder,
    ::aws_smithy_json::deserialize::error::DeserializeError,
> {
    let mut tokens_owned =
        ::aws_smithy_json::deserialize::json_token_iter(crate::protocol_serde::or_empty_doc(value)).peekable();
    let tokens = &mut tokens_owned;
    ::aws_smithy_json::deserialize::token::expect_start_object(tokens.next())?;
    loop {
        match tokens.next().transpose()? {
            Some(::aws_smithy_json::deserialize::Token::EndObject { .. }) => break,
            Some(::aws_smithy_json::deserialize::Token::ObjectKey { key, .. }) => match key.to_unescaped()?.as_ref() {
                "limits" => {
                    builder = builder.set_limits(crate::protocol_serde::shape_usage_limits::de_usage_limits(tokens)?);
                },
                "daysUntilReset" => {
                    builder = builder.set_days_until_reset(
                        ::aws_smithy_json::deserialize::token::expect_number_or_null(tokens.next())?
                            .map(i32::try_from)
                            .transpose()?,
                    );
                },
                "usageBreakdown" => {
                    builder = builder.set_usage_breakdown(
                        crate::protocol_serde::shape_usage_breakdown::de_usage_breakdown(tokens)?,
                    );
                },
                "usageBreakdownList" => {
                    builder = builder.set_usage_breakdown_list(
                        crate::protocol_serde::shape_usage_breakdown_list::de_usage_breakdown_list(tokens)?,
                    );
                },
                "subscriptionInfo" => {
                    builder = builder.set_subscription_info(
                        crate::protocol_serde::shape_subscription_info::de_subscription_info(tokens)?,
                    );
                },
                "overageConfiguration" => {
                    builder = builder.set_overage_configuration(
                        crate::protocol_serde::shape_overage_configuration::de_overage_configuration(tokens)?,
                    );
                },
                "userInfo" => {
                    builder = builder.set_user_info(crate::protocol_serde::shape_user_info::de_user_info(tokens)?);
                },
                "freeTrialInfo" => {
                    builder = builder.set_free_trial_info(
                        crate::protocol_serde::shape_free_trial_info::de_free_trial_info(tokens)?,
                    );
                },
                _ => ::aws_smithy_json::deserialize::token::skip_value(tokens)?,
            },
            other => {
                return Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
                    format!("expected object key or end object, found: {:?}", other),
                ));
            },
        }
    }
    if tokens.next().is_some() {
        return Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
            "found more JSON tokens after completing parsing",
        ));
    }
    Ok(builder)
}
