// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_set_user_preference_input_input(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::operation::set_user_preference::SetUserPreferenceInput,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.overage_configuration {
        #[allow(unused_mut)]
        let mut object_2 = object.key("overageConfiguration").start_object();
        crate::protocol_serde::shape_overage_configuration::ser_overage_configuration(&mut object_2, var_1)?;
        object_2.finish();
    }
    Ok(())
}
