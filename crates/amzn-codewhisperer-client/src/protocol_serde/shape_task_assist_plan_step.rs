// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_task_assist_plan_step(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::TaskAssistPlanStep,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("filePath").string(input.file_path.as_str());
    }
    {
        object.key("description").string(input.description.as_str());
    }
    if let Some(var_1) = &input.start_line {
        object.key("startLine").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_1).into()),
        );
    }
    if let Some(var_2) = &input.end_line {
        object.key("endLine").number(
            #[allow(clippy::useless_conversion)]
            ::aws_smithy_types::Number::NegInt((*var_2).into()),
        );
    }
    if let Some(var_3) = &input.action {
        object.key("action").string(var_3.as_str());
    }
    Ok(())
}
