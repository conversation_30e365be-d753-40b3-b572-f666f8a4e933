// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`GetTransformationPlan`](crate::operation::get_transformation_plan::builders::GetTransformationPlanFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`transformation_job_id(impl Into<String>)`](crate::operation::get_transformation_plan::builders::GetTransformationPlanFluentBuilder::transformation_job_id) / [`set_transformation_job_id(Option<String>)`](crate::operation::get_transformation_plan::builders::GetTransformationPlanFluentBuilder::set_transformation_job_id):<br>required: **true**<br>Identifier for the Transformation Job<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::get_transformation_plan::builders::GetTransformationPlanFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::get_transformation_plan::builders::GetTransformationPlanFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`GetTransformationPlanOutput`](crate::operation::get_transformation_plan::GetTransformationPlanOutput)
    ///   with field(s):
    ///   - [`transformation_plan(TransformationPlan)`](crate::operation::get_transformation_plan::GetTransformationPlanOutput::transformation_plan): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<GetTransformationPlanError>`](crate::operation::get_transformation_plan::GetTransformationPlanError)
    pub fn get_transformation_plan(
        &self,
    ) -> crate::operation::get_transformation_plan::builders::GetTransformationPlanFluentBuilder {
        crate::operation::get_transformation_plan::builders::GetTransformationPlanFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
