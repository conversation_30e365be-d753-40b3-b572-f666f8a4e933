// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`ListAvailableCustomizations`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder)
    /// operation. This operation supports pagination; See
    /// [`into_paginator()`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder::into_paginator).
    ///
    ///
    /// - The fluent builder is configurable:
    ///   - [`max_results(i32)`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder::max_results) / [`set_max_results(Option<i32>)`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder::set_max_results):<br>required: **false**<br>(undocumented)<br>
    ///   - [`next_token(impl Into<String>)`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder::next_token) / [`set_next_token(Option<String>)`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder::set_next_token):<br>required: **false**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with [`ListAvailableCustomizationsOutput`](crate::operation::list_available_customizations::ListAvailableCustomizationsOutput) with field(s):
    ///   - [`customizations(Vec::<Customization>)`](crate::operation::list_available_customizations::ListAvailableCustomizationsOutput::customizations): (undocumented)
    ///   - [`next_token(Option<String>)`](crate::operation::list_available_customizations::ListAvailableCustomizationsOutput::next_token): (undocumented)
    /// - On failure, responds with [`SdkError<ListAvailableCustomizationsError>`](crate::operation::list_available_customizations::ListAvailableCustomizationsError)
    pub fn list_available_customizations(
        &self,
    ) -> crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder {
        crate::operation::list_available_customizations::builders::ListAvailableCustomizationsFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
