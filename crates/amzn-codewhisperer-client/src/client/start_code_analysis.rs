// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`StartCodeAnalysis`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`artifacts(ArtifactType, impl
    ///     Into<String>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::artifacts)
    ///     / [`set_artifacts(Option<HashMap::<ArtifactType,
    ///     String>>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::set_artifacts):
    ///     <br>required: **true**<br>(undocumented)<br>
    ///   - [`programming_language(ProgrammingLanguage)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::programming_language) / [`set_programming_language(Option<ProgrammingLanguage>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::set_programming_language):<br>required: **true**<br>Programming Languages supported by CodeWhisperer<br>
    ///   - [`client_token(impl Into<String>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::client_token) / [`set_client_token(Option<String>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::set_client_token):<br>required: **false**<br>(undocumented)<br>
    ///   - [`scope(CodeAnalysisScope)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::scope) / [`set_scope(Option<CodeAnalysisScope>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::set_scope):<br>required: **false**<br>(undocumented)<br>
    ///   - [`code_scan_name(impl Into<String>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::code_scan_name) / [`set_code_scan_name(Option<String>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::set_code_scan_name):<br>required: **false**<br>Code analysis scan name<br>
    ///   - [`code_diff_metadata(CodeDiffMetadata)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::code_diff_metadata) / [`set_code_diff_metadata(Option<CodeDiffMetadata>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::set_code_diff_metadata):<br>required: **false**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`StartCodeAnalysisOutput`](crate::operation::start_code_analysis::StartCodeAnalysisOutput)
    ///   with field(s):
    ///   - [`job_id(String)`](crate::operation::start_code_analysis::StartCodeAnalysisOutput::job_id): (undocumented)
    ///   - [`status(CodeAnalysisStatus)`](crate::operation::start_code_analysis::StartCodeAnalysisOutput::status): (undocumented)
    ///   - [`error_message(Option<String>)`](crate::operation::start_code_analysis::StartCodeAnalysisOutput::error_message): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<StartCodeAnalysisError>`](crate::operation::start_code_analysis::StartCodeAnalysisError)
    pub fn start_code_analysis(
        &self,
    ) -> crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder {
        crate::operation::start_code_analysis::builders::StartCodeAnalysisFluentBuilder::new(self.handle.clone())
    }
}
