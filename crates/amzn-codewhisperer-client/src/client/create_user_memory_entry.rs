// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`CreateUserMemoryEntry`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`memory_entry_string(impl Into<String>)`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::memory_entry_string) / [`set_memory_entry_string(Option<String>)`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::set_memory_entry_string):<br>required: **true**<br>(undocumented)<br>
    ///   - [`origin(Origin)`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::origin) / [`set_origin(Option<Origin>)`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::set_origin):<br>required: **true**<br>Enum to represent the origin application conversing with Sidekick.<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::set_profile_arn):<br>required: **false**<br>ProfileArn for the managing Q Profile<br>
    ///   - [`client_token(impl Into<String>)`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::client_token) / [`set_client_token(Option<String>)`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::set_client_token):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`CreateUserMemoryEntryOutput`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput)
    ///   with field(s):
    ///   - [`memory_entry(MemoryEntry)`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput::memory_entry): MemoryEntry corresponds to a single user memory
    /// - On failure, responds with
    ///   [`SdkError<CreateUserMemoryEntryError>`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryError)
    pub fn create_user_memory_entry(
        &self,
    ) -> crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder {
        crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
