// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`StartTaskAssistCodeGeneration`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`conversation_state(ConversationState)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::conversation_state) / [`set_conversation_state(Option<ConversationState>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::set_conversation_state):<br>required: **true**<br>Structure to represent the current state of a chat conversation.<br>
    ///   - [`workspace_state(WorkspaceState)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::workspace_state) / [`set_workspace_state(Option<WorkspaceState>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::set_workspace_state):<br>required: **true**<br>Represents a Workspace state uploaded to S3 for Async Code Actions<br>
    ///   - [`task_assist_plan(TaskAssistPlanStep)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::task_assist_plan) / [`set_task_assist_plan(Option<Vec::<TaskAssistPlanStep>>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::set_task_assist_plan):<br>required: **false**<br>(undocumented)<br>
    ///   - [`code_generation_id(impl Into<String>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::code_generation_id) / [`set_code_generation_id(Option<String>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::set_code_generation_id):<br>required: **false**<br>ID which represents a single code generation in a conversation<br>
    ///   - [`current_code_generation_id(impl Into<String>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::current_code_generation_id) / [`set_current_code_generation_id(Option<String>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::set_current_code_generation_id):<br>required: **false**<br>ID which represents a single code generation in a conversation<br>
    ///   - [`intent(Intent)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::intent) / [`set_intent(Option<Intent>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::set_intent):<br>required: **false**<br>(undocumented)<br>
    ///   - [`intent_context(IntentContext)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::intent_context) / [`set_intent_context(Option<IntentContext>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::set_intent_context):<br>required: **false**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with [`StartTaskAssistCodeGenerationOutput`](crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput) with field(s):
    ///   - [`conversation_id(String)`](crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput::conversation_id): ID which represents a multi-turn conversation
    ///   - [`code_generation_id(String)`](crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput::code_generation_id): ID which represents a single code generation in a conversation
    /// - On failure, responds with [`SdkError<StartTaskAssistCodeGenerationError>`](crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationError)
    pub fn start_task_assist_code_generation(
        &self,
    ) -> crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder {
        crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
