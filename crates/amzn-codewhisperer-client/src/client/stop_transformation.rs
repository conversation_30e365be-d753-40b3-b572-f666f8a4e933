// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`StopTransformation`](crate::operation::stop_transformation::builders::StopTransformationFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`transformation_job_id(impl Into<String>)`](crate::operation::stop_transformation::builders::StopTransformationFluentBuilder::transformation_job_id) / [`set_transformation_job_id(Option<String>)`](crate::operation::stop_transformation::builders::StopTransformationFluentBuilder::set_transformation_job_id):<br>required: **true**<br>Identifier for the Transformation Job<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::stop_transformation::builders::StopTransformationFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::stop_transformation::builders::StopTransformationFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`StopTransformationOutput`](crate::operation::stop_transformation::StopTransformationOutput)
    ///   with field(s):
    ///   - [`transformation_status(TransformationStatus)`](crate::operation::stop_transformation::StopTransformationOutput::transformation_status): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<StopTransformationError>`](crate::operation::stop_transformation::StopTransformationError)
    pub fn stop_transformation(
        &self,
    ) -> crate::operation::stop_transformation::builders::StopTransformationFluentBuilder {
        crate::operation::stop_transformation::builders::StopTransformationFluentBuilder::new(self.handle.clone())
    }
}
