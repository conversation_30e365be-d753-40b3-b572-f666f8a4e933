// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`GetCodeAnalysis`](crate::operation::get_code_analysis::builders::GetCodeAnalysisFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`job_id(impl Into<String>)`](crate::operation::get_code_analysis::builders::GetCodeAnalysisFluentBuilder::job_id) / [`set_job_id(Option<String>)`](crate::operation::get_code_analysis::builders::GetCodeAnalysisFluentBuilder::set_job_id):<br>required: **true**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::get_code_analysis::builders::GetCodeAnalysisFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::get_code_analysis::builders::GetCodeAnalysisFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`GetCodeAnalysisOutput`](crate::operation::get_code_analysis::GetCodeAnalysisOutput) with
    ///   field(s):
    ///   - [`status(CodeAnalysisStatus)`](crate::operation::get_code_analysis::GetCodeAnalysisOutput::status): (undocumented)
    ///   - [`error_message(Option<String>)`](crate::operation::get_code_analysis::GetCodeAnalysisOutput::error_message): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<GetCodeAnalysisError>`](crate::operation::get_code_analysis::GetCodeAnalysisError)
    pub fn get_code_analysis(&self) -> crate::operation::get_code_analysis::builders::GetCodeAnalysisFluentBuilder {
        crate::operation::get_code_analysis::builders::GetCodeAnalysisFluentBuilder::new(self.handle.clone())
    }
}
