// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`SetUserPreference`](crate::operation::set_user_preference::builders::SetUserPreferenceFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`overage_configuration(OverageConfiguration)`](crate::operation::set_user_preference::builders::SetUserPreferenceFluentBuilder::overage_configuration) / [`set_overage_configuration(Option<OverageConfiguration>)`](crate::operation::set_user_preference::builders::SetUserPreferenceFluentBuilder::set_overage_configuration):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`SetUserPreferenceOutput`](crate::operation::set_user_preference::SetUserPreferenceOutput)
    /// - On failure, responds with
    ///   [`SdkError<SetUserPreferenceError>`](crate::operation::set_user_preference::SetUserPreferenceError)
    pub fn set_user_preference(
        &self,
    ) -> crate::operation::set_user_preference::builders::SetUserPreferenceFluentBuilder {
        crate::operation::set_user_preference::builders::SetUserPreferenceFluentBuilder::new(self.handle.clone())
    }
}
