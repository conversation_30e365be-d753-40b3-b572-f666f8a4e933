// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`CreateArtifactUploadUrl`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`content_md5(impl Into<String>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::content_md5) / [`set_content_md5(Option<String>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_content_md5):<br>required: **false**<br>(undocumented)<br>
    ///   - [`content_checksum(impl Into<String>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::content_checksum) / [`set_content_checksum(Option<String>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_content_checksum):<br>required: **false**<br>(undocumented)<br>
    ///   - [`content_checksum_type(ContentChecksumType)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::content_checksum_type) / [`set_content_checksum_type(Option<ContentChecksumType>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_content_checksum_type):<br>required: **false**<br>(undocumented)<br>
    ///   - [`content_length(i64)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::content_length) / [`set_content_length(Option<i64>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_content_length):<br>required: **false**<br>(undocumented)<br>
    ///   - [`artifact_type(ArtifactType)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::artifact_type) / [`set_artifact_type(Option<ArtifactType>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_artifact_type):<br>required: **false**<br>(undocumented)<br>
    ///   - [`upload_intent(UploadIntent)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::upload_intent) / [`set_upload_intent(Option<UploadIntent>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_upload_intent):<br>required: **false**<br>Upload Intent<br>
    ///   - [`upload_context(UploadContext)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::upload_context) / [`set_upload_context(Option<UploadContext>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_upload_context):<br>required: **false**<br>(undocumented)<br>
    ///   - [`upload_id(impl Into<String>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::upload_id) / [`set_upload_id(Option<String>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_upload_id):<br>required: **false**<br>Upload ID returned by CreateUploadUrl API<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`CreateArtifactUploadUrlOutput`](crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput)
    ///   with field(s):
    ///   - [`upload_id(String)`](crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput::upload_id): Upload ID returned by CreateUploadUrl API
    ///   - [`upload_url(String)`](crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput::upload_url): (undocumented)
    ///   - [`kms_key_arn(Option<String>)`](crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput::kms_key_arn): (undocumented)
    ///   - [`request_headers(Option<HashMap::<String,
    ///     String>>)`](crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput::request_headers):
    ///     (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<CreateArtifactUploadUrlError>`](crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlError)
    pub fn create_artifact_upload_url(
        &self,
    ) -> crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder {
        crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
