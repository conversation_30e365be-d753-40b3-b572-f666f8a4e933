// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`ListEvents`](crate::operation::list_events::builders::ListEventsFluentBuilder) operation.
    /// This operation supports pagination; See
    /// [`into_paginator()`](crate::operation::list_events::builders::ListEventsFluentBuilder::into_paginator).
    ///
    ///
    /// - The fluent builder is configurable:
    ///   - [`conversation_id(impl Into<String>)`](crate::operation::list_events::builders::ListEventsFluentBuilder::conversation_id) / [`set_conversation_id(Option<String>)`](crate::operation::list_events::builders::ListEventsFluentBuilder::set_conversation_id):<br>required: **true**<br>(undocumented)<br>
    ///   - [`max_results(i32)`](crate::operation::list_events::builders::ListEventsFluentBuilder::max_results) / [`set_max_results(Option<i32>)`](crate::operation::list_events::builders::ListEventsFluentBuilder::set_max_results):<br>required: **false**<br>(undocumented)<br>
    ///   - [`next_token(impl
    ///     Into<String>)`](crate::operation::list_events::builders::ListEventsFluentBuilder::next_token)
    ///     / [`set_next_token(Option<String>)`](crate::operation::list_events::builders::ListEventsFluentBuilder::set_next_token):
    ///     <br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`ListEventsOutput`](crate::operation::list_events::ListEventsOutput) with field(s):
    ///   - [`conversation_id(String)`](crate::operation::list_events::ListEventsOutput::conversation_id): (undocumented)
    ///   - [`events(Vec::<Event>)`](crate::operation::list_events::ListEventsOutput::events):
    ///     (undocumented)
    ///   - [`next_token(Option<String>)`](crate::operation::list_events::ListEventsOutput::next_token): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<ListEventsError>`](crate::operation::list_events::ListEventsError)
    pub fn list_events(&self) -> crate::operation::list_events::builders::ListEventsFluentBuilder {
        crate::operation::list_events::builders::ListEventsFluentBuilder::new(self.handle.clone())
    }
}
