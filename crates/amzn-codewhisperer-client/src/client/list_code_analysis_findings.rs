// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`ListCodeAnalysisFindings`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder)
    /// operation. This operation supports pagination; See
    /// [`into_paginator()`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::into_paginator).
    ///
    ///
    /// - The fluent builder is configurable:
    ///   - [`job_id(impl Into<String>)`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::job_id) / [`set_job_id(Option<String>)`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::set_job_id):<br>required: **true**<br>(undocumented)<br>
    ///   - [`next_token(impl Into<String>)`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::next_token) / [`set_next_token(Option<String>)`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::set_next_token):<br>required: **false**<br>(undocumented)<br>
    ///   - [`code_analysis_findings_schema(CodeAnalysisFindingsSchema)`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::code_analysis_findings_schema) / [`set_code_analysis_findings_schema(Option<CodeAnalysisFindingsSchema>)`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::set_code_analysis_findings_schema):<br>required: **true**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`ListCodeAnalysisFindingsOutput`](crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsOutput)
    ///   with field(s):
    ///   - [`next_token(Option<String>)`](crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsOutput::next_token): (undocumented)
    ///   - [`code_analysis_findings(String)`](crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsOutput::code_analysis_findings): (undocumented)
    /// - On failure, responds with [`SdkError<ListCodeAnalysisFindingsError>`](crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsError)
    pub fn list_code_analysis_findings(
        &self,
    ) -> crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder {
        crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
