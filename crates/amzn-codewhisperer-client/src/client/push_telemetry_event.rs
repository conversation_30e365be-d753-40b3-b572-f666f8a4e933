// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`PushTelemetryEvent`](crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`client_token(impl Into<String>)`](crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder::client_token) / [`set_client_token(Option<String>)`](crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder::set_client_token):<br>required: **false**<br>(undocumented)<br>
    ///   - [`event_type(impl Into<String>)`](crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder::event_type) / [`set_event_type(Option<String>)`](crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder::set_event_type):<br>required: **true**<br>(undocumented)<br>
    ///   - [`event(Document)`](crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder::event) / [`set_event(Option<Document>)`](crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder::set_event):<br>required: **true**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`PushTelemetryEventOutput`](crate::operation::push_telemetry_event::PushTelemetryEventOutput)
    /// - On failure, responds with
    ///   [`SdkError<PushTelemetryEventError>`](crate::operation::push_telemetry_event::PushTelemetryEventError)
    pub fn push_telemetry_event(
        &self,
    ) -> crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder {
        crate::operation::push_telemetry_event::builders::PushTelemetryEventFluentBuilder::new(self.handle.clone())
    }
}
