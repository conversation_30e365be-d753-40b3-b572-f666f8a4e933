// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`ListUserMemoryEntries`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder)
    /// operation. This operation supports pagination; See
    /// [`into_paginator()`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder::into_paginator).
    ///
    ///
    /// - The fluent builder is configurable:
    ///   - [`max_results(i32)`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder::max_results) / [`set_max_results(Option<i32>)`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder::set_max_results):<br>required: **false**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder::set_profile_arn):<br>required: **false**<br>ProfileArn for the managing Q Profile<br>
    ///   - [`next_token(impl Into<String>)`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder::next_token) / [`set_next_token(Option<String>)`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder::set_next_token):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`ListUserMemoryEntriesOutput`](crate::operation::list_user_memory_entries::ListUserMemoryEntriesOutput)
    ///   with field(s):
    ///   - [`memory_entries(Vec::<MemoryEntry>)`](crate::operation::list_user_memory_entries::ListUserMemoryEntriesOutput::memory_entries): List of user memories
    ///   - [`next_token(Option<String>)`](crate::operation::list_user_memory_entries::ListUserMemoryEntriesOutput::next_token): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<ListUserMemoryEntriesError>`](crate::operation::list_user_memory_entries::ListUserMemoryEntriesError)
    pub fn list_user_memory_entries(
        &self,
    ) -> crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder {
        crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
