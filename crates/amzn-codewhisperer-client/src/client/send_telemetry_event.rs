// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`SendTelemetryEvent`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`client_token(impl Into<String>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::client_token) / [`set_client_token(Option<String>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::set_client_token):<br>required: **false**<br>(undocumented)<br>
    ///   - [`telemetry_event(TelemetryEvent)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::telemetry_event) / [`set_telemetry_event(Option<TelemetryEvent>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::set_telemetry_event):<br>required: **true**<br>(undocumented)<br>
    ///   - [`opt_out_preference(OptOutPreference)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::opt_out_preference) / [`set_opt_out_preference(Option<OptOutPreference>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::set_opt_out_preference):<br>required: **false**<br>(undocumented)<br>
    ///   - [`user_context(UserContext)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::user_context) / [`set_user_context(Option<UserContext>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::set_user_context):<br>required: **false**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    ///   - [`model_id(impl Into<String>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::model_id) / [`set_model_id(Option<String>)`](crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::set_model_id):<br>required: **false**<br>Unique identifier for the model<br>
    /// - On success, responds with
    ///   [`SendTelemetryEventOutput`](crate::operation::send_telemetry_event::SendTelemetryEventOutput)
    /// - On failure, responds with
    ///   [`SdkError<SendTelemetryEventError>`](crate::operation::send_telemetry_event::SendTelemetryEventError)
    pub fn send_telemetry_event(
        &self,
    ) -> crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder {
        crate::operation::send_telemetry_event::builders::SendTelemetryEventFluentBuilder::new(self.handle.clone())
    }
}
