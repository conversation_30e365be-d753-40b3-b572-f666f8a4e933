// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`CreateWorkspace`](crate::operation::create_workspace::builders::CreateWorkspaceFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`workspace_root(impl Into<String>)`](crate::operation::create_workspace::builders::CreateWorkspaceFluentBuilder::workspace_root) / [`set_workspace_root(Option<String>)`](crate::operation::create_workspace::builders::CreateWorkspaceFluentBuilder::set_workspace_root):<br>required: **true**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::create_workspace::builders::CreateWorkspaceFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::create_workspace::builders::CreateWorkspaceFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`CreateWorkspaceOutput`](crate::operation::create_workspace::CreateWorkspaceOutput) with
    ///   field(s):
    ///   - [`workspace(WorkspaceMetadata)`](crate::operation::create_workspace::CreateWorkspaceOutput::workspace): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<CreateWorkspaceError>`](crate::operation::create_workspace::CreateWorkspaceError)
    pub fn create_workspace(&self) -> crate::operation::create_workspace::builders::CreateWorkspaceFluentBuilder {
        crate::operation::create_workspace::builders::CreateWorkspaceFluentBuilder::new(self.handle.clone())
    }
}
