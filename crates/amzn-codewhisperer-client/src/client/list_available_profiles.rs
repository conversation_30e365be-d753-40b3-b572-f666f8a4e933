// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`ListAvailableProfiles`](crate::operation::list_available_profiles::builders::ListAvailableProfilesFluentBuilder)
    /// operation. This operation supports pagination; See
    /// [`into_paginator()`](crate::operation::list_available_profiles::builders::ListAvailableProfilesFluentBuilder::into_paginator).
    ///
    ///
    /// - The fluent builder is configurable:
    ///   - [`max_results(i32)`](crate::operation::list_available_profiles::builders::ListAvailableProfilesFluentBuilder::max_results) / [`set_max_results(Option<i32>)`](crate::operation::list_available_profiles::builders::ListAvailableProfilesFluentBuilder::set_max_results):<br>required: **false**<br>(undocumented)<br>
    ///   - [`next_token(impl Into<String>)`](crate::operation::list_available_profiles::builders::ListAvailableProfilesFluentBuilder::next_token) / [`set_next_token(Option<String>)`](crate::operation::list_available_profiles::builders::ListAvailableProfilesFluentBuilder::set_next_token):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`ListAvailableProfilesOutput`](crate::operation::list_available_profiles::ListAvailableProfilesOutput)
    ///   with field(s):
    ///   - [`profiles(Vec::<Profile>)`](crate::operation::list_available_profiles::ListAvailableProfilesOutput::profiles): (undocumented)
    ///   - [`next_token(Option<String>)`](crate::operation::list_available_profiles::ListAvailableProfilesOutput::next_token): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<ListAvailableProfilesError>`](crate::operation::list_available_profiles::ListAvailableProfilesError)
    pub fn list_available_profiles(
        &self,
    ) -> crate::operation::list_available_profiles::builders::ListAvailableProfilesFluentBuilder {
        crate::operation::list_available_profiles::builders::ListAvailableProfilesFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
