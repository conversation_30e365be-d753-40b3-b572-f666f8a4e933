// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`CreateSubscriptionToken`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`client_token(impl Into<String>)`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::client_token) / [`set_client_token(Option<String>)`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::set_client_token):<br>required: **false**<br>(undocumented)<br>
    ///   - [`status_only(bool)`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::status_only) / [`set_status_only(Option<bool>)`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::set_status_only):<br>required: **false**<br>(undocumented)<br>
    ///   - [`provider(SubscriptionProvider)`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::provider) / [`set_provider(Option<SubscriptionProvider>)`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::set_provider):<br>required: **false**<br>(undocumented)<br>
    ///   - [`subscription_type(SubscriptionType)`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::subscription_type) / [`set_subscription_type(Option<SubscriptionType>)`](crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::set_subscription_type):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`CreateSubscriptionTokenOutput`](crate::operation::create_subscription_token::CreateSubscriptionTokenOutput)
    ///   with field(s):
    ///   - [`encoded_verification_url(Option<String>)`](crate::operation::create_subscription_token::CreateSubscriptionTokenOutput::encoded_verification_url): (undocumented)
    ///   - [`token(Option<String>)`](crate::operation::create_subscription_token::CreateSubscriptionTokenOutput::token): (undocumented)
    ///   - [`status(SubscriptionStatus)`](crate::operation::create_subscription_token::CreateSubscriptionTokenOutput::status): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<CreateSubscriptionTokenError>`](crate::operation::create_subscription_token::CreateSubscriptionTokenError)
    pub fn create_subscription_token(
        &self,
    ) -> crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder {
        crate::operation::create_subscription_token::builders::CreateSubscriptionTokenFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
