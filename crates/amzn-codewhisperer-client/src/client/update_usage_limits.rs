// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`UpdateUsageLimits`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`account_id(impl Into<String>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::account_id) / [`set_account_id(Option<String>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::set_account_id):<br>required: **true**<br>(undocumented)<br>
    ///   - [`accountless_user_id(impl Into<String>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::accountless_user_id) / [`set_accountless_user_id(Option<String>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::set_accountless_user_id):<br>required: **false**<br>(undocumented)<br>
    ///   - [`directory_id(impl Into<String>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::directory_id) / [`set_directory_id(Option<String>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::set_directory_id):<br>required: **false**<br>(undocumented)<br>
    ///   - [`feature_type(UsageLimitType)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::feature_type) / [`set_feature_type(Option<UsageLimitType>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::set_feature_type):<br>required: **true**<br>(undocumented)<br>
    ///   - [`requested_limit(i64)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::requested_limit) / [`set_requested_limit(Option<i64>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::set_requested_limit):<br>required: **true**<br>(undocumented)<br>
    ///   - [`justification(impl Into<String>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::justification) / [`set_justification(Option<String>)`](crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::set_justification):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`UpdateUsageLimitsOutput`](crate::operation::update_usage_limits::UpdateUsageLimitsOutput)
    ///   with field(s):
    ///   - [`status(UsageLimitUpdateRequestStatus)`](crate::operation::update_usage_limits::UpdateUsageLimitsOutput::status): (undocumented)
    ///   - [`approved_limit(Option<i64>)`](crate::operation::update_usage_limits::UpdateUsageLimitsOutput::approved_limit): (undocumented)
    ///   - [`remaining_requests_this_month(Option<i32>)`](crate::operation::update_usage_limits::UpdateUsageLimitsOutput::remaining_requests_this_month): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<UpdateUsageLimitsError>`](crate::operation::update_usage_limits::UpdateUsageLimitsError)
    pub fn update_usage_limits(
        &self,
    ) -> crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder {
        crate::operation::update_usage_limits::builders::UpdateUsageLimitsFluentBuilder::new(self.handle.clone())
    }
}
