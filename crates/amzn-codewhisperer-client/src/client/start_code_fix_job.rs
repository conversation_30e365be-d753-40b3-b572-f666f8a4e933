// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`StartCodeFixJob`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`snippet_range(Range)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::snippet_range) / [`set_snippet_range(Option<Range>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::set_snippet_range):<br>required: **true**<br>Indicates Range / Span in a Text Document<br>
    ///   - [`upload_id(impl Into<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::upload_id) / [`set_upload_id(Option<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::set_upload_id):<br>required: **true**<br>Upload ID returned by CreateUploadUrl API<br>
    ///   - [`description(impl Into<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::description) / [`set_description(Option<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::set_description):<br>required: **false**<br>(undocumented)<br>
    ///   - [`rule_id(impl Into<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::rule_id) / [`set_rule_id(Option<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::set_rule_id):<br>required: **false**<br>(undocumented)<br>
    ///   - [`code_fix_name(impl Into<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::code_fix_name) / [`set_code_fix_name(Option<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::set_code_fix_name):<br>required: **false**<br>Code fix name<br>
    ///   - [`reference_tracker_configuration(ReferenceTrackerConfiguration)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::reference_tracker_configuration) / [`set_reference_tracker_configuration(Option<ReferenceTrackerConfiguration>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::set_reference_tracker_configuration):<br>required: **false**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`StartCodeFixJobOutput`](crate::operation::start_code_fix_job::StartCodeFixJobOutput)
    ///   with field(s):
    ///   - [`job_id(Option<String>)`](crate::operation::start_code_fix_job::StartCodeFixJobOutput::job_id): (undocumented)
    ///   - [`status(Option<CodeFixJobStatus>)`](crate::operation::start_code_fix_job::StartCodeFixJobOutput::status): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<StartCodeFixJobError>`](crate::operation::start_code_fix_job::StartCodeFixJobError)
    pub fn start_code_fix_job(&self) -> crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder {
        crate::operation::start_code_fix_job::builders::StartCodeFixJobFluentBuilder::new(self.handle.clone())
    }
}
