// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`DeleteWorkspace`](crate::operation::delete_workspace::builders::DeleteWorkspaceFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`workspace_id(impl Into<String>)`](crate::operation::delete_workspace::builders::DeleteWorkspaceFluentBuilder::workspace_id) / [`set_workspace_id(Option<String>)`](crate::operation::delete_workspace::builders::DeleteWorkspaceFluentBuilder::set_workspace_id):<br>required: **true**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::delete_workspace::builders::DeleteWorkspaceFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::delete_workspace::builders::DeleteWorkspaceFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`DeleteWorkspaceOutput`](crate::operation::delete_workspace::DeleteWorkspaceOutput)
    /// - On failure, responds with
    ///   [`SdkError<DeleteWorkspaceError>`](crate::operation::delete_workspace::DeleteWorkspaceError)
    pub fn delete_workspace(&self) -> crate::operation::delete_workspace::builders::DeleteWorkspaceFluentBuilder {
        crate::operation::delete_workspace::builders::DeleteWorkspaceFluentBuilder::new(self.handle.clone())
    }
}
