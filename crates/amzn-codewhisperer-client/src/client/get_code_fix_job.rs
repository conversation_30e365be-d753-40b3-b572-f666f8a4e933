// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`GetCodeFixJob`](crate::operation::get_code_fix_job::builders::GetCodeFixJobFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`job_id(impl
    ///     Into<String>)`](crate::operation::get_code_fix_job::builders::GetCodeFixJobFluentBuilder::job_id)
    ///     / [`set_job_id(Option<String>)`](crate::operation::get_code_fix_job::builders::GetCodeFixJobFluentBuilder::set_job_id):
    ///     <br>required: **true**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::get_code_fix_job::builders::GetCodeFixJobFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::get_code_fix_job::builders::GetCodeFixJobFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`GetCodeFixJobOutput`](crate::operation::get_code_fix_job::GetCodeFixJobOutput) with
    ///   field(s):
    ///   - [`job_status(Option<CodeFixJobStatus>)`](crate::operation::get_code_fix_job::GetCodeFixJobOutput::job_status): (undocumented)
    ///   - [`suggested_fix(Option<SuggestedFix>)`](crate::operation::get_code_fix_job::GetCodeFixJobOutput::suggested_fix): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<GetCodeFixJobError>`](crate::operation::get_code_fix_job::GetCodeFixJobError)
    pub fn get_code_fix_job(&self) -> crate::operation::get_code_fix_job::builders::GetCodeFixJobFluentBuilder {
        crate::operation::get_code_fix_job::builders::GetCodeFixJobFluentBuilder::new(self.handle.clone())
    }
}
