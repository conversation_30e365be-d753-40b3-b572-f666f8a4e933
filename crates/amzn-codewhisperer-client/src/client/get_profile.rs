// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`GetProfile`](crate::operation::get_profile::builders::GetProfileFluentBuilder) operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::get_profile::builders::GetProfileFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::get_profile::builders::GetProfileFluentBuilder::set_profile_arn):<br>required: **true**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`GetProfileOutput`](crate::operation::get_profile::GetProfileOutput) with field(s):
    ///   - [`profile(ProfileInfo)`](crate::operation::get_profile::GetProfileOutput::profile):
    ///     (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<GetProfileError>`](crate::operation::get_profile::GetProfileError)
    pub fn get_profile(&self) -> crate::operation::get_profile::builders::GetProfileFluentBuilder {
        crate::operation::get_profile::builders::GetProfileFluentBuilder::new(self.handle.clone())
    }
}
