// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`GetRetrievals`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`customization_arn(impl Into<String>)`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::customization_arn) / [`set_customization_arn(Option<String>)`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::set_customization_arn):<br>required: **true**<br>(undocumented)<br>
    ///   - [`query(impl
    ///     Into<String>)`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::query)
    ///     / [`set_query(Option<String>)`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::set_query):
    ///     <br>required: **true**<br>(undocumented)<br>
    ///   - [`language_name(impl Into<String>)`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::language_name) / [`set_language_name(Option<String>)`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::set_language_name):<br>required: **true**<br>(undocumented)<br>
    ///   - [`max_results(i32)`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::max_results) / [`set_max_results(Option<i32>)`](crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::set_max_results):<br>required: **true**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`GetRetrievalsOutput`](crate::operation::get_retrievals::GetRetrievalsOutput) with
    ///   field(s):
    ///   - [`retrieval_map(HashMap::<String,
    ///     Vec::<Retrieval>>)`](crate::operation::get_retrievals::GetRetrievalsOutput::retrieval_map):
    ///     (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<GetRetrievalsError>`](crate::operation::get_retrievals::GetRetrievalsError)
    pub fn get_retrievals(&self) -> crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder {
        crate::operation::get_retrievals::builders::GetRetrievalsFluentBuilder::new(self.handle.clone())
    }
}
