// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`ListAvailableModels`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder)
    /// operation. This operation supports pagination; See
    /// [`into_paginator()`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::into_paginator).
    ///
    ///
    /// - The fluent builder is configurable:
    ///   - [`origin(Origin)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::origin) / [`set_origin(Option<Origin>)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::set_origin):<br>required: **true**<br>The origin context for which to list available models<br>
    ///   - [`max_results(i32)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::max_results) / [`set_max_results(Option<i32>)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::set_max_results):<br>required: **false**<br>Maximum number of models to return in a single response<br>
    ///   - [`next_token(impl Into<String>)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::next_token) / [`set_next_token(Option<String>)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::set_next_token):<br>required: **false**<br>Token for retrieving the next page of results<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::set_profile_arn):<br>required: **false**<br>ARN of the profile to use for model filtering<br>
    ///   - [`model_provider(ModelProvider)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::model_provider) / [`set_model_provider(Option<ModelProvider>)`](crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::set_model_provider):<br>required: **false**<br>Provider of AI models<br>
    /// - On success, responds with
    ///   [`ListAvailableModelsOutput`](crate::operation::list_available_models::ListAvailableModelsOutput)
    ///   with field(s):
    ///   - [`models(Vec::<Model>)`](crate::operation::list_available_models::ListAvailableModelsOutput::models): List of available models
    ///   - [`default_model(Option<Model>)`](crate::operation::list_available_models::ListAvailableModelsOutput::default_model): Default model set by the client
    ///   - [`next_token(Option<String>)`](crate::operation::list_available_models::ListAvailableModelsOutput::next_token): Token for retrieving the next page of results
    /// - On failure, responds with
    ///   [`SdkError<ListAvailableModelsError>`](crate::operation::list_available_models::ListAvailableModelsError)
    pub fn list_available_models(
        &self,
    ) -> crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder {
        crate::operation::list_available_models::builders::ListAvailableModelsFluentBuilder::new(self.handle.clone())
    }
}
