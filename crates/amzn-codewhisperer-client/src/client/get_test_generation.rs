// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`GetTestGeneration`](crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`test_generation_job_group_name(impl Into<String>)`](crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder::test_generation_job_group_name) / [`set_test_generation_job_group_name(Option<String>)`](crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder::set_test_generation_job_group_name):<br>required: **true**<br>Test generation job group name<br>
    ///   - [`test_generation_job_id(impl Into<String>)`](crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder::test_generation_job_id) / [`set_test_generation_job_id(Option<String>)`](crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder::set_test_generation_job_id):<br>required: **true**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder::set_profile_arn):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`GetTestGenerationOutput`](crate::operation::get_test_generation::GetTestGenerationOutput)
    ///   with field(s):
    ///   - [`test_generation_job(Option<TestGenerationJob>)`](crate::operation::get_test_generation::GetTestGenerationOutput::test_generation_job): Represents a test generation job
    /// - On failure, responds with
    ///   [`SdkError<GetTestGenerationError>`](crate::operation::get_test_generation::GetTestGenerationError)
    pub fn get_test_generation(
        &self,
    ) -> crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder {
        crate::operation::get_test_generation::builders::GetTestGenerationFluentBuilder::new(self.handle.clone())
    }
}
