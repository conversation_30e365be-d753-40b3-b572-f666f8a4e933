// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`DeleteUserMemoryEntry`](crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryFluentBuilder)
    /// operation.
    ///
    /// - The fluent builder is configurable:
    ///   - [`id(impl Into<String>)`](crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryFluentBuilder::id) / [`set_id(Option<String>)`](crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryFluentBuilder::set_id):<br>required: **true**<br>(undocumented)<br>
    ///   - [`profile_arn(impl Into<String>)`](crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryFluentBuilder::profile_arn) / [`set_profile_arn(Option<String>)`](crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryFluentBuilder::set_profile_arn):<br>required: **false**<br>ProfileArn for the managing Q Profile<br>
    /// - On success, responds with
    ///   [`DeleteUserMemoryEntryOutput`](crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryOutput)
    /// - On failure, responds with
    ///   [`SdkError<DeleteUserMemoryEntryError>`](crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryError)
    pub fn delete_user_memory_entry(
        &self,
    ) -> crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryFluentBuilder {
        crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryFluentBuilder::new(
            self.handle.clone(),
        )
    }
}
