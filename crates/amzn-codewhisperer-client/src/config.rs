// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Configuration for a amzn_codewhisperer_client service client.
///
///
/// Service configuration allows for customization of endpoints, region, credentials providers,
/// and retry configuration. Generally, it is constructed automatically for you from a shared
/// configuration loaded by the `aws-config` crate. For example:
///
/// ```ignore
/// // Load a shared config from the environment
/// let shared_config = aws_config::from_env().load().await;
/// // The client constructor automatically converts the shared config into the service config
/// let client = Client::new(&shared_config);
/// ```
///
/// The service config can also be constructed manually using its builder.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct Config {
    // Both `config` and `cloneable` are the same config, but the cloneable one
    // is kept around so that it is possible to convert back into a builder. This can be
    // optimized in the future.
    pub(crate) config: crate::config::<PERSON><PERSON>en<PERSON><PERSON>er,
    cloneable: ::aws_smithy_types::config_bag::CloneableLayer,
    pub(crate) runtime_components: crate::config::RuntimeComponentsBuilder,
    pub(crate) runtime_plugins: ::std::vec::Vec<crate::config::SharedRuntimePlugin>,
    behavior_version: ::std::option::Option<crate::config::BehaviorVersion>,
}
impl Config {
    /// Constructs a config builder.
    /// <div class="warning">
    /// Note that a config created from this builder will not have the same safe defaults as one
    /// created by the <a href="https://crates.io/crates/aws-config" target="_blank">aws-config</a> crate.
    /// </div>
    pub fn builder() -> Builder {
        Builder::default()
    }

    /// Converts this config back into a builder so that it can be tweaked.
    pub fn to_builder(&self) -> Builder {
        Builder {
            config: self.cloneable.clone(),
            runtime_components: self.runtime_components.clone(),
            runtime_plugins: self.runtime_plugins.clone(),
            behavior_version: self.behavior_version,
        }
    }

    /// Return a reference to the stalled stream protection configuration contained in this config,
    /// if any.
    pub fn stalled_stream_protection(&self) -> ::std::option::Option<&crate::config::StalledStreamProtectionConfig> {
        self.config.load::<crate::config::StalledStreamProtectionConfig>()
    }

    /// Return the [`SharedHttpClient`](crate::config::SharedHttpClient) to use when making
    /// requests, if any.
    pub fn http_client(&self) -> Option<crate::config::SharedHttpClient> {
        self.runtime_components.http_client()
    }

    /// Returns the endpoint resolver.
    pub fn endpoint_resolver(&self) -> ::aws_smithy_runtime_api::client::endpoint::SharedEndpointResolver {
        self.runtime_components
            .endpoint_resolver()
            .expect("resolver defaulted if not set")
    }

    /// Return a reference to the retry configuration contained in this config, if any.
    pub fn retry_config(&self) -> ::std::option::Option<&::aws_smithy_types::retry::RetryConfig> {
        self.config.load::<::aws_smithy_types::retry::RetryConfig>()
    }

    /// Return a cloned shared async sleep implementation from this config, if any.
    pub fn sleep_impl(&self) -> ::std::option::Option<crate::config::SharedAsyncSleep> {
        self.runtime_components.sleep_impl()
    }

    /// Return a reference to the timeout configuration contained in this config, if any.
    pub fn timeout_config(&self) -> ::std::option::Option<&::aws_smithy_types::timeout::TimeoutConfig> {
        self.config.load::<::aws_smithy_types::timeout::TimeoutConfig>()
    }

    /// Returns a reference to the retry partition contained in this config, if any.
    ///
    /// WARNING: This method is unstable and may be removed at any time. Do not rely on this
    /// method for anything!
    pub fn retry_partition(&self) -> ::std::option::Option<&::aws_smithy_runtime::client::retries::RetryPartition> {
        self.config
            .load::<::aws_smithy_runtime::client::retries::RetryPartition>()
    }

    /// Returns the configured identity cache for auth.
    pub fn identity_cache(&self) -> ::std::option::Option<crate::config::SharedIdentityCache> {
        self.runtime_components.identity_cache()
    }

    /// Returns interceptors currently registered by the user.
    pub fn interceptors(&self) -> impl Iterator<Item = crate::config::SharedInterceptor> + '_ {
        self.runtime_components.interceptors()
    }

    /// Return time source used for this service.
    pub fn time_source(&self) -> ::std::option::Option<::aws_smithy_async::time::SharedTimeSource> {
        self.runtime_components.time_source()
    }

    /// Returns retry classifiers currently registered by the user.
    pub fn retry_classifiers(
        &self,
    ) -> impl Iterator<Item = ::aws_smithy_runtime_api::client::retries::classifiers::SharedRetryClassifier> + '_ {
        self.runtime_components.retry_classifiers()
    }

    /// Returns the name of the app that is using the client, if it was provided.
    ///
    /// This _optional_ name is used to identify the application in the user agent that
    /// gets sent along with requests.
    pub fn app_name(&self) -> ::std::option::Option<&::aws_types::app_name::AppName> {
        self.config.load::<::aws_types::app_name::AppName>()
    }

    /// Returns the invocation ID generator if one was given in config.
    ///
    /// The invocation ID generator generates ID values for the `amz-sdk-invocation-id` header. By
    /// default, this will be a random UUID. Overriding it may be useful in tests that examine the
    /// HTTP request and need to be deterministic.
    pub fn invocation_id_generator(
        &self,
    ) -> ::std::option::Option<::aws_runtime::invocation_id::SharedInvocationIdGenerator> {
        self.config
            .load::<::aws_runtime::invocation_id::SharedInvocationIdGenerator>()
            .cloned()
    }

    /// Creates a new [service config](crate::Config) from a [shared
    /// `config`](::aws_types::sdk_config::SdkConfig).
    pub fn new(config: &::aws_types::sdk_config::SdkConfig) -> Self {
        Builder::from(config).build()
    }
}
/// Builder for creating a `Config`.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct Builder {
    pub(crate) config: ::aws_smithy_types::config_bag::CloneableLayer,
    pub(crate) runtime_components: crate::config::RuntimeComponentsBuilder,
    pub(crate) runtime_plugins: ::std::vec::Vec<crate::config::SharedRuntimePlugin>,
    pub(crate) behavior_version: ::std::option::Option<crate::config::BehaviorVersion>,
}
impl ::std::default::Default for Builder {
    fn default() -> Self {
        Self {
            config: ::std::default::Default::default(),
            runtime_components: crate::config::RuntimeComponentsBuilder::new("service config"),
            runtime_plugins: ::std::default::Default::default(),
            behavior_version: ::std::default::Default::default(),
        }
    }
}
impl Builder {
    /// Constructs a config builder.
    /// <div class="warning">
    /// Note that a config created from this builder will not have the same safe defaults as one
    /// created by the <a href="https://crates.io/crates/aws-config" target="_blank">aws-config</a> crate.
    /// </div>
    pub fn new() -> Self {
        Self::default()
    }

    /// Constructs a config builder from the given `config_bag`, setting only fields stored in the
    /// config bag, but not those in runtime components.
    #[allow(unused)]
    pub(crate) fn from_config_bag(config_bag: &::aws_smithy_types::config_bag::ConfigBag) -> Self {
        let mut builder = Self::new();
        builder.set_stalled_stream_protection(
            config_bag
                .load::<crate::config::StalledStreamProtectionConfig>()
                .cloned(),
        );
        builder.set_retry_config(config_bag.load::<::aws_smithy_types::retry::RetryConfig>().cloned());
        builder.set_timeout_config(config_bag.load::<::aws_smithy_types::timeout::TimeoutConfig>().cloned());
        builder.set_retry_partition(
            config_bag
                .load::<::aws_smithy_runtime::client::retries::RetryPartition>()
                .cloned(),
        );
        builder.set_app_name(config_bag.load::<::aws_types::app_name::AppName>().cloned());
        builder
    }

    /// Set the [`StalledStreamProtectionConfig`](crate::config::StalledStreamProtectionConfig)
    /// to configure protection for stalled streams.
    pub fn stalled_stream_protection(
        mut self,
        stalled_stream_protection_config: crate::config::StalledStreamProtectionConfig,
    ) -> Self {
        self.set_stalled_stream_protection(::std::option::Option::Some(stalled_stream_protection_config));
        self
    }

    /// Set the [`StalledStreamProtectionConfig`](crate::config::StalledStreamProtectionConfig)
    /// to configure protection for stalled streams.
    pub fn set_stalled_stream_protection(
        &mut self,
        stalled_stream_protection_config: ::std::option::Option<crate::config::StalledStreamProtectionConfig>,
    ) -> &mut Self {
        self.config.store_or_unset(stalled_stream_protection_config);
        self
    }

    /// Sets the idempotency token provider to use for service calls that require tokens.
    pub fn idempotency_token_provider(
        mut self,
        idempotency_token_provider: impl ::std::convert::Into<crate::idempotency_token::IdempotencyTokenProvider>,
    ) -> Self {
        self.set_idempotency_token_provider(::std::option::Option::Some(idempotency_token_provider.into()));
        self
    }

    /// Sets the idempotency token provider to use for service calls that require tokens.
    pub fn set_idempotency_token_provider(
        &mut self,
        idempotency_token_provider: ::std::option::Option<crate::idempotency_token::IdempotencyTokenProvider>,
    ) -> &mut Self {
        self.config.store_or_unset(idempotency_token_provider);
        self
    }

    /// Sets the HTTP client to use when making requests.
    ///
    /// # Examples
    /// ```no_run
    /// # #[cfg(test)]
    /// # mod tests {
    /// # #[test]
    /// # fn example() {
    /// use std::time::Duration;
    ///
    /// use amzn_codewhisperer_client::config::Config;
    /// use aws_smithy_runtime::client::http::hyper_014::HyperClientBuilder;
    ///
    /// let https_connector = hyper_rustls::HttpsConnectorBuilder::new()
    ///     .with_webpki_roots()
    ///     .https_only()
    ///     .enable_http1()
    ///     .enable_http2()
    ///     .build();
    /// let hyper_client = HyperClientBuilder::new().build(https_connector);
    ///
    /// // This connector can then be given to a generated service Config
    /// let config = my_service_client::Config::builder()
    ///     .endpoint_url("https://example.com")
    ///     .http_client(hyper_client)
    ///     .build();
    /// let client = my_service_client::Client::from_conf(config);
    /// # }
    /// # }
    /// ```
    pub fn http_client(mut self, http_client: impl crate::config::HttpClient + 'static) -> Self {
        self.set_http_client(::std::option::Option::Some(crate::config::IntoShared::into_shared(
            http_client,
        )));
        self
    }

    /// Sets the HTTP client to use when making requests.
    ///
    /// # Examples
    /// ```no_run
    /// # #[cfg(test)]
    /// # mod tests {
    /// # #[test]
    /// # fn example() {
    /// use std::time::Duration;
    ///
    /// use amzn_codewhisperer_client::config::{
    ///     Builder,
    ///     Config,
    /// };
    /// use aws_smithy_runtime::client::http::hyper_014::HyperClientBuilder;
    ///
    /// fn override_http_client(builder: &mut Builder) {
    ///     let https_connector = hyper_rustls::HttpsConnectorBuilder::new()
    ///         .with_webpki_roots()
    ///         .https_only()
    ///         .enable_http1()
    ///         .enable_http2()
    ///         .build();
    ///     let hyper_client = HyperClientBuilder::new().build(https_connector);
    ///     builder.set_http_client(Some(hyper_client));
    /// }
    ///
    /// let mut builder = amzn_codewhisperer_client::Config::builder();
    /// override_http_client(&mut builder);
    /// let config = builder.build();
    /// # }
    /// # }
    /// ```
    pub fn set_http_client(&mut self, http_client: Option<crate::config::SharedHttpClient>) -> &mut Self {
        self.runtime_components.set_http_client(http_client);
        self
    }

    /// Sets the bearer token that will be used for HTTP bearer auth.
    pub fn bearer_token(self, bearer_token: crate::config::Token) -> Self {
        self.bearer_token_resolver(bearer_token)
    }

    /// Sets a bearer token provider that will be used for HTTP bearer auth.
    pub fn bearer_token_resolver(
        mut self,
        bearer_token_resolver: impl crate::config::ResolveIdentity + 'static,
    ) -> Self {
        self.runtime_components.set_identity_resolver(
            ::aws_smithy_runtime_api::client::auth::http::HTTP_BEARER_AUTH_SCHEME_ID,
            ::aws_smithy_runtime_api::shared::IntoShared::<
                ::aws_smithy_runtime_api::client::identity::SharedIdentityResolver,
            >::into_shared(bearer_token_resolver),
        );
        self
    }

    /// Set the endpoint URL to use when making requests.
    ///
    /// Note: setting an endpoint URL will replace any endpoint resolver that has been set.
    ///
    /// # Panics
    /// Panics if an invalid URL is given.
    pub fn endpoint_url(mut self, endpoint_url: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.set_endpoint_url(::std::option::Option::Some(endpoint_url.into()));
        self
    }

    /// Set the endpoint URL to use when making requests.
    ///
    /// Note: setting an endpoint URL will replace any endpoint resolver that has been set.
    ///
    /// # Panics
    /// Panics if an invalid URL is given.
    pub fn set_endpoint_url(&mut self, endpoint_url: ::std::option::Option<::std::string::String>) -> &mut Self {
        #[allow(deprecated)]
        self.set_endpoint_resolver(endpoint_url.map(|url| {
            ::aws_smithy_runtime_api::shared::IntoShared::into_shared(
                ::aws_smithy_runtime::client::orchestrator::endpoints::StaticUriEndpointResolver::uri(url),
            )
        }));
        self
    }

    /// Sets the endpoint resolver to use when making requests.
    ///
    /// This service does not define a default endpoint resolver.
    ///
    /// Note: setting an endpoint resolver will replace any endpoint URL that has been set.
    /// This method accepts an endpoint resolver [specific to this
    /// service](crate::config::endpoint::ResolveEndpoint). If you want to provide a shared
    /// endpoint resolver, use [`Self::set_endpoint_resolver`].
    ///
    /// # Examples
    /// Create a custom endpoint resolver that resolves a different endpoing per-stage, e.g. staging
    /// vs. production. ```no_run
    /// use amzn_codewhisperer_client::config::endpoint::{
    ///     Endpoint,
    ///     EndpointFuture,
    ///     Params,
    ///     ResolveEndpoint,
    /// };
    /// #[derive(Debug)]
    /// struct StageResolver {
    ///     stage: String,
    /// }
    /// impl ResolveEndpoint for StageResolver {
    ///     fn resolve_endpoint(&self, params: &Params) -> EndpointFuture<'_> {
    ///         let stage = &self.stage;
    ///         EndpointFuture::ready(Ok(Endpoint::builder()
    ///             .url(format!("{stage}.myservice.com"))
    ///             .build()))
    ///     }
    /// }
    /// let resolver = StageResolver {
    ///     stage: std::env::var("STAGE").unwrap(),
    /// };
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .endpoint_resolver(resolver)
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    pub fn endpoint_resolver(
        mut self,
        endpoint_resolver: impl crate::config::endpoint::ResolveEndpoint + 'static,
    ) -> Self {
        self.set_endpoint_resolver(::std::option::Option::Some(endpoint_resolver.into_shared_resolver()));
        self
    }

    /// Sets the endpoint resolver to use when making requests.
    ///
    /// This service does not define a default endpoint resolver.
    pub fn set_endpoint_resolver(
        &mut self,
        endpoint_resolver: ::std::option::Option<::aws_smithy_runtime_api::client::endpoint::SharedEndpointResolver>,
    ) -> &mut Self {
        self.runtime_components.set_endpoint_resolver(endpoint_resolver);
        self
    }

    /// Set the retry_config for the builder
    ///
    /// # Examples
    /// ```no_run
    /// use amzn_codewhisperer_client::config::Config;
    /// use amzn_codewhisperer_client::config::retry::RetryConfig;
    ///
    /// let retry_config = RetryConfig::standard().with_max_attempts(5);
    /// let config = Config::builder().retry_config(retry_config).build();
    /// ```
    pub fn retry_config(mut self, retry_config: ::aws_smithy_types::retry::RetryConfig) -> Self {
        self.set_retry_config(Some(retry_config));
        self
    }

    /// Set the retry_config for the builder
    ///
    /// # Examples
    /// ```no_run
    /// use amzn_codewhisperer_client::config::retry::RetryConfig;
    /// use amzn_codewhisperer_client::config::{
    ///     Builder,
    ///     Config,
    /// };
    ///
    /// fn disable_retries(builder: &mut Builder) {
    ///     let retry_config = RetryConfig::standard().with_max_attempts(1);
    ///     builder.set_retry_config(Some(retry_config));
    /// }
    ///
    /// let mut builder = Config::builder();
    /// disable_retries(&mut builder);
    /// let config = builder.build();
    /// ```
    pub fn set_retry_config(
        &mut self,
        retry_config: ::std::option::Option<::aws_smithy_types::retry::RetryConfig>,
    ) -> &mut Self {
        retry_config.map(|r| self.config.store_put(r));
        self
    }

    /// Set the sleep_impl for the builder
    ///
    /// # Examples
    ///
    /// ```no_run
    /// use amzn_codewhisperer_client::config::{
    ///     AsyncSleep,
    ///     Config,
    ///     SharedAsyncSleep,
    ///     Sleep,
    /// };
    ///
    /// #[derive(Debug)]
    /// pub struct ForeverSleep;
    ///
    /// impl AsyncSleep for ForeverSleep {
    ///     fn sleep(&self, duration: std::time::Duration) -> Sleep {
    ///         Sleep::new(std::future::pending())
    ///     }
    /// }
    ///
    /// let sleep_impl = SharedAsyncSleep::new(ForeverSleep);
    /// let config = Config::builder().sleep_impl(sleep_impl).build();
    /// ```
    pub fn sleep_impl(mut self, sleep_impl: impl crate::config::AsyncSleep + 'static) -> Self {
        self.set_sleep_impl(Some(::aws_smithy_runtime_api::shared::IntoShared::into_shared(
            sleep_impl,
        )));
        self
    }

    /// Set the sleep_impl for the builder
    ///
    /// # Examples
    ///
    /// ```no_run
    /// use amzn_codewhisperer_client::config::{
    ///     AsyncSleep,
    ///     Builder,
    ///     Config,
    ///     SharedAsyncSleep,
    ///     Sleep,
    /// };
    ///
    /// #[derive(Debug)]
    /// pub struct ForeverSleep;
    ///
    /// impl AsyncSleep for ForeverSleep {
    ///     fn sleep(&self, duration: std::time::Duration) -> Sleep {
    ///         Sleep::new(std::future::pending())
    ///     }
    /// }
    ///
    /// fn set_never_ending_sleep_impl(builder: &mut Builder) {
    ///     let sleep_impl = SharedAsyncSleep::new(ForeverSleep);
    ///     builder.set_sleep_impl(Some(sleep_impl));
    /// }
    ///
    /// let mut builder = Config::builder();
    /// set_never_ending_sleep_impl(&mut builder);
    /// let config = builder.build();
    /// ```
    pub fn set_sleep_impl(&mut self, sleep_impl: ::std::option::Option<crate::config::SharedAsyncSleep>) -> &mut Self {
        self.runtime_components.set_sleep_impl(sleep_impl);
        self
    }

    /// Set the timeout_config for the builder
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use std::time::Duration;
    /// use amzn_codewhisperer_client::config::Config;
    /// use amzn_codewhisperer_client::config::timeout::TimeoutConfig;
    ///
    /// let timeout_config = TimeoutConfig::builder()
    ///     .operation_attempt_timeout(Duration::from_secs(1))
    ///     .build();
    /// let config = Config::builder().timeout_config(timeout_config).build();
    /// ```
    pub fn timeout_config(mut self, timeout_config: ::aws_smithy_types::timeout::TimeoutConfig) -> Self {
        self.set_timeout_config(Some(timeout_config));
        self
    }

    /// Set the timeout_config for the builder.
    ///
    /// Setting this to `None` has no effect if another source of configuration has set timeouts. If
    /// you are attempting to disable timeouts, use
    /// [`TimeoutConfig::disabled`](::aws_smithy_types::timeout::TimeoutConfig::disabled)
    ///
    ///
    /// # Examples
    ///
    /// ```no_run
    /// # use std::time::Duration;
    /// use amzn_codewhisperer_client::config::timeout::TimeoutConfig;
    /// use amzn_codewhisperer_client::config::{
    ///     Builder,
    ///     Config,
    /// };
    ///
    /// fn set_request_timeout(builder: &mut Builder) {
    ///     let timeout_config = TimeoutConfig::builder()
    ///         .operation_attempt_timeout(Duration::from_secs(1))
    ///         .build();
    ///     builder.set_timeout_config(Some(timeout_config));
    /// }
    ///
    /// let mut builder = Config::builder();
    /// set_request_timeout(&mut builder);
    /// let config = builder.build();
    /// ```
    pub fn set_timeout_config(
        &mut self,
        timeout_config: ::std::option::Option<::aws_smithy_types::timeout::TimeoutConfig>,
    ) -> &mut Self {
        // passing None has no impact.
        let Some(mut timeout_config) = timeout_config else {
            return self;
        };

        if let Some(base) = self.config.load::<::aws_smithy_types::timeout::TimeoutConfig>() {
            timeout_config.take_defaults_from(base);
        }
        self.config.store_put(timeout_config);
        self
    }

    /// Set the partition for retry-related state. When clients share a retry partition, they will
    /// also share things like token buckets and client rate limiters. By default, all clients
    /// for the same service will share a partition.
    pub fn retry_partition(mut self, retry_partition: ::aws_smithy_runtime::client::retries::RetryPartition) -> Self {
        self.set_retry_partition(Some(retry_partition));
        self
    }

    /// Set the partition for retry-related state. When clients share a retry partition, they will
    /// also share things like token buckets and client rate limiters. By default, all clients
    /// for the same service will share a partition.
    pub fn set_retry_partition(
        &mut self,
        retry_partition: ::std::option::Option<::aws_smithy_runtime::client::retries::RetryPartition>,
    ) -> &mut Self {
        retry_partition.map(|r| self.config.store_put(r));
        self
    }

    /// Set the identity cache for auth.
    ///
    /// The identity cache defaults to a lazy caching implementation that will resolve
    /// an identity when it is requested, and place it in the cache thereafter. Subsequent
    /// requests will take the value from the cache while it is still valid. Once it expires,
    /// the next request will result in refreshing the identity.
    ///
    /// This configuration allows you to disable or change the default caching mechanism.
    /// To use a custom caching mechanism, implement the
    /// [`ResolveCachedIdentity`](crate::config::ResolveCachedIdentity) trait and pass that
    /// implementation into this function.
    ///
    /// # Examples
    ///
    /// Disabling identity caching:
    /// ```no_run
    /// use amzn_codewhisperer_client::config::IdentityCache;
    ///
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .identity_cache(IdentityCache::no_cache())
    ///     // ...
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    ///
    /// Customizing lazy caching:
    /// ```no_run
    /// use std::time::Duration;
    ///
    /// use amzn_codewhisperer_client::config::IdentityCache;
    ///
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .identity_cache(
    ///         IdentityCache::lazy()
    ///             // change the load timeout to 10 seconds
    ///             .load_timeout(Duration::from_secs(10))
    ///             .build()
    ///     )
    ///     // ...
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    pub fn identity_cache(mut self, identity_cache: impl crate::config::ResolveCachedIdentity + 'static) -> Self {
        self.set_identity_cache(identity_cache);
        self
    }

    /// Set the identity cache for auth.
    ///
    /// The identity cache defaults to a lazy caching implementation that will resolve
    /// an identity when it is requested, and place it in the cache thereafter. Subsequent
    /// requests will take the value from the cache while it is still valid. Once it expires,
    /// the next request will result in refreshing the identity.
    ///
    /// This configuration allows you to disable or change the default caching mechanism.
    /// To use a custom caching mechanism, implement the
    /// [`ResolveCachedIdentity`](crate::config::ResolveCachedIdentity) trait and pass that
    /// implementation into this function.
    ///
    /// # Examples
    ///
    /// Disabling identity caching:
    /// ```no_run
    /// use amzn_codewhisperer_client::config::IdentityCache;
    ///
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .identity_cache(IdentityCache::no_cache())
    ///     // ...
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    ///
    /// Customizing lazy caching:
    /// ```no_run
    /// use std::time::Duration;
    ///
    /// use amzn_codewhisperer_client::config::IdentityCache;
    ///
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .identity_cache(
    ///         IdentityCache::lazy()
    ///             // change the load timeout to 10 seconds
    ///             .load_timeout(Duration::from_secs(10))
    ///             .build()
    ///     )
    ///     // ...
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    pub fn set_identity_cache(
        &mut self,
        identity_cache: impl crate::config::ResolveCachedIdentity + 'static,
    ) -> &mut Self {
        self.runtime_components
            .set_identity_cache(::std::option::Option::Some(identity_cache));
        self
    }

    /// Add an [interceptor](crate::config::Intercept) that runs at specific stages of the request
    /// execution pipeline.
    ///
    /// Interceptors targeted at a certain stage are executed according to the pre-defined priority.
    /// The SDK provides a default set of interceptors. An interceptor configured by this method
    /// will run after those default interceptors.
    ///
    /// # Examples
    /// ```no_run
    /// # #[cfg(test)]
    /// # mod tests {
    /// # #[test]
    /// # fn example() {
    /// use amzn_codewhisperer_client::config::Config;
    /// use aws_smithy_runtime_api::client::interceptors::context::phase::BeforeTransmit;
    /// use aws_smithy_runtime_api::client::interceptors::{
    ///     Interceptor,
    ///     InterceptorContext,
    /// };
    /// use aws_smithy_types::config_bag::ConfigBag;
    ///
    /// fn base_url() -> String {
    ///     // ...
    ///     # String::new()
    /// }
    ///
    /// #[derive(Debug)]
    /// pub struct UriModifierInterceptor;
    /// impl Intercept for UriModifierInterceptor {
    ///     fn modify_before_signing(
    ///         &self,
    ///         context: &mut InterceptorContext<BeforeTransmit>,
    ///         _cfg: &mut ConfigBag,
    ///     ) -> Result<(), aws_smithy_runtime_api::client::interceptors::BoxError> {
    ///         let request = context.request_mut();
    ///         let uri = format!("{}{}", base_url(), request.uri().path());
    ///         *request.uri_mut() = uri.parse()?;
    ///
    ///         Ok(())
    ///     }
    /// }
    ///
    /// let config = Config::builder()
    ///     .interceptor(UriModifierInterceptor)
    ///     .build();
    /// # }
    /// # }
    /// ```
    pub fn interceptor(mut self, interceptor: impl crate::config::Intercept + 'static) -> Self {
        self.push_interceptor(crate::config::SharedInterceptor::new(interceptor));
        self
    }

    /// Add a [`SharedInterceptor`](crate::config::SharedInterceptor) that runs at specific stages
    /// of the request execution pipeline.
    ///
    /// Interceptors targeted at a certain stage are executed according to the pre-defined priority.
    /// The SDK provides a default set of interceptors. An interceptor configured by this method
    /// will run after those default interceptors.
    ///
    /// # Examples
    /// ```no_run
    /// # #[cfg(test)]
    /// # mod tests {
    /// # #[test]
    /// # fn example() {
    /// use amzn_codewhisperer_client::config::{
    ///     Builder,
    ///     Config,
    /// };
    /// use aws_smithy_runtime_api::client::interceptors::context::phase::BeforeTransmit;
    /// use aws_smithy_runtime_api::client::interceptors::{
    ///     Interceptor,
    ///     InterceptorContext,
    ///     SharedInterceptor,
    /// };
    /// use aws_smithy_types::config_bag::ConfigBag;
    ///
    /// fn base_url() -> String {
    ///     // ...
    ///     # String::new()
    /// }
    ///
    /// fn modify_request_uri(builder: &mut Builder) {
    ///     #[derive(Debug)]
    ///     pub struct UriModifierInterceptor;
    ///     impl Intercept for UriModifierInterceptor {
    ///         fn modify_before_signing(
    ///             &self,
    ///             context: &mut InterceptorContext<BeforeTransmit>,
    ///             _cfg: &mut ConfigBag,
    ///         ) -> Result<(), aws_smithy_runtime_api::client::interceptors::BoxError> {
    ///             let request = context.request_mut();
    ///             let uri = format!("{}{}", base_url(), request.uri().path());
    ///             *request.uri_mut() = uri.parse()?;
    ///
    ///             Ok(())
    ///         }
    ///     }
    ///     builder.push_interceptor(SharedInterceptor::new(UriModifierInterceptor));
    /// }
    ///
    /// let mut builder = Config::builder();
    /// modify_request_uri(&mut builder);
    /// let config = builder.build();
    /// # }
    /// # }
    /// ```
    pub fn push_interceptor(&mut self, interceptor: crate::config::SharedInterceptor) -> &mut Self {
        self.runtime_components.push_interceptor(interceptor);
        self
    }

    /// Set [`SharedInterceptor`](crate::config::SharedInterceptor)s for the builder.
    pub fn set_interceptors(
        &mut self,
        interceptors: impl IntoIterator<Item = crate::config::SharedInterceptor>,
    ) -> &mut Self {
        self.runtime_components.set_interceptors(interceptors.into_iter());
        self
    }

    /// Sets the time source used for this service
    pub fn time_source(mut self, time_source: impl ::aws_smithy_async::time::TimeSource + 'static) -> Self {
        self.set_time_source(::std::option::Option::Some(
            ::aws_smithy_runtime_api::shared::IntoShared::into_shared(time_source),
        ));
        self
    }

    /// Sets the time source used for this service
    pub fn set_time_source(
        &mut self,
        time_source: ::std::option::Option<::aws_smithy_async::time::SharedTimeSource>,
    ) -> &mut Self {
        self.runtime_components.set_time_source(time_source);
        self
    }

    /// Add type implementing
    /// [`ClassifyRetry`](::aws_smithy_runtime_api::client::retries::classifiers::ClassifyRetry)
    /// that will be used by the
    /// [`RetryStrategy`](::aws_smithy_runtime_api::client::retries::RetryStrategy) to determine
    /// what responses should be retried.
    ///
    /// A retry classifier configured by this method will run according to its
    /// [priority](::aws_smithy_runtime_api::client::retries::classifiers::RetryClassifierPriority).
    ///
    /// # Examples
    /// ```no_run
    /// # #[cfg(test)]
    /// # mod tests {
    /// # #[test]
    /// # fn example() {
    /// use aws_smithy_runtime_api::client::interceptors::context::InterceptorContext;
    /// use aws_smithy_runtime_api::client::orchestrator::OrchestratorError;
    /// use aws_smithy_runtime_api::client::retries::classifiers::{
    ///     ClassifyRetry, RetryAction, RetryClassifierPriority,
    /// };
    /// use aws_smithy_types::error::metadata::ProvideErrorMetadata;
    /// use aws_smithy_types::retry::ErrorKind;
    /// use std::error::Error as StdError;
    /// use std::marker::PhantomData;
    /// use amzn_codewhisperer_client::config::Config;
    /// # struct SomeOperationError {}
    ///
    /// const RETRYABLE_ERROR_CODES: &[&str] = [
    ///     // List error codes to be retried here...
    /// ];
    ///
    /// // When classifying at an operation's error type, classifiers require a generic parameter.
    /// // When classifying the HTTP response alone, no generic is needed.
    /// #[derive(Debug, Default)]
    /// pub struct ErrorCodeClassifier<E> {
    ///     _inner: PhantomData<E>,
    /// }
    ///
    /// impl<E> ExampleErrorCodeClassifier<E> {
    ///     pub fn new() -> Self {
    ///         Self {
    ///             _inner: PhantomData,
    ///         }
    ///     }
    /// }
    ///
    /// impl<E> ClassifyRetry for ExampleErrorCodeClassifier<E>
    /// where
    ///     // Adding a trait bound for ProvideErrorMetadata allows us to inspect the error code.
    ///     E: StdError + ProvideErrorMetadata + Send + Sync + 'static,
    /// {
    ///     fn classify_retry(&self, ctx: &InterceptorContext) -> RetryAction {
    ///         // Check for a result
    ///         let output_or_error = ctx.output_or_error();
    ///         // Check for an error
    ///         let error = match output_or_error {
    ///             Some(Ok(_)) | None => return RetryAction::NoActionIndicated,
    ///               Some(Err(err)) => err,
    ///         };
    ///
    ///         // Downcast the generic error and extract the code
    ///         let error_code = OrchestratorError::as_operation_error(error)
    ///             .and_then(|err| err.downcast_ref::<E>())
    ///             .and_then(|err| err.code());
    ///
    ///         // If this error's code is in our list, return an action that tells the RetryStrategy to retry this request.
    ///         if let Some(error_code) = error_code {
    ///             if RETRYABLE_ERROR_CODES.contains(&error_code) {
    ///                 return RetryAction::transient_error();
    ///             }
    ///         }
    ///
    ///         // Otherwise, return that no action is indicated i.e. that this classifier doesn't require a retry.
    ///         // Another classifier may still classify this response as retryable.
    ///         RetryAction::NoActionIndicated
    ///     }
    ///
    ///     fn name(&self) -> &'static str { "Example Error Code Classifier" }
    /// }
    ///
    /// let config = Config::builder()
    ///     .retry_classifier(ExampleErrorCodeClassifier::<SomeOperationError>::new())
    ///     .build();
    /// # }
    /// # }
    /// ```
    pub fn retry_classifier(
        mut self,
        retry_classifier: impl ::aws_smithy_runtime_api::client::retries::classifiers::ClassifyRetry + 'static,
    ) -> Self {
        self.push_retry_classifier(
            ::aws_smithy_runtime_api::client::retries::classifiers::SharedRetryClassifier::new(retry_classifier),
        );
        self
    }

    /// Add a [`SharedRetryClassifier`](::aws_smithy_runtime_api::client::retries::classifiers::SharedRetryClassifier) that will be used by the
    /// [`RetryStrategy`](::aws_smithy_runtime_api::client::retries::RetryStrategy) to determine
    /// what responses should be retried.
    ///
    /// A retry classifier configured by this method will run according to its priority.
    ///
    /// # Examples
    /// ```no_run
    /// # #[cfg(test)]
    /// # mod tests {
    /// # #[test]
    /// # fn example() {
    /// use aws_smithy_runtime_api::client::interceptors::context::InterceptorContext;
    /// use aws_smithy_runtime_api::client::orchestrator::OrchestratorError;
    /// use aws_smithy_runtime_api::client::retries::classifiers::{
    ///     ClassifyRetry, RetryAction, RetryClassifierPriority,
    /// };
    /// use aws_smithy_types::error::metadata::ProvideErrorMetadata;
    /// use aws_smithy_types::retry::ErrorKind;
    /// use std::error::Error as StdError;
    /// use std::marker::PhantomData;
    /// use amzn_codewhisperer_client::config::{Builder, Config};
    /// # struct SomeOperationError {}
    ///
    /// const RETRYABLE_ERROR_CODES: &[&str] = [
    ///     // List error codes to be retried here...
    /// ];
    /// fn set_example_error_code_classifier(builder: &mut Builder) {
    ///     // When classifying at an operation's error type, classifiers require a generic parameter.
    ///     // When classifying the HTTP response alone, no generic is needed.
    ///     #[derive(Debug, Default)]
    ///     pub struct ExampleErrorCodeClassifier<E> {
    ///         _inner: PhantomData<E>,
    ///     }
    ///
    ///     impl<E> ExampleErrorCodeClassifier<E> {
    ///         pub fn new() -> Self {
    ///             Self {
    ///                 _inner: PhantomData,
    ///             }
    ///         }
    ///     }
    ///
    ///     impl<E> ClassifyRetry for ExampleErrorCodeClassifier<E>
    ///     where
    ///         // Adding a trait bound for ProvideErrorMetadata allows us to inspect the error code.
    ///         E: StdError + ProvideErrorMetadata + Send + Sync + 'static,
    ///     {
    ///         fn classify_retry(&self, ctx: &InterceptorContext) -> RetryAction {
    ///             // Check for a result
    ///             let output_or_error = ctx.output_or_error();
    ///             // Check for an error
    ///             let error = match output_or_error {
    ///                 Some(Ok(_)) | None => return RetryAction::NoActionIndicated,
    ///                   Some(Err(err)) => err,
    ///             };
    ///
    ///             // Downcast the generic error and extract the code
    ///             let error_code = OrchestratorError::as_operation_error(error)
    ///                 .and_then(|err| err.downcast_ref::<E>())
    ///                 .and_then(|err| err.code());
    ///
    ///             // If this error's code is in our list, return an action that tells the RetryStrategy to retry this request.
    ///             if let Some(error_code) = error_code {
    ///                 if RETRYABLE_ERROR_CODES.contains(&error_code) {
    ///                     return RetryAction::transient_error();
    ///                 }
    ///             }
    ///
    ///             // Otherwise, return that no action is indicated i.e. that this classifier doesn't require a retry.
    ///             // Another classifier may still classify this response as retryable.
    ///             RetryAction::NoActionIndicated
    ///         }
    ///
    ///         fn name(&self) -> &'static str { "Example Error Code Classifier" }
    ///     }
    ///
    ///     builder.push_retry_classifier(ExampleErrorCodeClassifier::<SomeOperationError>::new())
    /// }
    ///
    /// let mut builder = Config::builder();
    /// set_example_error_code_classifier(&mut builder);
    /// let config = builder.build();
    /// # }
    /// # }
    /// ```
    pub fn push_retry_classifier(
        &mut self,
        retry_classifier: ::aws_smithy_runtime_api::client::retries::classifiers::SharedRetryClassifier,
    ) -> &mut Self {
        self.runtime_components.push_retry_classifier(retry_classifier);
        self
    }

    /// Set [`SharedRetryClassifier`](::aws_smithy_runtime_api::client::retries::classifiers::SharedRetryClassifier)s for the builder, replacing any that
    /// were previously set.
    pub fn set_retry_classifiers(
        &mut self,
        retry_classifiers: impl IntoIterator<
            Item = ::aws_smithy_runtime_api::client::retries::classifiers::SharedRetryClassifier,
        >,
    ) -> &mut Self {
        self.runtime_components
            .set_retry_classifiers(retry_classifiers.into_iter());
        self
    }

    /// Sets the name of the app that is using the client.
    ///
    /// This _optional_ name is used to identify the application in the user agent that
    /// gets sent along with requests.
    pub fn app_name(mut self, app_name: ::aws_types::app_name::AppName) -> Self {
        self.set_app_name(Some(app_name));
        self
    }

    /// Sets the name of the app that is using the client.
    ///
    /// This _optional_ name is used to identify the application in the user agent that
    /// gets sent along with requests.
    pub fn set_app_name(&mut self, app_name: ::std::option::Option<::aws_types::app_name::AppName>) -> &mut Self {
        self.config.store_or_unset(app_name);
        self
    }

    /// Sets the access token provider for this service
    ///
    /// Note: the [`Self::bearer_token`] and [`Self::bearer_token_resolver`] methods are
    /// equivalent to this method, but take the [`Token`] and [`ResolveIdentity`] types
    /// respectively.
    ///
    /// [`Token`]: crate::config::Token
    /// [`ResolveIdentity`]: crate::config::ResolveIdentity
    pub fn token_provider(mut self, token_provider: impl crate::config::ProvideToken + 'static) -> Self {
        self.set_token_provider(::std::option::Option::Some(
            ::aws_smithy_runtime_api::shared::IntoShared::<crate::config::SharedTokenProvider>::into_shared(
                token_provider,
            ),
        ));
        self
    }

    /// Sets the access token provider for this service
    ///
    /// Note: the [`Self::bearer_token`] and [`Self::bearer_token_resolver`] methods are
    /// equivalent to this method, but take the [`Token`] and [`ResolveIdentity`] types
    /// respectively.
    ///
    /// [`Token`]: crate::config::Token
    /// [`ResolveIdentity`]: crate::config::ResolveIdentity
    pub fn set_token_provider(
        &mut self,
        token_provider: ::std::option::Option<crate::config::SharedTokenProvider>,
    ) -> &mut Self {
        if let Some(token_provider) = token_provider {
            self.runtime_components.set_identity_resolver(
                ::aws_smithy_runtime_api::client::auth::http::HTTP_BEARER_AUTH_SCHEME_ID,
                token_provider,
            );
        }
        self
    }

    /// Overrides the default invocation ID generator.
    ///
    /// The invocation ID generator generates ID values for the `amz-sdk-invocation-id` header. By
    /// default, this will be a random UUID. Overriding it may be useful in tests that examine the
    /// HTTP request and need to be deterministic.
    pub fn invocation_id_generator(
        mut self,
        gen: impl ::aws_runtime::invocation_id::InvocationIdGenerator + 'static,
    ) -> Self {
        self.set_invocation_id_generator(::std::option::Option::Some(
            ::aws_runtime::invocation_id::SharedInvocationIdGenerator::new(gen),
        ));
        self
    }

    /// Overrides the default invocation ID generator.
    ///
    /// The invocation ID generator generates ID values for the `amz-sdk-invocation-id` header. By
    /// default, this will be a random UUID. Overriding it may be useful in tests that examine the
    /// HTTP request and need to be deterministic.
    pub fn set_invocation_id_generator(
        &mut self,
        gen: ::std::option::Option<::aws_runtime::invocation_id::SharedInvocationIdGenerator>,
    ) -> &mut Self {
        self.config.store_or_unset(gen);
        self
    }

    /// Sets the [`behavior major version`](crate::config::BehaviorVersion).
    ///
    /// Over time, new best-practice behaviors are introduced. However, these behaviors might not be
    /// backwards compatible. For example, a change which introduces new default timeouts or a
    /// new retry-mode for all operations might be the ideal behavior but could break existing
    /// applications.
    ///
    /// # Examples
    ///
    /// Set the behavior major version to `latest`. This is equivalent to enabling the
    /// `behavior-version-latest` cargo feature. ```no_run
    /// use amzn_codewhisperer_client::config::BehaviorVersion;
    ///
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .behavior_version(BehaviorVersion::latest())
    ///     // ...
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    /// 
    /// Customizing behavior major version:
    /// ```no_run
    /// use amzn_codewhisperer_client::config::BehaviorVersion;
    ///
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .behavior_version(BehaviorVersion::v2023_11_09())
    ///     // ...
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    pub fn behavior_version(mut self, behavior_version: crate::config::BehaviorVersion) -> Self {
        self.set_behavior_version(Some(behavior_version));
        self
    }

    /// Sets the [`behavior major version`](crate::config::BehaviorVersion).
    ///
    /// Over time, new best-practice behaviors are introduced. However, these behaviors might not be
    /// backwards compatible. For example, a change which introduces new default timeouts or a
    /// new retry-mode for all operations might be the ideal behavior but could break existing
    /// applications.
    ///
    /// # Examples
    ///
    /// Set the behavior major version to `latest`. This is equivalent to enabling the
    /// `behavior-version-latest` cargo feature. ```no_run
    /// use amzn_codewhisperer_client::config::BehaviorVersion;
    ///
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .behavior_version(BehaviorVersion::latest())
    ///     // ...
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    /// 
    /// Customizing behavior major version:
    /// ```no_run
    /// use amzn_codewhisperer_client::config::BehaviorVersion;
    ///
    /// let config = amzn_codewhisperer_client::Config::builder()
    ///     .behavior_version(BehaviorVersion::v2023_11_09())
    ///     // ...
    ///     .build();
    /// let client = amzn_codewhisperer_client::Client::from_conf(config);
    /// ```
    pub fn set_behavior_version(&mut self, behavior_version: Option<crate::config::BehaviorVersion>) -> &mut Self {
        self.behavior_version = behavior_version;
        self
    }

    /// Convenience method to set the latest behavior major version
    ///
    /// This is equivalent to enabling the `behavior-version-latest` Cargo feature
    pub fn behavior_version_latest(mut self) -> Self {
        self.set_behavior_version(Some(crate::config::BehaviorVersion::latest()));
        self
    }

    /// Adds a runtime plugin to the config.
    pub fn runtime_plugin(mut self, plugin: impl crate::config::RuntimePlugin + 'static) -> Self {
        self.push_runtime_plugin(crate::config::SharedRuntimePlugin::new(plugin));
        self
    }

    /// Adds a runtime plugin to the config.
    pub fn push_runtime_plugin(&mut self, plugin: crate::config::SharedRuntimePlugin) -> &mut Self {
        self.runtime_plugins.push(plugin);
        self
    }

    #[cfg(any(feature = "test-util", test))]
    #[allow(unused_mut)]
    /// Apply test defaults to the builder
    pub fn apply_test_defaults(&mut self) -> &mut Self {
        self.set_idempotency_token_provider(Some("00000000-0000-4000-8000-000000000000".into()));
        self.set_time_source(::std::option::Option::Some(
            ::aws_smithy_async::time::SharedTimeSource::new(::aws_smithy_async::time::StaticTimeSource::new(
                ::std::time::UNIX_EPOCH + ::std::time::Duration::from_secs(**********),
            )),
        ));
        self.config
            .store_put(::aws_runtime::user_agent::AwsUserAgent::for_tests());
        self.set_token_provider(Some(crate::config::SharedTokenProvider::new(
            ::aws_credential_types::Token::for_tests(),
        )));
        self.behavior_version = ::std::option::Option::Some(crate::config::BehaviorVersion::latest());
        self
    }

    #[cfg(any(feature = "test-util", test))]
    #[allow(unused_mut)]
    /// Apply test defaults to the builder
    pub fn with_test_defaults(mut self) -> Self {
        self.apply_test_defaults();
        self
    }

    /// Builds a [`Config`].
    #[allow(unused_mut)]
    pub fn build(mut self) -> Config {
        let mut layer = self.config;
        if self.runtime_components.time_source().is_none() {
            self.runtime_components
                .set_time_source(::std::option::Option::Some(::std::default::Default::default()));
        }
        layer.store_put(crate::meta::API_METADATA.clone());
        Config {
            config: crate::config::Layer::from(layer.clone())
                .with_name("amzn_codewhisperer_client::config::Config")
                .freeze(),
            cloneable: layer,
            runtime_components: self.runtime_components,
            runtime_plugins: self.runtime_plugins,
            behavior_version: self.behavior_version,
        }
    }
}
#[derive(::std::fmt::Debug)]
pub(crate) struct ServiceRuntimePlugin {
    config: ::std::option::Option<::aws_smithy_types::config_bag::FrozenLayer>,
    runtime_components: ::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder,
}

impl ServiceRuntimePlugin {
    pub fn new(_service_config: crate::config::Config) -> Self {
        let config = {
            let mut cfg = ::aws_smithy_types::config_bag::Layer::new("AmazonCodeWhispererService");
            cfg.store_put(crate::idempotency_token::default_provider());
            ::std::option::Option::Some(cfg.freeze())
        };
        let mut runtime_components =
            ::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder::new("ServiceRuntimePlugin");
        runtime_components.push_auth_scheme(::aws_smithy_runtime_api::client::auth::SharedAuthScheme::new(
            ::aws_smithy_runtime::client::auth::http::BearerAuthScheme::new(),
        ));
        runtime_components.push_interceptor(
            ::aws_smithy_runtime::client::http::connection_poisoning::ConnectionPoisoningInterceptor::new(),
        );
        runtime_components.push_retry_classifier(
            ::aws_smithy_runtime::client::retries::classifiers::HttpStatusCodeClassifier::default(),
        );
        runtime_components
            .push_interceptor(crate::sdk_feature_tracker::retry_mode::RetryModeFeatureTrackerInterceptor::new());
        runtime_components.push_interceptor(::aws_runtime::service_clock_skew::ServiceClockSkewInterceptor::new());
        runtime_components.push_interceptor(::aws_runtime::request_info::RequestInfoInterceptor::new());
        runtime_components.push_interceptor(::aws_runtime::user_agent::UserAgentInterceptor::new());
        runtime_components.push_interceptor(::aws_runtime::invocation_id::InvocationIdInterceptor::new());
        runtime_components.push_interceptor(::aws_runtime::recursion_detection::RecursionDetectionInterceptor::new());
        Self {
            config,
            runtime_components,
        }
    }
}

impl ::aws_smithy_runtime_api::client::runtime_plugin::RuntimePlugin for ServiceRuntimePlugin {
    fn config(&self) -> ::std::option::Option<::aws_smithy_types::config_bag::FrozenLayer> {
        self.config.clone()
    }

    fn order(&self) -> ::aws_smithy_runtime_api::client::runtime_plugin::Order {
        ::aws_smithy_runtime_api::client::runtime_plugin::Order::Defaults
    }

    fn runtime_components(
        &self,
        _: &::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder,
    ) -> ::std::borrow::Cow<'_, ::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder> {
        ::std::borrow::Cow::Borrowed(&self.runtime_components)
    }
}

/// Cross-operation shared-state singletons
/// A plugin that enables configuration for a single operation invocation
///
/// The `config` method will return a `FrozenLayer` by storing values from `config_override`.
/// In the case of default values requested, they will be obtained from `client_config`.
#[derive(Debug)]
pub(crate) struct ConfigOverrideRuntimePlugin {
    pub(crate) config: ::aws_smithy_types::config_bag::FrozenLayer,
    pub(crate) components: ::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder,
}

impl ConfigOverrideRuntimePlugin {
    #[allow(dead_code)] // unused when a service does not provide any operations
    pub(crate) fn new(
        config_override: Builder,
        initial_config: ::aws_smithy_types::config_bag::FrozenLayer,
        initial_components: &::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder,
    ) -> Self {
        let mut layer = config_override.config;
        let mut components = config_override.runtime_components;
        #[allow(unused_mut)]
        let mut resolver = ::aws_smithy_runtime::client::config_override::Resolver::overrid(
            initial_config,
            initial_components,
            &mut layer,
            &mut components,
        );

        let _ = resolver;
        Self {
            config: ::aws_smithy_types::config_bag::Layer::from(layer)
                .with_name("amzn_codewhisperer_client::config::ConfigOverrideRuntimePlugin")
                .freeze(),
            components,
        }
    }
}

impl ::aws_smithy_runtime_api::client::runtime_plugin::RuntimePlugin for ConfigOverrideRuntimePlugin {
    fn config(&self) -> ::std::option::Option<::aws_smithy_types::config_bag::FrozenLayer> {
        Some(self.config.clone())
    }

    fn runtime_components(
        &self,
        _: &::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder,
    ) -> ::std::borrow::Cow<'_, ::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder> {
        ::std::borrow::Cow::Borrowed(&self.components)
    }
}

pub use ::aws_smithy_runtime::client::identity::IdentityCache;
pub use ::aws_smithy_runtime_api::client::runtime_components::RuntimeComponents;
pub use ::aws_smithy_types::config_bag::ConfigBag;

impl From<&::aws_types::sdk_config::SdkConfig> for Builder {
    fn from(input: &::aws_types::sdk_config::SdkConfig) -> Self {
        let mut builder = Builder::default();
        // resiliency
        builder.set_retry_config(input.retry_config().cloned());
        builder.set_timeout_config(input.timeout_config().cloned());
        builder.set_sleep_impl(input.sleep_impl());

        builder.set_http_client(input.http_client());
        builder.set_time_source(input.time_source());
        builder.set_behavior_version(input.behavior_version());
        // setting `None` here removes the default
        if let Some(config) = input.stalled_stream_protection() {
            builder.set_stalled_stream_protection(Some(config));
        }

        if let Some(cache) = input.identity_cache() {
            builder.set_identity_cache(cache);
        }
        builder.set_token_provider(input.token_provider());
        builder.set_app_name(input.app_name().cloned());

        builder
    }
}

impl From<&::aws_types::sdk_config::SdkConfig> for Config {
    fn from(sdk_config: &::aws_types::sdk_config::SdkConfig) -> Self {
        Builder::from(sdk_config).build()
    }
}

pub use ::aws_types::app_name::AppName;

#[allow(dead_code)]
fn service_config_key<'a>(env: &'a str, profile: &'a str) -> aws_types::service_config::ServiceConfigKey<'a> {
    ::aws_types::service_config::ServiceConfigKey::builder()
        .service_id("codewhispererruntime")
        .env(env)
        .profile(profile)
        .build()
        .expect("all field sets explicitly, can't fail")
}

pub use ::aws_smithy_async::rt::sleep::Sleep;

pub(crate) fn base_client_runtime_plugins(
    mut config: crate::Config,
) -> ::aws_smithy_runtime_api::client::runtime_plugin::RuntimePlugins {
    let mut configured_plugins = ::std::vec::Vec::new();
    ::std::mem::swap(&mut config.runtime_plugins, &mut configured_plugins);
    #[cfg(feature = "behavior-version-latest")]
    {
        if config.behavior_version.is_none() {
            config.behavior_version =
                Some(::aws_smithy_runtime_api::client::behavior_version::BehaviorVersion::latest());
        }
    }

    let default_retry_partition = "codewhispererruntime";

    let mut plugins = ::aws_smithy_runtime_api::client::runtime_plugin::RuntimePlugins::new()
                        // defaults
                        .with_client_plugins(::aws_smithy_runtime::client::defaults::default_plugins(
                            ::aws_smithy_runtime::client::defaults::DefaultPluginParams::new()
                                .with_retry_partition_name(default_retry_partition)
                                .with_behavior_version(config.behavior_version.expect("Invalid client configuration: A behavior major version must be set when sending a request or constructing a client. You must set it during client construction or by enabling the `behavior-version-latest` cargo feature."))
                        ))
                        // user config
                        .with_client_plugin(
                            ::aws_smithy_runtime_api::client::runtime_plugin::StaticRuntimePlugin::new()
                                .with_config(config.config.clone())
                                .with_runtime_components(config.runtime_components.clone())
                        )
                        // codegen config
                        .with_client_plugin(crate::config::ServiceRuntimePlugin::new(config.clone()))
                        .with_client_plugin(::aws_smithy_runtime::client::auth::no_auth::NoAuthRuntimePlugin::new());

    for plugin in configured_plugins {
        plugins = plugins.with_client_plugin(plugin);
    }
    plugins
}

pub use ::aws_credential_types::provider::token::{
    ProvideToken,
    SharedTokenProvider,
};
pub use ::aws_smithy_async::rt::sleep::{
    AsyncSleep,
    SharedAsyncSleep,
};
pub use ::aws_smithy_runtime_api::client::behavior_version::BehaviorVersion;
pub use ::aws_smithy_runtime_api::client::http::{
    HttpClient,
    SharedHttpClient,
};
pub use ::aws_smithy_runtime_api::client::identity::http::Token;
pub use ::aws_smithy_runtime_api::client::identity::{
    ResolveCachedIdentity,
    ResolveIdentity,
    SharedIdentityCache,
};
pub use ::aws_smithy_runtime_api::client::interceptors::{
    Intercept,
    SharedInterceptor,
};
pub use ::aws_smithy_runtime_api::client::runtime_components::RuntimeComponentsBuilder;
pub use ::aws_smithy_runtime_api::client::runtime_plugin::{
    RuntimePlugin,
    SharedRuntimePlugin,
};
pub use ::aws_smithy_runtime_api::client::stalled_stream_protection::StalledStreamProtectionConfig;
pub use ::aws_smithy_runtime_api::shared::IntoShared;
pub use ::aws_smithy_types::config_bag::{
    FrozenLayer,
    Layer,
};

/// Types needed to configure endpoint resolution.
pub mod endpoint;

/// HTTP request and response types.
pub mod http;

/// Types needed to implement [`Intercept`](crate::config::Intercept).
pub mod interceptors;

/// Retry configuration.
pub mod retry;

/// Timeout configuration.
pub mod timeout;
