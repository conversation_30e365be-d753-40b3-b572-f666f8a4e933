// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub(crate) fn access_denied_exception_correct_errors(
    mut builder: crate::types::error::builders::AccessDeniedErrorBuilder,
) -> crate::types::error::builders::AccessDeniedErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn validation_exception_correct_errors(
    mut builder: crate::types::error::builders::ValidationErrorBuilder,
) -> crate::types::error::builders::ValidationErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn throttling_exception_correct_errors(
    mut builder: crate::types::error::builders::ThrottlingErrorBuilder,
) -> crate::types::error::builders::ThrottlingErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn internal_server_exception_correct_errors(
    mut builder: crate::types::error::builders::InternalServerErrorBuilder,
) -> crate::types::error::builders::InternalServerErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn create_artifact_upload_url_output_output_correct_errors(
    mut builder: crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlOutputBuilder,
) -> crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlOutputBuilder {
    if builder.upload_id.is_none() {
        builder.upload_id = Some(Default::default())
    }
    if builder.upload_url.is_none() {
        builder.upload_url = Some(Default::default())
    }
    builder
}

pub(crate) fn conflict_exception_correct_errors(
    mut builder: crate::types::error::builders::ConflictErrorBuilder,
) -> crate::types::error::builders::ConflictErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn create_subscription_token_output_output_correct_errors(
    mut builder: crate::operation::create_subscription_token::builders::CreateSubscriptionTokenOutputBuilder,
) -> crate::operation::create_subscription_token::builders::CreateSubscriptionTokenOutputBuilder {
    if builder.status.is_none() {
        builder.status = "no value was set".parse::<crate::types::SubscriptionStatus>().ok()
    }
    builder
}

pub(crate) fn service_quota_exceeded_exception_correct_errors(
    mut builder: crate::types::error::builders::ServiceQuotaExceededErrorBuilder,
) -> crate::types::error::builders::ServiceQuotaExceededErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn create_task_assist_conversation_output_output_correct_errors(
    mut builder: crate::operation::create_task_assist_conversation::builders::CreateTaskAssistConversationOutputBuilder,
) -> crate::operation::create_task_assist_conversation::builders::CreateTaskAssistConversationOutputBuilder {
    if builder.conversation_id.is_none() {
        builder.conversation_id = Some(Default::default())
    }
    builder
}

pub(crate) fn resource_not_found_exception_correct_errors(
    mut builder: crate::types::error::builders::ResourceNotFoundErrorBuilder,
) -> crate::types::error::builders::ResourceNotFoundErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn create_upload_url_output_output_correct_errors(
    mut builder: crate::operation::create_upload_url::builders::CreateUploadUrlOutputBuilder,
) -> crate::operation::create_upload_url::builders::CreateUploadUrlOutputBuilder {
    if builder.upload_id.is_none() {
        builder.upload_id = Some(Default::default())
    }
    if builder.upload_url.is_none() {
        builder.upload_url = Some(Default::default())
    }
    builder
}

pub(crate) fn create_user_memory_entry_output_output_correct_errors(
    mut builder: crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryOutputBuilder,
) -> crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryOutputBuilder {
    if builder.memory_entry.is_none() {
        builder.memory_entry = {
            let builder = crate::types::builders::MemoryEntryBuilder::default();
            crate::serde_util::memory_entry_correct_errors(builder).build().ok()
        }
    }
    builder
}

pub(crate) fn create_workspace_output_output_correct_errors(
    mut builder: crate::operation::create_workspace::builders::CreateWorkspaceOutputBuilder,
) -> crate::operation::create_workspace::builders::CreateWorkspaceOutputBuilder {
    if builder.workspace.is_none() {
        builder.workspace = {
            let builder = crate::types::builders::WorkspaceMetadataBuilder::default();
            crate::serde_util::workspace_metadata_correct_errors(builder)
                .build()
                .ok()
        }
    }
    builder
}

pub(crate) fn delete_task_assist_conversation_output_output_correct_errors(
    mut builder: crate::operation::delete_task_assist_conversation::builders::DeleteTaskAssistConversationOutputBuilder,
) -> crate::operation::delete_task_assist_conversation::builders::DeleteTaskAssistConversationOutputBuilder {
    if builder.conversation_id.is_none() {
        builder.conversation_id = Some(Default::default())
    }
    builder
}

pub(crate) fn get_code_analysis_output_output_correct_errors(
    mut builder: crate::operation::get_code_analysis::builders::GetCodeAnalysisOutputBuilder,
) -> crate::operation::get_code_analysis::builders::GetCodeAnalysisOutputBuilder {
    if builder.status.is_none() {
        builder.status = "no value was set".parse::<crate::types::CodeAnalysisStatus>().ok()
    }
    builder
}

pub(crate) fn get_profile_output_output_correct_errors(
    mut builder: crate::operation::get_profile::builders::GetProfileOutputBuilder,
) -> crate::operation::get_profile::builders::GetProfileOutputBuilder {
    if builder.profile.is_none() {
        builder.profile = {
            let builder = crate::types::builders::ProfileInfoBuilder::default();
            crate::serde_util::profile_info_correct_errors(builder).build().ok()
        }
    }
    builder
}

pub(crate) fn get_retrievals_output_output_correct_errors(
    mut builder: crate::operation::get_retrievals::builders::GetRetrievalsOutputBuilder,
) -> crate::operation::get_retrievals::builders::GetRetrievalsOutputBuilder {
    if builder.retrieval_map.is_none() {
        builder.retrieval_map = Some(Default::default())
    }
    builder
}

pub(crate) fn get_task_assist_code_generation_output_output_correct_errors(
    mut builder: crate::operation::get_task_assist_code_generation::builders::GetTaskAssistCodeGenerationOutputBuilder,
) -> crate::operation::get_task_assist_code_generation::builders::GetTaskAssistCodeGenerationOutputBuilder {
    if builder.conversation_id.is_none() {
        builder.conversation_id = Some(Default::default())
    }
    if builder.code_generation_status.is_none() {
        builder.code_generation_status = {
            let builder = crate::types::builders::CodeGenerationStatusBuilder::default();
            crate::serde_util::code_generation_status_correct_errors(builder)
                .build()
                .ok()
        }
    }
    builder
}

pub(crate) fn get_transformation_output_output_correct_errors(
    mut builder: crate::operation::get_transformation::builders::GetTransformationOutputBuilder,
) -> crate::operation::get_transformation::builders::GetTransformationOutputBuilder {
    if builder.transformation_job.is_none() {
        builder.transformation_job = {
            let builder = crate::types::builders::TransformationJobBuilder::default();
            Some(builder.build())
        }
    }
    builder
}

pub(crate) fn get_transformation_plan_output_output_correct_errors(
    mut builder: crate::operation::get_transformation_plan::builders::GetTransformationPlanOutputBuilder,
) -> crate::operation::get_transformation_plan::builders::GetTransformationPlanOutputBuilder {
    if builder.transformation_plan.is_none() {
        builder.transformation_plan = {
            let builder = crate::types::builders::TransformationPlanBuilder::default();
            crate::serde_util::transformation_plan_correct_errors(builder)
                .build()
                .ok()
        }
    }
    builder
}

pub(crate) fn list_available_customizations_output_output_correct_errors(
    mut builder: crate::operation::list_available_customizations::builders::ListAvailableCustomizationsOutputBuilder,
) -> crate::operation::list_available_customizations::builders::ListAvailableCustomizationsOutputBuilder {
    if builder.customizations.is_none() {
        builder.customizations = Some(Default::default())
    }
    builder
}

pub(crate) fn list_available_models_output_output_correct_errors(
    mut builder: crate::operation::list_available_models::builders::ListAvailableModelsOutputBuilder,
) -> crate::operation::list_available_models::builders::ListAvailableModelsOutputBuilder {
    if builder.models.is_none() {
        builder.models = Some(Default::default())
    }
    builder
}

pub(crate) fn list_available_profiles_output_output_correct_errors(
    mut builder: crate::operation::list_available_profiles::builders::ListAvailableProfilesOutputBuilder,
) -> crate::operation::list_available_profiles::builders::ListAvailableProfilesOutputBuilder {
    if builder.profiles.is_none() {
        builder.profiles = Some(Default::default())
    }
    builder
}

pub(crate) fn list_available_subscriptions_output_output_correct_errors(
    mut builder: crate::operation::list_available_subscriptions::builders::ListAvailableSubscriptionsOutputBuilder,
) -> crate::operation::list_available_subscriptions::builders::ListAvailableSubscriptionsOutputBuilder {
    if builder.subscription_plans.is_none() {
        builder.subscription_plans = Some(Default::default())
    }
    builder
}

pub(crate) fn list_code_analysis_findings_output_output_correct_errors(
    mut builder: crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsOutputBuilder,
) -> crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsOutputBuilder {
    if builder.code_analysis_findings.is_none() {
        builder.code_analysis_findings = Some(Default::default())
    }
    builder
}

pub(crate) fn list_events_output_output_correct_errors(
    mut builder: crate::operation::list_events::builders::ListEventsOutputBuilder,
) -> crate::operation::list_events::builders::ListEventsOutputBuilder {
    if builder.conversation_id.is_none() {
        builder.conversation_id = Some(Default::default())
    }
    if builder.events.is_none() {
        builder.events = Some(Default::default())
    }
    builder
}

pub(crate) fn list_feature_evaluations_output_output_correct_errors(
    mut builder: crate::operation::list_feature_evaluations::builders::ListFeatureEvaluationsOutputBuilder,
) -> crate::operation::list_feature_evaluations::builders::ListFeatureEvaluationsOutputBuilder {
    if builder.feature_evaluations.is_none() {
        builder.feature_evaluations = Some(Default::default())
    }
    builder
}

pub(crate) fn list_user_memory_entries_output_output_correct_errors(
    mut builder: crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesOutputBuilder,
) -> crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesOutputBuilder {
    if builder.memory_entries.is_none() {
        builder.memory_entries = Some(Default::default())
    }
    builder
}

pub(crate) fn list_workspace_metadata_output_output_correct_errors(
    mut builder: crate::operation::list_workspace_metadata::builders::ListWorkspaceMetadataOutputBuilder,
) -> crate::operation::list_workspace_metadata::builders::ListWorkspaceMetadataOutputBuilder {
    if builder.workspaces.is_none() {
        builder.workspaces = Some(Default::default())
    }
    builder
}

pub(crate) fn resume_transformation_output_output_correct_errors(
    mut builder: crate::operation::resume_transformation::builders::ResumeTransformationOutputBuilder,
) -> crate::operation::resume_transformation::builders::ResumeTransformationOutputBuilder {
    if builder.transformation_status.is_none() {
        builder.transformation_status = "no value was set".parse::<crate::types::TransformationStatus>().ok()
    }
    builder
}

pub(crate) fn start_code_analysis_output_output_correct_errors(
    mut builder: crate::operation::start_code_analysis::builders::StartCodeAnalysisOutputBuilder,
) -> crate::operation::start_code_analysis::builders::StartCodeAnalysisOutputBuilder {
    if builder.job_id.is_none() {
        builder.job_id = Some(Default::default())
    }
    if builder.status.is_none() {
        builder.status = "no value was set".parse::<crate::types::CodeAnalysisStatus>().ok()
    }
    builder
}

pub(crate) fn start_task_assist_code_generation_output_output_correct_errors(
    mut builder: crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationOutputBuilder,
) -> crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationOutputBuilder {
    if builder.conversation_id.is_none() {
        builder.conversation_id = Some(Default::default())
    }
    if builder.code_generation_id.is_none() {
        builder.code_generation_id = Some(Default::default())
    }
    builder
}

pub(crate) fn start_transformation_output_output_correct_errors(
    mut builder: crate::operation::start_transformation::builders::StartTransformationOutputBuilder,
) -> crate::operation::start_transformation::builders::StartTransformationOutputBuilder {
    if builder.transformation_job_id.is_none() {
        builder.transformation_job_id = Some(Default::default())
    }
    builder
}

pub(crate) fn stop_transformation_output_output_correct_errors(
    mut builder: crate::operation::stop_transformation::builders::StopTransformationOutputBuilder,
) -> crate::operation::stop_transformation::builders::StopTransformationOutputBuilder {
    if builder.transformation_status.is_none() {
        builder.transformation_status = "no value was set".parse::<crate::types::TransformationStatus>().ok()
    }
    builder
}

pub(crate) fn update_usage_limit_quota_exceeded_exception_correct_errors(
    mut builder: crate::types::error::builders::UpdateUsageLimitQuotaExceededErrorBuilder,
) -> crate::types::error::builders::UpdateUsageLimitQuotaExceededErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn update_usage_limits_output_output_correct_errors(
    mut builder: crate::operation::update_usage_limits::builders::UpdateUsageLimitsOutputBuilder,
) -> crate::operation::update_usage_limits::builders::UpdateUsageLimitsOutputBuilder {
    if builder.status.is_none() {
        builder.status = "no value was set"
            .parse::<crate::types::UsageLimitUpdateRequestStatus>()
            .ok()
    }
    builder
}

pub(crate) fn memory_entry_correct_errors(
    mut builder: crate::types::builders::MemoryEntryBuilder,
) -> crate::types::builders::MemoryEntryBuilder {
    if builder.id.is_none() {
        builder.id = Some(Default::default())
    }
    if builder.memory_entry_string.is_none() {
        builder.memory_entry_string = Some(Default::default())
    }
    if builder.metadata.is_none() {
        builder.metadata = {
            let builder = crate::types::builders::MemoryEntryMetadataBuilder::default();
            crate::serde_util::memory_entry_metadata_correct_errors(builder)
                .build()
                .ok()
        }
    }
    builder
}

pub(crate) fn workspace_metadata_correct_errors(
    mut builder: crate::types::builders::WorkspaceMetadataBuilder,
) -> crate::types::builders::WorkspaceMetadataBuilder {
    if builder.workspace_id.is_none() {
        builder.workspace_id = Some(Default::default())
    }
    if builder.workspace_status.is_none() {
        builder.workspace_status = "no value was set".parse::<crate::types::WorkspaceStatus>().ok()
    }
    builder
}

pub(crate) fn profile_info_correct_errors(
    mut builder: crate::types::builders::ProfileInfoBuilder,
) -> crate::types::builders::ProfileInfoBuilder {
    if builder.arn.is_none() {
        builder.arn = Some(Default::default())
    }
    builder
}

pub(crate) fn code_generation_status_correct_errors(
    mut builder: crate::types::builders::CodeGenerationStatusBuilder,
) -> crate::types::builders::CodeGenerationStatusBuilder {
    if builder.status.is_none() {
        builder.status = "no value was set"
            .parse::<crate::types::CodeGenerationWorkflowStatus>()
            .ok()
    }
    if builder.current_stage.is_none() {
        builder.current_stage = "no value was set"
            .parse::<crate::types::CodeGenerationWorkflowStage>()
            .ok()
    }
    builder
}

pub(crate) fn transformation_plan_correct_errors(
    mut builder: crate::types::builders::TransformationPlanBuilder,
) -> crate::types::builders::TransformationPlanBuilder {
    if builder.transformation_steps.is_none() {
        builder.transformation_steps = Some(Default::default())
    }
    builder
}

pub(crate) fn model_correct_errors(
    mut builder: crate::types::builders::ModelBuilder,
) -> crate::types::builders::ModelBuilder {
    if builder.model_id.is_none() {
        builder.model_id = Some(Default::default())
    }
    builder
}

pub(crate) fn overage_configuration_correct_errors(
    mut builder: crate::types::builders::OverageConfigurationBuilder,
) -> crate::types::builders::OverageConfigurationBuilder {
    if builder.overage_status.is_none() {
        builder.overage_status = "no value was set".parse::<crate::types::OverageStatus>().ok()
    }
    builder
}

pub(crate) fn subscription_info_correct_errors(
    mut builder: crate::types::builders::SubscriptionInfoBuilder,
) -> crate::types::builders::SubscriptionInfoBuilder {
    if builder.r#type.is_none() {
        builder.r#type = "no value was set".parse::<crate::types::SubscriptionType>().ok()
    }
    builder
}

pub(crate) fn test_generation_job_correct_errors(
    mut builder: crate::types::builders::TestGenerationJobBuilder,
) -> crate::types::builders::TestGenerationJobBuilder {
    if builder.test_generation_job_id.is_none() {
        builder.test_generation_job_id = Some(Default::default())
    }
    if builder.test_generation_job_group_name.is_none() {
        builder.test_generation_job_group_name = Some(Default::default())
    }
    if builder.status.is_none() {
        builder.status = "no value was set".parse::<crate::types::TestGenerationJobStatus>().ok()
    }
    if builder.creation_time.is_none() {
        builder.creation_time = Some(::aws_smithy_types::DateTime::from_fractional_secs(0, 0_f64))
    }
    builder
}

pub(crate) fn usage_breakdown_correct_errors(
    mut builder: crate::types::builders::UsageBreakdownBuilder,
) -> crate::types::builders::UsageBreakdownBuilder {
    if builder.current_usage.is_none() {
        builder.current_usage = Some(Default::default())
    }
    if builder.current_overages.is_none() {
        builder.current_overages = Some(Default::default())
    }
    if builder.usage_limit.is_none() {
        builder.usage_limit = Some(Default::default())
    }
    if builder.overage_charges.is_none() {
        builder.overage_charges = Some(Default::default())
    }
    if builder.currency.is_none() {
        builder.currency = "no value was set".parse::<crate::types::Currency>().ok()
    }
    builder
}

pub(crate) fn user_info_correct_errors(
    mut builder: crate::types::builders::UserInfoBuilder,
) -> crate::types::builders::UserInfoBuilder {
    if builder.user_id.is_none() {
        builder.user_id = Some(Default::default())
    }
    if builder.email.is_none() {
        builder.email = Some(Default::default())
    }
    builder
}

pub(crate) fn memory_entry_metadata_correct_errors(
    mut builder: crate::types::builders::MemoryEntryMetadataBuilder,
) -> crate::types::builders::MemoryEntryMetadataBuilder {
    if builder.origin.is_none() {
        builder.origin = "no value was set".parse::<crate::types::Origin>().ok()
    }
    if builder.created_at.is_none() {
        builder.created_at = Some(::aws_smithy_types::DateTime::from_fractional_secs(0, 0_f64))
    }
    if builder.updated_at.is_none() {
        builder.updated_at = Some(::aws_smithy_types::DateTime::from_fractional_secs(0, 0_f64))
    }
    builder
}

pub(crate) fn completion_correct_errors(
    mut builder: crate::types::builders::CompletionBuilder,
) -> crate::types::builders::CompletionBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn customization_correct_errors(
    mut builder: crate::types::builders::CustomizationBuilder,
) -> crate::types::builders::CustomizationBuilder {
    if builder.arn.is_none() {
        builder.arn = Some(Default::default())
    }
    builder
}

pub(crate) fn event_correct_errors(
    mut builder: crate::types::builders::EventBuilder,
) -> crate::types::builders::EventBuilder {
    if builder.event_id.is_none() {
        builder.event_id = Some(Default::default())
    }
    if builder.generation_id.is_none() {
        builder.generation_id = Some(Default::default())
    }
    if builder.event_timestamp.is_none() {
        builder.event_timestamp = Some(::aws_smithy_types::DateTime::from_fractional_secs(0, 0_f64))
    }
    if builder.event_type.is_none() {
        builder.event_type = Some(Default::default())
    }
    if builder.event_blob.is_none() {
        builder.event_blob = Some(::aws_smithy_types::Blob::new(""))
    }
    builder
}

pub(crate) fn feature_evaluation_correct_errors(
    mut builder: crate::types::builders::FeatureEvaluationBuilder,
) -> crate::types::builders::FeatureEvaluationBuilder {
    if builder.feature.is_none() {
        builder.feature = Some(Default::default())
    }
    if builder.variation.is_none() {
        builder.variation = Some(Default::default())
    }
    if builder.value.is_none() {
        builder.value = Some(crate::types::FeatureValue::Unknown)
    }
    builder
}

pub(crate) fn profile_correct_errors(
    mut builder: crate::types::builders::ProfileBuilder,
) -> crate::types::builders::ProfileBuilder {
    if builder.arn.is_none() {
        builder.arn = Some(Default::default())
    }
    if builder.profile_name.is_none() {
        builder.profile_name = Some(Default::default())
    }
    builder
}

pub(crate) fn subscription_plan_correct_errors(
    mut builder: crate::types::builders::SubscriptionPlanBuilder,
) -> crate::types::builders::SubscriptionPlanBuilder {
    if builder.name.is_none() {
        builder.name = "no value was set".parse::<crate::types::SubscriptionName>().ok()
    }
    if builder.pricing.is_none() {
        builder.pricing = {
            let builder = crate::types::builders::PricingInfoBuilder::default();
            crate::serde_util::pricing_info_correct_errors(builder).build().ok()
        }
    }
    builder
}

pub(crate) fn usage_limit_list_correct_errors(
    mut builder: crate::types::builders::UsageLimitListBuilder,
) -> crate::types::builders::UsageLimitListBuilder {
    if builder.r#type.is_none() {
        builder.r#type = "no value was set".parse::<crate::types::UsageLimitType>().ok()
    }
    if builder.current_usage.is_none() {
        builder.current_usage = Some(Default::default())
    }
    if builder.total_usage_limit.is_none() {
        builder.total_usage_limit = Some(Default::default())
    }
    builder
}

pub(crate) fn by_user_analytics_correct_errors(
    mut builder: crate::types::builders::ByUserAnalyticsBuilder,
) -> crate::types::builders::ByUserAnalyticsBuilder {
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn dashboard_analytics_correct_errors(
    mut builder: crate::types::builders::DashboardAnalyticsBuilder,
) -> crate::types::builders::DashboardAnalyticsBuilder {
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn edit_correct_errors(
    mut builder: crate::types::builders::EditBuilder,
) -> crate::types::builders::EditBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn mcp_configuration_correct_errors(
    mut builder: crate::types::builders::McpConfigurationBuilder,
) -> crate::types::builders::McpConfigurationBuilder {
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn pricing_info_correct_errors(
    mut builder: crate::types::builders::PricingInfoBuilder,
) -> crate::types::builders::PricingInfoBuilder {
    if builder.amount.is_none() {
        builder.amount = Some(Default::default())
    }
    if builder.currency.is_none() {
        builder.currency = "no value was set".parse::<crate::types::Currency>().ok()
    }
    builder
}

pub(crate) fn prompt_logging_correct_errors(
    mut builder: crate::types::builders::PromptLoggingBuilder,
) -> crate::types::builders::PromptLoggingBuilder {
    if builder.s3_uri.is_none() {
        builder.s3_uri = Some(Default::default())
    }
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn reference_tracker_configuration_correct_errors(
    mut builder: crate::types::builders::ReferenceTrackerConfigurationBuilder,
) -> crate::types::builders::ReferenceTrackerConfigurationBuilder {
    if builder.recommendations_with_references.is_none() {
        builder.recommendations_with_references = "no value was set"
            .parse::<crate::types::RecommendationsWithReferencesPreference>()
            .ok()
    }
    builder
}

pub(crate) fn resource_policy_correct_errors(
    mut builder: crate::types::builders::ResourcePolicyBuilder,
) -> crate::types::builders::ResourcePolicyBuilder {
    if builder.effect.is_none() {
        builder.effect = "no value was set".parse::<crate::types::ResourcePolicyEffect>().ok()
    }
    builder
}

pub(crate) fn retrieval_correct_errors(
    mut builder: crate::types::builders::RetrievalBuilder,
) -> crate::types::builders::RetrievalBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn transformation_step_correct_errors(
    mut builder: crate::types::builders::TransformationStepBuilder,
) -> crate::types::builders::TransformationStepBuilder {
    if builder.id.is_none() {
        builder.id = Some(Default::default())
    }
    if builder.name.is_none() {
        builder.name = Some(Default::default())
    }
    if builder.description.is_none() {
        builder.description = Some(Default::default())
    }
    if builder.status.is_none() {
        builder.status = "no value was set"
            .parse::<crate::types::TransformationStepStatus>()
            .ok()
    }
    builder
}

pub(crate) fn workspace_context_correct_errors(
    mut builder: crate::types::builders::WorkspaceContextBuilder,
) -> crate::types::builders::WorkspaceContextBuilder {
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn application_properties_correct_errors(
    mut builder: crate::types::builders::ApplicationPropertiesBuilder,
) -> crate::types::builders::ApplicationPropertiesBuilder {
    if builder.tenant_id.is_none() {
        builder.tenant_id = Some(Default::default())
    }
    if builder.application_arn.is_none() {
        builder.application_arn = Some(Default::default())
    }
    if builder.tenant_url.is_none() {
        builder.tenant_url = Some(Default::default())
    }
    if builder.application_type.is_none() {
        builder.application_type = "no value was set".parse::<crate::types::FunctionalityName>().ok()
    }
    builder
}

pub(crate) fn notifications_feature_correct_errors(
    mut builder: crate::types::builders::NotificationsFeatureBuilder,
) -> crate::types::builders::NotificationsFeatureBuilder {
    if builder.feature.is_none() {
        builder.feature = Some(Default::default())
    }
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn sso_identity_details_correct_errors(
    mut builder: crate::types::builders::SsoIdentityDetailsBuilder,
) -> crate::types::builders::SsoIdentityDetailsBuilder {
    if builder.instance_arn.is_none() {
        builder.instance_arn = Some(Default::default())
    }
    if builder.oidc_client_id.is_none() {
        builder.oidc_client_id = Some(Default::default())
    }
    builder
}

pub(crate) fn transformation_progress_update_correct_errors(
    mut builder: crate::types::builders::TransformationProgressUpdateBuilder,
) -> crate::types::builders::TransformationProgressUpdateBuilder {
    if builder.name.is_none() {
        builder.name = Some(Default::default())
    }
    if builder.status.is_none() {
        builder.status = "no value was set"
            .parse::<crate::types::TransformationProgressUpdateStatus>()
            .ok()
    }
    builder
}
