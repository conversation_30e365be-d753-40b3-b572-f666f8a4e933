// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::start_test_generation::_start_test_generation_input::StartTestGenerationInputBuilder;
pub use crate::operation::start_test_generation::_start_test_generation_output::StartTestGenerationOutputBuilder;

impl crate::operation::start_test_generation::builders::StartTestGenerationInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::start_test_generation::StartTestGenerationOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_test_generation::StartTestGenerationError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.start_test_generation();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `StartTestGeneration`.
///
/// API to start test generation.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct StartTestGenerationFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::start_test_generation::builders::StartTestGenerationInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::start_test_generation::StartTestGenerationOutput,
        crate::operation::start_test_generation::StartTestGenerationError,
    > for StartTestGenerationFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::start_test_generation::StartTestGenerationOutput,
            crate::operation::start_test_generation::StartTestGenerationError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl StartTestGenerationFluentBuilder {
    /// Creates a new `StartTestGenerationFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the StartTestGeneration as a reference.
    pub fn as_input(&self) -> &crate::operation::start_test_generation::builders::StartTestGenerationInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::start_test_generation::StartTestGenerationOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_test_generation::StartTestGenerationError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins = crate::operation::start_test_generation::StartTestGeneration::operation_runtime_plugins(
            self.handle.runtime_plugins.clone(),
            &self.handle.conf,
            self.config_override,
        );
        crate::operation::start_test_generation::StartTestGeneration::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::start_test_generation::StartTestGenerationOutput,
        crate::operation::start_test_generation::StartTestGenerationError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn upload_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.upload_id(input.into());
        self
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn set_upload_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_upload_id(input);
        self
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn get_upload_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_upload_id()
    }

    /// Appends an item to `targetCodeList`.
    ///
    /// To override the contents of this collection use
    /// [`set_target_code_list`](Self::set_target_code_list).
    #[allow(missing_docs)] // documentation missing in model
    pub fn target_code_list(mut self, input: crate::types::TargetCode) -> Self {
        self.inner = self.inner.target_code_list(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_target_code_list(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::TargetCode>>,
    ) -> Self {
        self.inner = self.inner.set_target_code_list(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_target_code_list(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::TargetCode>> {
        self.inner.get_target_code_list()
    }

    /// The content of user input.
    pub fn user_input(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.user_input(input.into());
        self
    }

    /// The content of user input.
    pub fn set_user_input(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_user_input(input);
        self
    }

    /// The content of user input.
    pub fn get_user_input(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_user_input()
    }

    /// Test generation job group name
    pub fn test_generation_job_group_name(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.test_generation_job_group_name(input.into());
        self
    }

    /// Test generation job group name
    pub fn set_test_generation_job_group_name(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_test_generation_job_group_name(input);
        self
    }

    /// Test generation job group name
    pub fn get_test_generation_job_group_name(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_test_generation_job_group_name()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.client_token(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_client_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_client_token(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_client_token(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_client_token()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn reference_tracker_configuration(mut self, input: crate::types::ReferenceTrackerConfiguration) -> Self {
        self.inner = self.inner.reference_tracker_configuration(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_reference_tracker_configuration(
        mut self,
        input: ::std::option::Option<crate::types::ReferenceTrackerConfiguration>,
    ) -> Self {
        self.inner = self.inner.set_reference_tracker_configuration(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_reference_tracker_configuration(
        &self,
    ) -> &::std::option::Option<crate::types::ReferenceTrackerConfiguration> {
        self.inner.get_reference_tracker_configuration()
    }
}
