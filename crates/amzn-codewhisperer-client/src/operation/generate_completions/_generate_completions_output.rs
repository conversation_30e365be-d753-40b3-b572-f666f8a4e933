// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct GenerateCompletionsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub predictions: ::std::option::Option<::std::vec::Vec<crate::types::Prediction>>,
    #[allow(missing_docs)] // documentation missing in model
    pub completions: ::std::option::Option<::std::vec::Vec<crate::types::Completion>>,
    #[allow(missing_docs)] // documentation missing in model
    pub next_token: ::std::option::Option<::std::string::String>,
    /// Unique identifier for the model
    pub model_id: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl GenerateCompletionsOutput {
    #[allow(missing_docs)] // documentation missing in model
    /// If no value was sent for this field, a default will be set. If you want to determine if no
    /// value was sent, use `.predictions.is_none()`.
    pub fn predictions(&self) -> &[crate::types::Prediction] {
        self.predictions.as_deref().unwrap_or_default()
    }

    #[allow(missing_docs)] // documentation missing in model
    /// If no value was sent for this field, a default will be set. If you want to determine if no
    /// value was sent, use `.completions.is_none()`.
    pub fn completions(&self) -> &[crate::types::Completion] {
        self.completions.as_deref().unwrap_or_default()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(&self) -> ::std::option::Option<&str> {
        self.next_token.as_deref()
    }

    /// Unique identifier for the model
    pub fn model_id(&self) -> ::std::option::Option<&str> {
        self.model_id.as_deref()
    }
}
impl ::std::fmt::Debug for GenerateCompletionsOutput {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("GenerateCompletionsOutput");
        formatter.field("predictions", &self.predictions);
        formatter.field("completions", &self.completions);
        formatter.field("next_token", &"*** Sensitive Data Redacted ***");
        formatter.field("model_id", &self.model_id);
        formatter.field("_request_id", &self._request_id);
        formatter.finish()
    }
}
impl ::aws_types::request_id::RequestId for GenerateCompletionsOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl GenerateCompletionsOutput {
    /// Creates a new builder-style object to manufacture
    /// [`GenerateCompletionsOutput`](crate::operation::generate_completions::GenerateCompletionsOutput).
    pub fn builder() -> crate::operation::generate_completions::builders::GenerateCompletionsOutputBuilder {
        crate::operation::generate_completions::builders::GenerateCompletionsOutputBuilder::default()
    }
}

/// A builder for
/// [`GenerateCompletionsOutput`](crate::operation::generate_completions::GenerateCompletionsOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct GenerateCompletionsOutputBuilder {
    pub(crate) predictions: ::std::option::Option<::std::vec::Vec<crate::types::Prediction>>,
    pub(crate) completions: ::std::option::Option<::std::vec::Vec<crate::types::Completion>>,
    pub(crate) next_token: ::std::option::Option<::std::string::String>,
    pub(crate) model_id: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl GenerateCompletionsOutputBuilder {
    /// Appends an item to `predictions`.
    ///
    /// To override the contents of this collection use [`set_predictions`](Self::set_predictions).
    pub fn predictions(mut self, input: crate::types::Prediction) -> Self {
        let mut v = self.predictions.unwrap_or_default();
        v.push(input);
        self.predictions = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_predictions(mut self, input: ::std::option::Option<::std::vec::Vec<crate::types::Prediction>>) -> Self {
        self.predictions = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_predictions(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::Prediction>> {
        &self.predictions
    }

    /// Appends an item to `completions`.
    ///
    /// To override the contents of this collection use [`set_completions`](Self::set_completions).
    pub fn completions(mut self, input: crate::types::Completion) -> Self {
        let mut v = self.completions.unwrap_or_default();
        v.push(input);
        self.completions = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_completions(mut self, input: ::std::option::Option<::std::vec::Vec<crate::types::Completion>>) -> Self {
        self.completions = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_completions(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::Completion>> {
        &self.completions
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.next_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.next_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.next_token
    }

    /// Unique identifier for the model
    pub fn model_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.model_id = ::std::option::Option::Some(input.into());
        self
    }

    /// Unique identifier for the model
    pub fn set_model_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.model_id = input;
        self
    }

    /// Unique identifier for the model
    pub fn get_model_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.model_id
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`GenerateCompletionsOutput`](crate::operation::generate_completions::GenerateCompletionsOutput).
    pub fn build(self) -> crate::operation::generate_completions::GenerateCompletionsOutput {
        crate::operation::generate_completions::GenerateCompletionsOutput {
            predictions: self.predictions,
            completions: self.completions,
            next_token: self.next_token,
            model_id: self.model_id,
            _request_id: self._request_id,
        }
    }
}
impl ::std::fmt::Debug for GenerateCompletionsOutputBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("GenerateCompletionsOutputBuilder");
        formatter.field("predictions", &self.predictions);
        formatter.field("completions", &self.completions);
        formatter.field("next_token", &"*** Sensitive Data Redacted ***");
        formatter.field("model_id", &self.model_id);
        formatter.field("_request_id", &self._request_id);
        formatter.finish()
    }
}
