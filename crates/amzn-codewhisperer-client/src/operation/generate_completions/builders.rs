// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::generate_completions::_generate_completions_input::GenerateCompletionsInputBuilder;
pub use crate::operation::generate_completions::_generate_completions_output::GenerateCompletionsOutputBuilder;

impl crate::operation::generate_completions::builders::GenerateCompletionsInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::generate_completions::GenerateCompletionsOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::generate_completions::GenerateCompletionsError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.generate_completions();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `GenerateCompletions`.
///
/// Generate completions based on the provided file context in a paginated response.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct GenerateCompletionsFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::generate_completions::builders::GenerateCompletionsInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::generate_completions::GenerateCompletionsOutput,
        crate::operation::generate_completions::GenerateCompletionsError,
    > for GenerateCompletionsFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::generate_completions::GenerateCompletionsOutput,
            crate::operation::generate_completions::GenerateCompletionsError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl GenerateCompletionsFluentBuilder {
    /// Creates a new `GenerateCompletionsFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the GenerateCompletions as a reference.
    pub fn as_input(&self) -> &crate::operation::generate_completions::builders::GenerateCompletionsInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::generate_completions::GenerateCompletionsOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::generate_completions::GenerateCompletionsError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins = crate::operation::generate_completions::GenerateCompletions::operation_runtime_plugins(
            self.handle.runtime_plugins.clone(),
            &self.handle.conf,
            self.config_override,
        );
        crate::operation::generate_completions::GenerateCompletions::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::generate_completions::GenerateCompletionsOutput,
        crate::operation::generate_completions::GenerateCompletionsError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    /// Create a paginator for this request
    ///
    /// Paginators are used by calling
    /// [`send().await`](crate::operation::generate_completions::paginator::GenerateCompletionsPaginator::send)
    /// which returns a
    /// [`PaginationStream`](aws_smithy_async::future::pagination_stream::PaginationStream).
    pub fn into_paginator(self) -> crate::operation::generate_completions::paginator::GenerateCompletionsPaginator {
        crate::operation::generate_completions::paginator::GenerateCompletionsPaginator::new(self.handle, self.inner)
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn file_context(mut self, input: crate::types::FileContext) -> Self {
        self.inner = self.inner.file_context(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_file_context(mut self, input: ::std::option::Option<crate::types::FileContext>) -> Self {
        self.inner = self.inner.set_file_context(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_file_context(&self) -> &::std::option::Option<crate::types::FileContext> {
        self.inner.get_file_context()
    }

    /// Represents the state of an Editor
    pub fn editor_state(mut self, input: crate::types::EditorState) -> Self {
        self.inner = self.inner.editor_state(input);
        self
    }

    /// Represents the state of an Editor
    pub fn set_editor_state(mut self, input: ::std::option::Option<crate::types::EditorState>) -> Self {
        self.inner = self.inner.set_editor_state(input);
        self
    }

    /// Represents the state of an Editor
    pub fn get_editor_state(&self) -> &::std::option::Option<crate::types::EditorState> {
        self.inner.get_editor_state()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn max_results(mut self, input: i32) -> Self {
        self.inner = self.inner.max_results(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_max_results(mut self, input: ::std::option::Option<i32>) -> Self {
        self.inner = self.inner.set_max_results(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_max_results(&self) -> &::std::option::Option<i32> {
        self.inner.get_max_results()
    }

    /// Appends an item to `predictionTypes`.
    ///
    /// To override the contents of this collection use
    /// [`set_prediction_types`](Self::set_prediction_types).
    #[allow(missing_docs)] // documentation missing in model
    pub fn prediction_types(mut self, input: crate::types::PredictionType) -> Self {
        self.inner = self.inner.prediction_types(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_prediction_types(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::PredictionType>>,
    ) -> Self {
        self.inner = self.inner.set_prediction_types(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_prediction_types(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::PredictionType>> {
        self.inner.get_prediction_types()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.next_token(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_next_token(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_next_token()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn reference_tracker_configuration(mut self, input: crate::types::ReferenceTrackerConfiguration) -> Self {
        self.inner = self.inner.reference_tracker_configuration(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_reference_tracker_configuration(
        mut self,
        input: ::std::option::Option<crate::types::ReferenceTrackerConfiguration>,
    ) -> Self {
        self.inner = self.inner.set_reference_tracker_configuration(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_reference_tracker_configuration(
        &self,
    ) -> &::std::option::Option<crate::types::ReferenceTrackerConfiguration> {
        self.inner.get_reference_tracker_configuration()
    }

    /// Appends an item to `supplementalContexts`.
    ///
    /// To override the contents of this collection use
    /// [`set_supplemental_contexts`](Self::set_supplemental_contexts).
    #[allow(missing_docs)] // documentation missing in model
    pub fn supplemental_contexts(mut self, input: crate::types::SupplementalContext) -> Self {
        self.inner = self.inner.supplemental_contexts(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_supplemental_contexts(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::SupplementalContext>>,
    ) -> Self {
        self.inner = self.inner.set_supplemental_contexts(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_supplemental_contexts(
        &self,
    ) -> &::std::option::Option<::std::vec::Vec<crate::types::SupplementalContext>> {
        self.inner.get_supplemental_contexts()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn customization_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.customization_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_customization_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_customization_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_customization_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_customization_arn()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn opt_out_preference(mut self, input: crate::types::OptOutPreference) -> Self {
        self.inner = self.inner.opt_out_preference(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_opt_out_preference(mut self, input: ::std::option::Option<crate::types::OptOutPreference>) -> Self {
        self.inner = self.inner.set_opt_out_preference(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_opt_out_preference(&self) -> &::std::option::Option<crate::types::OptOutPreference> {
        self.inner.get_opt_out_preference()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn user_context(mut self, input: crate::types::UserContext) -> Self {
        self.inner = self.inner.user_context(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_user_context(mut self, input: ::std::option::Option<crate::types::UserContext>) -> Self {
        self.inner = self.inner.set_user_context(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_user_context(&self) -> &::std::option::Option<crate::types::UserContext> {
        self.inner.get_user_context()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn workspace_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.workspace_id(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_workspace_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_workspace_id(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_workspace_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_workspace_id()
    }

    /// Unique identifier for the model
    pub fn model_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.model_id(input.into());
        self
    }

    /// Unique identifier for the model
    pub fn set_model_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_model_id(input);
        self
    }

    /// Unique identifier for the model
    pub fn get_model_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_model_id()
    }
}
