// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Structure to represent stop code transformation response.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ResumeTransformationOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub transformation_status: crate::types::TransformationStatus,
    _request_id: Option<String>,
}
impl ResumeTransformationOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn transformation_status(&self) -> &crate::types::TransformationStatus {
        &self.transformation_status
    }
}
impl ::aws_types::request_id::RequestId for ResumeTransformationOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl ResumeTransformationOutput {
    /// Creates a new builder-style object to manufacture
    /// [`ResumeTransformationOutput`](crate::operation::resume_transformation::ResumeTransformationOutput).
    pub fn builder() -> crate::operation::resume_transformation::builders::ResumeTransformationOutputBuilder {
        crate::operation::resume_transformation::builders::ResumeTransformationOutputBuilder::default()
    }
}

/// A builder for
/// [`ResumeTransformationOutput`](crate::operation::resume_transformation::ResumeTransformationOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ResumeTransformationOutputBuilder {
    pub(crate) transformation_status: ::std::option::Option<crate::types::TransformationStatus>,
    _request_id: Option<String>,
}
impl ResumeTransformationOutputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn transformation_status(mut self, input: crate::types::TransformationStatus) -> Self {
        self.transformation_status = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_transformation_status(
        mut self,
        input: ::std::option::Option<crate::types::TransformationStatus>,
    ) -> Self {
        self.transformation_status = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_transformation_status(&self) -> &::std::option::Option<crate::types::TransformationStatus> {
        &self.transformation_status
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ResumeTransformationOutput`](crate::operation::resume_transformation::ResumeTransformationOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`transformation_status`](crate::operation::resume_transformation::builders::ResumeTransformationOutputBuilder::transformation_status)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::resume_transformation::ResumeTransformationOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::resume_transformation::ResumeTransformationOutput {
            transformation_status: self.transformation_status.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "transformation_status",
                    "transformation_status was not specified but it is required when building ResumeTransformationOutput",
                )
            })?,
            _request_id: self._request_id,
        })
    }
}
