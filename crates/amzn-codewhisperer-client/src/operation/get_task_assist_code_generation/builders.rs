// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::get_task_assist_code_generation::_get_task_assist_code_generation_input::GetTaskAssistCodeGenerationInputBuilder;
pub use crate::operation::get_task_assist_code_generation::_get_task_assist_code_generation_output::GetTaskAssistCodeGenerationOutputBuilder;

impl crate::operation::get_task_assist_code_generation::builders::GetTaskAssistCodeGenerationInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.get_task_assist_code_generation();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `GetTaskAssistCodeGeneration`.
///
/// API to get status of task assist code generation.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct GetTaskAssistCodeGenerationFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::get_task_assist_code_generation::builders::GetTaskAssistCodeGenerationInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationOutput,
        crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationError,
    > for GetTaskAssistCodeGenerationFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationOutput,
            crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl GetTaskAssistCodeGenerationFluentBuilder {
    /// Creates a new `GetTaskAssistCodeGenerationFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the GetTaskAssistCodeGeneration as a reference.
    pub fn as_input(
        &self,
    ) -> &crate::operation::get_task_assist_code_generation::builders::GetTaskAssistCodeGenerationInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins =
            crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGeneration::operation_runtime_plugins(
                self.handle.runtime_plugins.clone(),
                &self.handle.conf,
                self.config_override,
            );
        crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGeneration::orchestrate(
            &runtime_plugins,
            input,
        )
        .await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationOutput,
        crate::operation::get_task_assist_code_generation::GetTaskAssistCodeGenerationError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    /// ID which represents a multi-turn conversation
    pub fn conversation_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.conversation_id(input.into());
        self
    }

    /// ID which represents a multi-turn conversation
    pub fn set_conversation_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_conversation_id(input);
        self
    }

    /// ID which represents a multi-turn conversation
    pub fn get_conversation_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_conversation_id()
    }

    /// ID which represents a single code generation in a conversation
    pub fn code_generation_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.code_generation_id(input.into());
        self
    }

    /// ID which represents a single code generation in a conversation
    pub fn set_code_generation_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_code_generation_id(input);
        self
    }

    /// ID which represents a single code generation in a conversation
    pub fn get_code_generation_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_code_generation_id()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }
}
