// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct SendTelemetryEventOutput {
    _request_id: Option<String>,
}
impl ::aws_types::request_id::RequestId for SendTelemetryEventOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl SendTelemetryEventOutput {
    /// Creates a new builder-style object to manufacture
    /// [`SendTelemetryEventOutput`](crate::operation::send_telemetry_event::SendTelemetryEventOutput).
    pub fn builder() -> crate::operation::send_telemetry_event::builders::SendTelemetryEventOutputBuilder {
        crate::operation::send_telemetry_event::builders::SendTelemetryEventOutputBuilder::default()
    }
}

/// A builder for
/// [`SendTelemetryEventOutput`](crate::operation::send_telemetry_event::SendTelemetryEventOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct SendTelemetryEventOutputBuilder {
    _request_id: Option<String>,
}
impl SendTelemetryEventOutputBuilder {
    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`SendTelemetryEventOutput`](crate::operation::send_telemetry_event::SendTelemetryEventOutput).
    pub fn build(self) -> crate::operation::send_telemetry_event::SendTelemetryEventOutput {
        crate::operation::send_telemetry_event::SendTelemetryEventOutput {
            _request_id: self._request_id,
        }
    }
}
