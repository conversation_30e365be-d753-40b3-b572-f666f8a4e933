// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct SetUserPreferenceOutput {
    _request_id: Option<String>,
}
impl ::aws_types::request_id::RequestId for SetUserPreferenceOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl SetUserPreferenceOutput {
    /// Creates a new builder-style object to manufacture
    /// [`SetUserPreferenceOutput`](crate::operation::set_user_preference::SetUserPreferenceOutput).
    pub fn builder() -> crate::operation::set_user_preference::builders::SetUserPreferenceOutputBuilder {
        crate::operation::set_user_preference::builders::SetUserPreferenceOutputBuilder::default()
    }
}

/// A builder for
/// [`SetUserPreferenceOutput`](crate::operation::set_user_preference::SetUserPreferenceOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct SetUserPreferenceOutputBuilder {
    _request_id: Option<String>,
}
impl SetUserPreferenceOutputBuilder {
    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`SetUserPreferenceOutput`](crate::operation::set_user_preference::SetUserPreferenceOutput).
    pub fn build(self) -> crate::operation::set_user_preference::SetUserPreferenceOutput {
        crate::operation::set_user_preference::SetUserPreferenceOutput {
            _request_id: self._request_id,
        }
    }
}
