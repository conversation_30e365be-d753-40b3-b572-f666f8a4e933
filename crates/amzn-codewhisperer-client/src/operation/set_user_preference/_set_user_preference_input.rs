// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct SetUserPreferenceInput {
    #[allow(missing_docs)] // documentation missing in model
    pub overage_configuration: ::std::option::Option<crate::types::OverageConfiguration>,
}
impl SetUserPreferenceInput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn overage_configuration(&self) -> ::std::option::Option<&crate::types::OverageConfiguration> {
        self.overage_configuration.as_ref()
    }
}
impl SetUserPreferenceInput {
    /// Creates a new builder-style object to manufacture
    /// [`SetUserPreferenceInput`](crate::operation::set_user_preference::SetUserPreferenceInput).
    pub fn builder() -> crate::operation::set_user_preference::builders::SetUserPreferenceInputBuilder {
        crate::operation::set_user_preference::builders::SetUserPreferenceInputBuilder::default()
    }
}

/// A builder for
/// [`SetUserPreferenceInput`](crate::operation::set_user_preference::SetUserPreferenceInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct SetUserPreferenceInputBuilder {
    pub(crate) overage_configuration: ::std::option::Option<crate::types::OverageConfiguration>,
}
impl SetUserPreferenceInputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn overage_configuration(mut self, input: crate::types::OverageConfiguration) -> Self {
        self.overage_configuration = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_overage_configuration(
        mut self,
        input: ::std::option::Option<crate::types::OverageConfiguration>,
    ) -> Self {
        self.overage_configuration = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_overage_configuration(&self) -> &::std::option::Option<crate::types::OverageConfiguration> {
        &self.overage_configuration
    }

    /// Consumes the builder and constructs a
    /// [`SetUserPreferenceInput`](crate::operation::set_user_preference::SetUserPreferenceInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::set_user_preference::SetUserPreferenceInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::set_user_preference::SetUserPreferenceInput {
            overage_configuration: self.overage_configuration,
        })
    }
}
