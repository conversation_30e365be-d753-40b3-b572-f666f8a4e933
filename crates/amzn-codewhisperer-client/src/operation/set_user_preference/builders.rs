// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::set_user_preference::_set_user_preference_input::SetUserPreferenceInputBuilder;
pub use crate::operation::set_user_preference::_set_user_preference_output::SetUserPreferenceOutputBuilder;

impl crate::operation::set_user_preference::builders::SetUserPreferenceInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::set_user_preference::SetUserPreferenceOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::set_user_preference::SetUserPreferenceError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.set_user_preference();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `SetUserPreference`.
///
/// API to set user preference
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct SetUserPreferenceFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::set_user_preference::builders::SetUserPreferenceInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::set_user_preference::SetUserPreferenceOutput,
        crate::operation::set_user_preference::SetUserPreferenceError,
    > for SetUserPreferenceFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::set_user_preference::SetUserPreferenceOutput,
            crate::operation::set_user_preference::SetUserPreferenceError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl SetUserPreferenceFluentBuilder {
    /// Creates a new `SetUserPreferenceFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the SetUserPreference as a reference.
    pub fn as_input(&self) -> &crate::operation::set_user_preference::builders::SetUserPreferenceInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::set_user_preference::SetUserPreferenceOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::set_user_preference::SetUserPreferenceError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins = crate::operation::set_user_preference::SetUserPreference::operation_runtime_plugins(
            self.handle.runtime_plugins.clone(),
            &self.handle.conf,
            self.config_override,
        );
        crate::operation::set_user_preference::SetUserPreference::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::set_user_preference::SetUserPreferenceOutput,
        crate::operation::set_user_preference::SetUserPreferenceError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn overage_configuration(mut self, input: crate::types::OverageConfiguration) -> Self {
        self.inner = self.inner.overage_configuration(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_overage_configuration(
        mut self,
        input: ::std::option::Option<crate::types::OverageConfiguration>,
    ) -> Self {
        self.inner = self.inner.set_overage_configuration(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_overage_configuration(&self) -> &::std::option::Option<crate::types::OverageConfiguration> {
        self.inner.get_overage_configuration()
    }
}
