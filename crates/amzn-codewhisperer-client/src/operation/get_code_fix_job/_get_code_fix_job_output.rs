// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct GetCodeFixJobOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub job_status: ::std::option::Option<crate::types::CodeFixJobStatus>,
    #[allow(missing_docs)] // documentation missing in model
    pub suggested_fix: ::std::option::Option<crate::types::SuggestedFix>,
    _request_id: Option<String>,
}
impl GetCodeFixJobOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn job_status(&self) -> ::std::option::Option<&crate::types::CodeFixJobStatus> {
        self.job_status.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn suggested_fix(&self) -> ::std::option::Option<&crate::types::SuggestedFix> {
        self.suggested_fix.as_ref()
    }
}
impl ::aws_types::request_id::RequestId for GetCodeFixJobOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl GetCodeFixJobOutput {
    /// Creates a new builder-style object to manufacture
    /// [`GetCodeFixJobOutput`](crate::operation::get_code_fix_job::GetCodeFixJobOutput).
    pub fn builder() -> crate::operation::get_code_fix_job::builders::GetCodeFixJobOutputBuilder {
        crate::operation::get_code_fix_job::builders::GetCodeFixJobOutputBuilder::default()
    }
}

/// A builder for [`GetCodeFixJobOutput`](crate::operation::get_code_fix_job::GetCodeFixJobOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct GetCodeFixJobOutputBuilder {
    pub(crate) job_status: ::std::option::Option<crate::types::CodeFixJobStatus>,
    pub(crate) suggested_fix: ::std::option::Option<crate::types::SuggestedFix>,
    _request_id: Option<String>,
}
impl GetCodeFixJobOutputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn job_status(mut self, input: crate::types::CodeFixJobStatus) -> Self {
        self.job_status = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_job_status(mut self, input: ::std::option::Option<crate::types::CodeFixJobStatus>) -> Self {
        self.job_status = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_job_status(&self) -> &::std::option::Option<crate::types::CodeFixJobStatus> {
        &self.job_status
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn suggested_fix(mut self, input: crate::types::SuggestedFix) -> Self {
        self.suggested_fix = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_suggested_fix(mut self, input: ::std::option::Option<crate::types::SuggestedFix>) -> Self {
        self.suggested_fix = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_suggested_fix(&self) -> &::std::option::Option<crate::types::SuggestedFix> {
        &self.suggested_fix
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`GetCodeFixJobOutput`](crate::operation::get_code_fix_job::GetCodeFixJobOutput).
    pub fn build(self) -> crate::operation::get_code_fix_job::GetCodeFixJobOutput {
        crate::operation::get_code_fix_job::GetCodeFixJobOutput {
            job_status: self.job_status,
            suggested_fix: self.suggested_fix,
            _request_id: self._request_id,
        }
    }
}
