// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::create_artifact_upload_url::_create_artifact_upload_url_input::CreateArtifactUploadUrlInputBuilder;
pub use crate::operation::create_artifact_upload_url::_create_artifact_upload_url_output::CreateArtifactUploadUrlOutputBuilder;

impl crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.create_artifact_upload_url();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `CreateArtifactUploadUrl`.
///
/// Creates a pre-signed, S3 write URL for uploading a repository zip archive.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct CreateArtifactUploadUrlFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput,
        crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlError,
    > for CreateArtifactUploadUrlFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput,
            crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl CreateArtifactUploadUrlFluentBuilder {
    /// Creates a new `CreateArtifactUploadUrlFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the CreateArtifactUploadUrl as a reference.
    pub fn as_input(
        &self,
    ) -> &crate::operation::create_artifact_upload_url::builders::CreateArtifactUploadUrlInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins =
            crate::operation::create_artifact_upload_url::CreateArtifactUploadUrl::operation_runtime_plugins(
                self.handle.runtime_plugins.clone(),
                &self.handle.conf,
                self.config_override,
            );
        crate::operation::create_artifact_upload_url::CreateArtifactUploadUrl::orchestrate(&runtime_plugins, input)
            .await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlOutput,
        crate::operation::create_artifact_upload_url::CreateArtifactUploadUrlError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn content_md5(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.content_md5(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_content_md5(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_content_md5(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_content_md5(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_content_md5()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn content_checksum(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.content_checksum(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_content_checksum(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_content_checksum(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_content_checksum(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_content_checksum()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn content_checksum_type(mut self, input: crate::types::ContentChecksumType) -> Self {
        self.inner = self.inner.content_checksum_type(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_content_checksum_type(
        mut self,
        input: ::std::option::Option<crate::types::ContentChecksumType>,
    ) -> Self {
        self.inner = self.inner.set_content_checksum_type(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_content_checksum_type(&self) -> &::std::option::Option<crate::types::ContentChecksumType> {
        self.inner.get_content_checksum_type()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn content_length(mut self, input: i64) -> Self {
        self.inner = self.inner.content_length(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_content_length(mut self, input: ::std::option::Option<i64>) -> Self {
        self.inner = self.inner.set_content_length(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_content_length(&self) -> &::std::option::Option<i64> {
        self.inner.get_content_length()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn artifact_type(mut self, input: crate::types::ArtifactType) -> Self {
        self.inner = self.inner.artifact_type(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_artifact_type(mut self, input: ::std::option::Option<crate::types::ArtifactType>) -> Self {
        self.inner = self.inner.set_artifact_type(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_artifact_type(&self) -> &::std::option::Option<crate::types::ArtifactType> {
        self.inner.get_artifact_type()
    }

    /// Upload Intent
    pub fn upload_intent(mut self, input: crate::types::UploadIntent) -> Self {
        self.inner = self.inner.upload_intent(input);
        self
    }

    /// Upload Intent
    pub fn set_upload_intent(mut self, input: ::std::option::Option<crate::types::UploadIntent>) -> Self {
        self.inner = self.inner.set_upload_intent(input);
        self
    }

    /// Upload Intent
    pub fn get_upload_intent(&self) -> &::std::option::Option<crate::types::UploadIntent> {
        self.inner.get_upload_intent()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn upload_context(mut self, input: crate::types::UploadContext) -> Self {
        self.inner = self.inner.upload_context(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_upload_context(mut self, input: ::std::option::Option<crate::types::UploadContext>) -> Self {
        self.inner = self.inner.set_upload_context(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_upload_context(&self) -> &::std::option::Option<crate::types::UploadContext> {
        self.inner.get_upload_context()
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn upload_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.upload_id(input.into());
        self
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn set_upload_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_upload_id(input);
        self
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn get_upload_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_upload_id()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }
}
