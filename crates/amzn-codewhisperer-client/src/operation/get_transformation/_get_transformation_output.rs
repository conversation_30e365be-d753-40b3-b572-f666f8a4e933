// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Structure to represent get code transformation response.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct GetTransformationOutput {
    /// Represent a Transformation Job
    pub transformation_job: crate::types::TransformationJob,
    _request_id: Option<String>,
}
impl GetTransformationOutput {
    /// Represent a Transformation Job
    pub fn transformation_job(&self) -> &crate::types::TransformationJob {
        &self.transformation_job
    }
}
impl ::aws_types::request_id::RequestId for GetTransformationOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl GetTransformationOutput {
    /// Creates a new builder-style object to manufacture
    /// [`GetTransformationOutput`](crate::operation::get_transformation::GetTransformationOutput).
    pub fn builder() -> crate::operation::get_transformation::builders::GetTransformationOutputBuilder {
        crate::operation::get_transformation::builders::GetTransformationOutputBuilder::default()
    }
}

/// A builder for
/// [`GetTransformationOutput`](crate::operation::get_transformation::GetTransformationOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct GetTransformationOutputBuilder {
    pub(crate) transformation_job: ::std::option::Option<crate::types::TransformationJob>,
    _request_id: Option<String>,
}
impl GetTransformationOutputBuilder {
    /// Represent a Transformation Job
    /// This field is required.
    pub fn transformation_job(mut self, input: crate::types::TransformationJob) -> Self {
        self.transformation_job = ::std::option::Option::Some(input);
        self
    }

    /// Represent a Transformation Job
    pub fn set_transformation_job(mut self, input: ::std::option::Option<crate::types::TransformationJob>) -> Self {
        self.transformation_job = input;
        self
    }

    /// Represent a Transformation Job
    pub fn get_transformation_job(&self) -> &::std::option::Option<crate::types::TransformationJob> {
        &self.transformation_job
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`GetTransformationOutput`](crate::operation::get_transformation::GetTransformationOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`transformation_job`](crate::operation::get_transformation::builders::GetTransformationOutputBuilder::transformation_job)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::get_transformation::GetTransformationOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::get_transformation::GetTransformationOutput {
            transformation_job: self.transformation_job.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "transformation_job",
                    "transformation_job was not specified but it is required when building GetTransformationOutput",
                )
            })?,
            _request_id: self._request_id,
        })
    }
}
