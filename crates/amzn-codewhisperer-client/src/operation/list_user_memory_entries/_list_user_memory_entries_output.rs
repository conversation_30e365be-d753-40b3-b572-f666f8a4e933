// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ListUserMemoryEntriesOutput {
    /// List of user memories
    pub memory_entries: ::std::vec::Vec<crate::types::MemoryEntry>,
    #[allow(missing_docs)] // documentation missing in model
    pub next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListUserMemoryEntriesOutput {
    /// List of user memories
    pub fn memory_entries(&self) -> &[crate::types::MemoryEntry] {
        use std::ops::Deref;
        self.memory_entries.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(&self) -> ::std::option::Option<&str> {
        self.next_token.as_deref()
    }
}
impl ::aws_types::request_id::RequestId for ListUserMemoryEntriesOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl ListUserMemoryEntriesOutput {
    /// Creates a new builder-style object to manufacture
    /// [`ListUserMemoryEntriesOutput`](crate::operation::list_user_memory_entries::ListUserMemoryEntriesOutput).
    pub fn builder() -> crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesOutputBuilder {
        crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesOutputBuilder::default()
    }
}

/// A builder for
/// [`ListUserMemoryEntriesOutput`](crate::operation::list_user_memory_entries::ListUserMemoryEntriesOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ListUserMemoryEntriesOutputBuilder {
    pub(crate) memory_entries: ::std::option::Option<::std::vec::Vec<crate::types::MemoryEntry>>,
    pub(crate) next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListUserMemoryEntriesOutputBuilder {
    /// Appends an item to `memory_entries`.
    ///
    /// To override the contents of this collection use
    /// [`set_memory_entries`](Self::set_memory_entries).
    ///
    /// List of user memories
    pub fn memory_entries(mut self, input: crate::types::MemoryEntry) -> Self {
        let mut v = self.memory_entries.unwrap_or_default();
        v.push(input);
        self.memory_entries = ::std::option::Option::Some(v);
        self
    }

    /// List of user memories
    pub fn set_memory_entries(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::MemoryEntry>>,
    ) -> Self {
        self.memory_entries = input;
        self
    }

    /// List of user memories
    pub fn get_memory_entries(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::MemoryEntry>> {
        &self.memory_entries
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.next_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.next_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.next_token
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ListUserMemoryEntriesOutput`](crate::operation::list_user_memory_entries::ListUserMemoryEntriesOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`memory_entries`](crate::operation::list_user_memory_entries::builders::ListUserMemoryEntriesOutputBuilder::memory_entries)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_user_memory_entries::ListUserMemoryEntriesOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(
            crate::operation::list_user_memory_entries::ListUserMemoryEntriesOutput {
                memory_entries: self.memory_entries.ok_or_else(|| {
                    ::aws_smithy_types::error::operation::BuildError::missing_field(
                        "memory_entries",
                        "memory_entries was not specified but it is required when building ListUserMemoryEntriesOutput",
                    )
                })?,
                next_token: self.next_token,
                _request_id: self._request_id,
            },
        )
    }
}
