// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct DeleteWorkspaceOutput {
    _request_id: Option<String>,
}
impl ::aws_types::request_id::RequestId for DeleteWorkspaceOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl DeleteWorkspaceOutput {
    /// Creates a new builder-style object to manufacture
    /// [`DeleteWorkspaceOutput`](crate::operation::delete_workspace::DeleteWorkspaceOutput).
    pub fn builder() -> crate::operation::delete_workspace::builders::DeleteWorkspaceOutputBuilder {
        crate::operation::delete_workspace::builders::DeleteWorkspaceOutputBuilder::default()
    }
}

/// A builder for
/// [`DeleteWorkspaceOutput`](crate::operation::delete_workspace::DeleteWorkspaceOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct DeleteWorkspaceOutputBuilder {
    _request_id: Option<String>,
}
impl DeleteWorkspaceOutputBuilder {
    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`DeleteWorkspaceOutput`](crate::operation::delete_workspace::DeleteWorkspaceOutput).
    pub fn build(self) -> crate::operation::delete_workspace::DeleteWorkspaceOutput {
        crate::operation::delete_workspace::DeleteWorkspaceOutput {
            _request_id: self._request_id,
        }
    }
}
