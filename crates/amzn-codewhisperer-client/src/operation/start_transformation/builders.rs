// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::start_transformation::_start_transformation_input::StartTransformationInputBuilder;
pub use crate::operation::start_transformation::_start_transformation_output::StartTransformationOutputBuilder;

impl crate::operation::start_transformation::builders::StartTransformationInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::start_transformation::StartTransformationOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_transformation::StartTransformationError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.start_transformation();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `StartTransformation`.
///
/// API to start code translation.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct StartTransformationFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::start_transformation::builders::StartTransformationInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::start_transformation::StartTransformationOutput,
        crate::operation::start_transformation::StartTransformationError,
    > for StartTransformationFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::start_transformation::StartTransformationOutput,
            crate::operation::start_transformation::StartTransformationError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl StartTransformationFluentBuilder {
    /// Creates a new `StartTransformationFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the StartTransformation as a reference.
    pub fn as_input(&self) -> &crate::operation::start_transformation::builders::StartTransformationInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::start_transformation::StartTransformationOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_transformation::StartTransformationError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins = crate::operation::start_transformation::StartTransformation::operation_runtime_plugins(
            self.handle.runtime_plugins.clone(),
            &self.handle.conf,
            self.config_override,
        );
        crate::operation::start_transformation::StartTransformation::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::start_transformation::StartTransformationOutput,
        crate::operation::start_transformation::StartTransformationError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    /// Represents a Workspace state uploaded to S3 for Async Code Actions
    pub fn workspace_state(mut self, input: crate::types::WorkspaceState) -> Self {
        self.inner = self.inner.workspace_state(input);
        self
    }

    /// Represents a Workspace state uploaded to S3 for Async Code Actions
    pub fn set_workspace_state(mut self, input: ::std::option::Option<crate::types::WorkspaceState>) -> Self {
        self.inner = self.inner.set_workspace_state(input);
        self
    }

    /// Represents a Workspace state uploaded to S3 for Async Code Actions
    pub fn get_workspace_state(&self) -> &::std::option::Option<crate::types::WorkspaceState> {
        self.inner.get_workspace_state()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn transformation_spec(mut self, input: crate::types::TransformationSpec) -> Self {
        self.inner = self.inner.transformation_spec(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_transformation_spec(mut self, input: ::std::option::Option<crate::types::TransformationSpec>) -> Self {
        self.inner = self.inner.set_transformation_spec(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_transformation_spec(&self) -> &::std::option::Option<crate::types::TransformationSpec> {
        self.inner.get_transformation_spec()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }
}
