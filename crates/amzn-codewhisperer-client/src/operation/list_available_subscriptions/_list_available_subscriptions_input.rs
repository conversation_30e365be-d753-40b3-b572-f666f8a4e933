// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ListAvailableSubscriptionsInput {}
impl ListAvailableSubscriptionsInput {
    /// Creates a new builder-style object to manufacture
    /// [`ListAvailableSubscriptionsInput`](crate::operation::list_available_subscriptions::ListAvailableSubscriptionsInput).
    pub fn builder() -> crate::operation::list_available_subscriptions::builders::ListAvailableSubscriptionsInputBuilder
    {
        crate::operation::list_available_subscriptions::builders::ListAvailableSubscriptionsInputBuilder::default()
    }
}

/// A builder for
/// [`ListAvailableSubscriptionsInput`](crate::operation::list_available_subscriptions::ListAvailableSubscriptionsInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ListAvailableSubscriptionsInputBuilder {}
impl ListAvailableSubscriptionsInputBuilder {
    /// Consumes the builder and constructs a
    /// [`ListAvailableSubscriptionsInput`](crate::operation::list_available_subscriptions::ListAvailableSubscriptionsInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_available_subscriptions::ListAvailableSubscriptionsInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::list_available_subscriptions::ListAvailableSubscriptionsInput {})
    }
}
