// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ListWorkspaceMetadataOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub workspaces: ::std::vec::Vec<crate::types::WorkspaceMetadata>,
    #[allow(missing_docs)] // documentation missing in model
    pub next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListWorkspaceMetadataOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn workspaces(&self) -> &[crate::types::WorkspaceMetadata] {
        use std::ops::Deref;
        self.workspaces.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(&self) -> ::std::option::Option<&str> {
        self.next_token.as_deref()
    }
}
impl ::aws_types::request_id::RequestId for ListWorkspaceMetadataOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl ListWorkspaceMetadataOutput {
    /// Creates a new builder-style object to manufacture
    /// [`ListWorkspaceMetadataOutput`](crate::operation::list_workspace_metadata::ListWorkspaceMetadataOutput).
    pub fn builder() -> crate::operation::list_workspace_metadata::builders::ListWorkspaceMetadataOutputBuilder {
        crate::operation::list_workspace_metadata::builders::ListWorkspaceMetadataOutputBuilder::default()
    }
}

/// A builder for
/// [`ListWorkspaceMetadataOutput`](crate::operation::list_workspace_metadata::ListWorkspaceMetadataOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ListWorkspaceMetadataOutputBuilder {
    pub(crate) workspaces: ::std::option::Option<::std::vec::Vec<crate::types::WorkspaceMetadata>>,
    pub(crate) next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListWorkspaceMetadataOutputBuilder {
    /// Appends an item to `workspaces`.
    ///
    /// To override the contents of this collection use [`set_workspaces`](Self::set_workspaces).
    pub fn workspaces(mut self, input: crate::types::WorkspaceMetadata) -> Self {
        let mut v = self.workspaces.unwrap_or_default();
        v.push(input);
        self.workspaces = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_workspaces(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::WorkspaceMetadata>>,
    ) -> Self {
        self.workspaces = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_workspaces(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::WorkspaceMetadata>> {
        &self.workspaces
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.next_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.next_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.next_token
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ListWorkspaceMetadataOutput`](crate::operation::list_workspace_metadata::ListWorkspaceMetadataOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`workspaces`](crate::operation::list_workspace_metadata::builders::ListWorkspaceMetadataOutputBuilder::workspaces)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_workspace_metadata::ListWorkspaceMetadataOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::list_workspace_metadata::ListWorkspaceMetadataOutput {
            workspaces: self.workspaces.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "workspaces",
                    "workspaces was not specified but it is required when building ListWorkspaceMetadataOutput",
                )
            })?,
            next_token: self.next_token,
            _request_id: self._request_id,
        })
    }
}
