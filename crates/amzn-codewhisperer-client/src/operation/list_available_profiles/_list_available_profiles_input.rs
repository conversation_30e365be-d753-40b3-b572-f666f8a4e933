// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ListAvailableProfilesInput {
    #[allow(missing_docs)] // documentation missing in model
    pub max_results: ::std::option::Option<i32>,
    #[allow(missing_docs)] // documentation missing in model
    pub next_token: ::std::option::Option<::std::string::String>,
}
impl ListAvailableProfilesInput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn max_results(&self) -> ::std::option::Option<i32> {
        self.max_results
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(&self) -> ::std::option::Option<&str> {
        self.next_token.as_deref()
    }
}
impl ListAvailableProfilesInput {
    /// Creates a new builder-style object to manufacture
    /// [`ListAvailableProfilesInput`](crate::operation::list_available_profiles::ListAvailableProfilesInput).
    pub fn builder() -> crate::operation::list_available_profiles::builders::ListAvailableProfilesInputBuilder {
        crate::operation::list_available_profiles::builders::ListAvailableProfilesInputBuilder::default()
    }
}

/// A builder for
/// [`ListAvailableProfilesInput`](crate::operation::list_available_profiles::ListAvailableProfilesInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ListAvailableProfilesInputBuilder {
    pub(crate) max_results: ::std::option::Option<i32>,
    pub(crate) next_token: ::std::option::Option<::std::string::String>,
}
impl ListAvailableProfilesInputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn max_results(mut self, input: i32) -> Self {
        self.max_results = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_max_results(mut self, input: ::std::option::Option<i32>) -> Self {
        self.max_results = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_max_results(&self) -> &::std::option::Option<i32> {
        &self.max_results
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.next_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.next_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.next_token
    }

    /// Consumes the builder and constructs a
    /// [`ListAvailableProfilesInput`](crate::operation::list_available_profiles::ListAvailableProfilesInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_available_profiles::ListAvailableProfilesInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::list_available_profiles::ListAvailableProfilesInput {
            max_results: self.max_results,
            next_token: self.next_token,
        })
    }
}
