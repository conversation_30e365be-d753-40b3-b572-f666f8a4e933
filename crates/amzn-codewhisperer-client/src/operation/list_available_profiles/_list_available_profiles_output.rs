// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ListAvailableProfilesOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub profiles: ::std::vec::Vec<crate::types::Profile>,
    #[allow(missing_docs)] // documentation missing in model
    pub next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListAvailableProfilesOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn profiles(&self) -> &[crate::types::Profile] {
        use std::ops::Deref;
        self.profiles.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(&self) -> ::std::option::Option<&str> {
        self.next_token.as_deref()
    }
}
impl ::aws_types::request_id::RequestId for ListAvailableProfilesOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl ListAvailableProfilesOutput {
    /// Creates a new builder-style object to manufacture
    /// [`ListAvailableProfilesOutput`](crate::operation::list_available_profiles::ListAvailableProfilesOutput).
    pub fn builder() -> crate::operation::list_available_profiles::builders::ListAvailableProfilesOutputBuilder {
        crate::operation::list_available_profiles::builders::ListAvailableProfilesOutputBuilder::default()
    }
}

/// A builder for
/// [`ListAvailableProfilesOutput`](crate::operation::list_available_profiles::ListAvailableProfilesOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ListAvailableProfilesOutputBuilder {
    pub(crate) profiles: ::std::option::Option<::std::vec::Vec<crate::types::Profile>>,
    pub(crate) next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListAvailableProfilesOutputBuilder {
    /// Appends an item to `profiles`.
    ///
    /// To override the contents of this collection use [`set_profiles`](Self::set_profiles).
    pub fn profiles(mut self, input: crate::types::Profile) -> Self {
        let mut v = self.profiles.unwrap_or_default();
        v.push(input);
        self.profiles = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profiles(mut self, input: ::std::option::Option<::std::vec::Vec<crate::types::Profile>>) -> Self {
        self.profiles = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profiles(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::Profile>> {
        &self.profiles
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.next_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.next_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.next_token
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ListAvailableProfilesOutput`](crate::operation::list_available_profiles::ListAvailableProfilesOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`profiles`](crate::operation::list_available_profiles::builders::ListAvailableProfilesOutputBuilder::profiles)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_available_profiles::ListAvailableProfilesOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::list_available_profiles::ListAvailableProfilesOutput {
            profiles: self.profiles.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "profiles",
                    "profiles was not specified but it is required when building ListAvailableProfilesOutput",
                )
            })?,
            next_token: self.next_token,
            _request_id: self._request_id,
        })
    }
}
