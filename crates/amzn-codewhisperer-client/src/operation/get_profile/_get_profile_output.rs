// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct GetProfileOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub profile: crate::types::ProfileInfo,
    _request_id: Option<String>,
}
impl GetProfileOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn profile(&self) -> &crate::types::ProfileInfo {
        &self.profile
    }
}
impl ::aws_types::request_id::RequestId for GetProfileOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl GetProfileOutput {
    /// Creates a new builder-style object to manufacture
    /// [`GetProfileOutput`](crate::operation::get_profile::GetProfileOutput).
    pub fn builder() -> crate::operation::get_profile::builders::GetProfileOutputBuilder {
        crate::operation::get_profile::builders::GetProfileOutputBuilder::default()
    }
}

/// A builder for [`GetProfileOutput`](crate::operation::get_profile::GetProfileOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct GetProfileOutputBuilder {
    pub(crate) profile: ::std::option::Option<crate::types::ProfileInfo>,
    _request_id: Option<String>,
}
impl GetProfileOutputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn profile(mut self, input: crate::types::ProfileInfo) -> Self {
        self.profile = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile(mut self, input: ::std::option::Option<crate::types::ProfileInfo>) -> Self {
        self.profile = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile(&self) -> &::std::option::Option<crate::types::ProfileInfo> {
        &self.profile
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`GetProfileOutput`](crate::operation::get_profile::GetProfileOutput). This method will
    /// fail if any of the following fields are not set:
    /// - [`profile`](crate::operation::get_profile::builders::GetProfileOutputBuilder::profile)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::get_profile::GetProfileOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::get_profile::GetProfileOutput {
            profile: self.profile.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "profile",
                    "profile was not specified but it is required when building GetProfileOutput",
                )
            })?,
            _request_id: self._request_id,
        })
    }
}
