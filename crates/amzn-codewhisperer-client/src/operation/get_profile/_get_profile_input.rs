// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct GetProfileInput {
    #[allow(missing_docs)] // documentation missing in model
    pub profile_arn: ::std::option::Option<::std::string::String>,
}
impl GetProfileInput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(&self) -> ::std::option::Option<&str> {
        self.profile_arn.as_deref()
    }
}
impl GetProfileInput {
    /// Creates a new builder-style object to manufacture
    /// [`GetProfileInput`](crate::operation::get_profile::GetProfileInput).
    pub fn builder() -> crate::operation::get_profile::builders::GetProfileInputBuilder {
        crate::operation::get_profile::builders::GetProfileInputBuilder::default()
    }
}

/// A builder for [`GetProfileInput`](crate::operation::get_profile::GetProfileInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct GetProfileInputBuilder {
    pub(crate) profile_arn: ::std::option::Option<::std::string::String>,
}
impl GetProfileInputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.profile_arn = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.profile_arn = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.profile_arn
    }

    /// Consumes the builder and constructs a
    /// [`GetProfileInput`](crate::operation::get_profile::GetProfileInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::get_profile::GetProfileInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::get_profile::GetProfileInput {
            profile_arn: self.profile_arn,
        })
    }
}
