// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct CreateUserMemoryEntryOutput {
    /// MemoryEntry corresponds to a single user memory
    pub memory_entry: crate::types::MemoryEntry,
    _request_id: Option<String>,
}
impl CreateUserMemoryEntryOutput {
    /// MemoryEntry corresponds to a single user memory
    pub fn memory_entry(&self) -> &crate::types::MemoryEntry {
        &self.memory_entry
    }
}
impl ::aws_types::request_id::RequestId for CreateUserMemoryEntryOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl CreateUserMemoryEntryOutput {
    /// Creates a new builder-style object to manufacture
    /// [`CreateUserMemoryEntryOutput`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput).
    pub fn builder() -> crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryOutputBuilder {
        crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryOutputBuilder::default()
    }
}

/// A builder for
/// [`CreateUserMemoryEntryOutput`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct CreateUserMemoryEntryOutputBuilder {
    pub(crate) memory_entry: ::std::option::Option<crate::types::MemoryEntry>,
    _request_id: Option<String>,
}
impl CreateUserMemoryEntryOutputBuilder {
    /// MemoryEntry corresponds to a single user memory
    /// This field is required.
    pub fn memory_entry(mut self, input: crate::types::MemoryEntry) -> Self {
        self.memory_entry = ::std::option::Option::Some(input);
        self
    }

    /// MemoryEntry corresponds to a single user memory
    pub fn set_memory_entry(mut self, input: ::std::option::Option<crate::types::MemoryEntry>) -> Self {
        self.memory_entry = input;
        self
    }

    /// MemoryEntry corresponds to a single user memory
    pub fn get_memory_entry(&self) -> &::std::option::Option<crate::types::MemoryEntry> {
        &self.memory_entry
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`CreateUserMemoryEntryOutput`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`memory_entry`](crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryOutputBuilder::memory_entry)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(
            crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput {
                memory_entry: self.memory_entry.ok_or_else(|| {
                    ::aws_smithy_types::error::operation::BuildError::missing_field(
                        "memory_entry",
                        "memory_entry was not specified but it is required when building CreateUserMemoryEntryOutput",
                    )
                })?,
                _request_id: self._request_id,
            },
        )
    }
}
