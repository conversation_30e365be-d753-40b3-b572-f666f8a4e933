// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct CreateUserMemoryEntryInput {
    #[allow(missing_docs)] // documentation missing in model
    pub memory_entry_string: ::std::option::Option<::std::string::String>,
    /// Enum to represent the origin application conversing with Sidekick.
    pub origin: ::std::option::Option<crate::types::Origin>,
    /// ProfileArn for the managing Q Profile
    pub profile_arn: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub client_token: ::std::option::Option<::std::string::String>,
}
impl CreateUserMemoryEntryInput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn memory_entry_string(&self) -> ::std::option::Option<&str> {
        self.memory_entry_string.as_deref()
    }

    /// Enum to represent the origin application conversing with Sidekick.
    pub fn origin(&self) -> ::std::option::Option<&crate::types::Origin> {
        self.origin.as_ref()
    }

    /// ProfileArn for the managing Q Profile
    pub fn profile_arn(&self) -> ::std::option::Option<&str> {
        self.profile_arn.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(&self) -> ::std::option::Option<&str> {
        self.client_token.as_deref()
    }
}
impl ::std::fmt::Debug for CreateUserMemoryEntryInput {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("CreateUserMemoryEntryInput");
        formatter.field("memory_entry_string", &"*** Sensitive Data Redacted ***");
        formatter.field("origin", &self.origin);
        formatter.field("profile_arn", &self.profile_arn);
        formatter.field("client_token", &self.client_token);
        formatter.finish()
    }
}
impl CreateUserMemoryEntryInput {
    /// Creates a new builder-style object to manufacture
    /// [`CreateUserMemoryEntryInput`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryInput).
    pub fn builder() -> crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryInputBuilder {
        crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryInputBuilder::default()
    }
}

/// A builder for
/// [`CreateUserMemoryEntryInput`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct CreateUserMemoryEntryInputBuilder {
    pub(crate) memory_entry_string: ::std::option::Option<::std::string::String>,
    pub(crate) origin: ::std::option::Option<crate::types::Origin>,
    pub(crate) profile_arn: ::std::option::Option<::std::string::String>,
    pub(crate) client_token: ::std::option::Option<::std::string::String>,
}
impl CreateUserMemoryEntryInputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn memory_entry_string(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.memory_entry_string = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_memory_entry_string(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.memory_entry_string = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_memory_entry_string(&self) -> &::std::option::Option<::std::string::String> {
        &self.memory_entry_string
    }

    /// Enum to represent the origin application conversing with Sidekick.
    /// This field is required.
    pub fn origin(mut self, input: crate::types::Origin) -> Self {
        self.origin = ::std::option::Option::Some(input);
        self
    }

    /// Enum to represent the origin application conversing with Sidekick.
    pub fn set_origin(mut self, input: ::std::option::Option<crate::types::Origin>) -> Self {
        self.origin = input;
        self
    }

    /// Enum to represent the origin application conversing with Sidekick.
    pub fn get_origin(&self) -> &::std::option::Option<crate::types::Origin> {
        &self.origin
    }

    /// ProfileArn for the managing Q Profile
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.profile_arn = ::std::option::Option::Some(input.into());
        self
    }

    /// ProfileArn for the managing Q Profile
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.profile_arn = input;
        self
    }

    /// ProfileArn for the managing Q Profile
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.profile_arn
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.client_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_client_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.client_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_client_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.client_token
    }

    /// Consumes the builder and constructs a
    /// [`CreateUserMemoryEntryInput`](crate::operation::create_user_memory_entry::CreateUserMemoryEntryInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::create_user_memory_entry::CreateUserMemoryEntryInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::create_user_memory_entry::CreateUserMemoryEntryInput {
            memory_entry_string: self.memory_entry_string,
            origin: self.origin,
            profile_arn: self.profile_arn,
            client_token: self.client_token,
        })
    }
}
impl ::std::fmt::Debug for CreateUserMemoryEntryInputBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("CreateUserMemoryEntryInputBuilder");
        formatter.field("memory_entry_string", &"*** Sensitive Data Redacted ***");
        formatter.field("origin", &self.origin);
        formatter.field("profile_arn", &self.profile_arn);
        formatter.field("client_token", &self.client_token);
        formatter.finish()
    }
}
