// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::create_user_memory_entry::_create_user_memory_entry_input::CreateUserMemoryEntryInputBuilder;
pub use crate::operation::create_user_memory_entry::_create_user_memory_entry_output::CreateUserMemoryEntryOutputBuilder;

impl crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::create_user_memory_entry::CreateUserMemoryEntryError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.create_user_memory_entry();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `CreateUserMemoryEntry`.
///
/// API to create a single user memory entry
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct CreateUserMemoryEntryFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput,
        crate::operation::create_user_memory_entry::CreateUserMemoryEntryError,
    > for CreateUserMemoryEntryFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput,
            crate::operation::create_user_memory_entry::CreateUserMemoryEntryError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl CreateUserMemoryEntryFluentBuilder {
    /// Creates a new `CreateUserMemoryEntryFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the CreateUserMemoryEntry as a reference.
    pub fn as_input(&self) -> &crate::operation::create_user_memory_entry::builders::CreateUserMemoryEntryInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::create_user_memory_entry::CreateUserMemoryEntryError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins =
            crate::operation::create_user_memory_entry::CreateUserMemoryEntry::operation_runtime_plugins(
                self.handle.runtime_plugins.clone(),
                &self.handle.conf,
                self.config_override,
            );
        crate::operation::create_user_memory_entry::CreateUserMemoryEntry::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::create_user_memory_entry::CreateUserMemoryEntryOutput,
        crate::operation::create_user_memory_entry::CreateUserMemoryEntryError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn memory_entry_string(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.memory_entry_string(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_memory_entry_string(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_memory_entry_string(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_memory_entry_string(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_memory_entry_string()
    }

    /// Enum to represent the origin application conversing with Sidekick.
    pub fn origin(mut self, input: crate::types::Origin) -> Self {
        self.inner = self.inner.origin(input);
        self
    }

    /// Enum to represent the origin application conversing with Sidekick.
    pub fn set_origin(mut self, input: ::std::option::Option<crate::types::Origin>) -> Self {
        self.inner = self.inner.set_origin(input);
        self
    }

    /// Enum to represent the origin application conversing with Sidekick.
    pub fn get_origin(&self) -> &::std::option::Option<crate::types::Origin> {
        self.inner.get_origin()
    }

    /// ProfileArn for the managing Q Profile
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    /// ProfileArn for the managing Q Profile
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    /// ProfileArn for the managing Q Profile
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.client_token(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_client_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_client_token(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_client_token(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_client_token()
    }
}
