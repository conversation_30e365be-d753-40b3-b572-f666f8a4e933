// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::create_subscription_token::_create_subscription_token_input::CreateSubscriptionTokenInputBuilder;
pub use crate::operation::create_subscription_token::_create_subscription_token_output::CreateSubscriptionTokenOutputBuilder;

impl crate::operation::create_subscription_token::builders::CreateSubscriptionTokenInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::create_subscription_token::CreateSubscriptionTokenOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::create_subscription_token::CreateSubscriptionTokenError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.create_subscription_token();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `CreateSubscriptionToken`.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct CreateSubscriptionTokenFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::create_subscription_token::builders::CreateSubscriptionTokenInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::create_subscription_token::CreateSubscriptionTokenOutput,
        crate::operation::create_subscription_token::CreateSubscriptionTokenError,
    > for CreateSubscriptionTokenFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::create_subscription_token::CreateSubscriptionTokenOutput,
            crate::operation::create_subscription_token::CreateSubscriptionTokenError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl CreateSubscriptionTokenFluentBuilder {
    /// Creates a new `CreateSubscriptionTokenFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the CreateSubscriptionToken as a reference.
    pub fn as_input(
        &self,
    ) -> &crate::operation::create_subscription_token::builders::CreateSubscriptionTokenInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::create_subscription_token::CreateSubscriptionTokenOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::create_subscription_token::CreateSubscriptionTokenError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins =
            crate::operation::create_subscription_token::CreateSubscriptionToken::operation_runtime_plugins(
                self.handle.runtime_plugins.clone(),
                &self.handle.conf,
                self.config_override,
            );
        crate::operation::create_subscription_token::CreateSubscriptionToken::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::create_subscription_token::CreateSubscriptionTokenOutput,
        crate::operation::create_subscription_token::CreateSubscriptionTokenError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.client_token(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_client_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_client_token(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_client_token(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_client_token()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn status_only(mut self, input: bool) -> Self {
        self.inner = self.inner.status_only(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_status_only(mut self, input: ::std::option::Option<bool>) -> Self {
        self.inner = self.inner.set_status_only(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_status_only(&self) -> &::std::option::Option<bool> {
        self.inner.get_status_only()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn provider(mut self, input: crate::types::SubscriptionProvider) -> Self {
        self.inner = self.inner.provider(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_provider(mut self, input: ::std::option::Option<crate::types::SubscriptionProvider>) -> Self {
        self.inner = self.inner.set_provider(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_provider(&self) -> &::std::option::Option<crate::types::SubscriptionProvider> {
        self.inner.get_provider()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn subscription_type(mut self, input: crate::types::SubscriptionType) -> Self {
        self.inner = self.inner.subscription_type(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_subscription_type(mut self, input: ::std::option::Option<crate::types::SubscriptionType>) -> Self {
        self.inner = self.inner.set_subscription_type(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_subscription_type(&self) -> &::std::option::Option<crate::types::SubscriptionType> {
        self.inner.get_subscription_type()
    }
}
