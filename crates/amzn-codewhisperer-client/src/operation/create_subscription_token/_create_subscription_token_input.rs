// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct CreateSubscriptionTokenInput {
    #[allow(missing_docs)] // documentation missing in model
    pub client_token: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub status_only: ::std::option::Option<bool>,
    #[allow(missing_docs)] // documentation missing in model
    pub provider: ::std::option::Option<crate::types::SubscriptionProvider>,
    #[allow(missing_docs)] // documentation missing in model
    pub subscription_type: ::std::option::Option<crate::types::SubscriptionType>,
}
impl CreateSubscriptionTokenInput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(&self) -> ::std::option::Option<&str> {
        self.client_token.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn status_only(&self) -> ::std::option::Option<bool> {
        self.status_only
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn provider(&self) -> ::std::option::Option<&crate::types::SubscriptionProvider> {
        self.provider.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn subscription_type(&self) -> ::std::option::Option<&crate::types::SubscriptionType> {
        self.subscription_type.as_ref()
    }
}
impl CreateSubscriptionTokenInput {
    /// Creates a new builder-style object to manufacture
    /// [`CreateSubscriptionTokenInput`](crate::operation::create_subscription_token::CreateSubscriptionTokenInput).
    pub fn builder() -> crate::operation::create_subscription_token::builders::CreateSubscriptionTokenInputBuilder {
        crate::operation::create_subscription_token::builders::CreateSubscriptionTokenInputBuilder::default()
    }
}

/// A builder for
/// [`CreateSubscriptionTokenInput`](crate::operation::create_subscription_token::CreateSubscriptionTokenInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct CreateSubscriptionTokenInputBuilder {
    pub(crate) client_token: ::std::option::Option<::std::string::String>,
    pub(crate) status_only: ::std::option::Option<bool>,
    pub(crate) provider: ::std::option::Option<crate::types::SubscriptionProvider>,
    pub(crate) subscription_type: ::std::option::Option<crate::types::SubscriptionType>,
}
impl CreateSubscriptionTokenInputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.client_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_client_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.client_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_client_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.client_token
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn status_only(mut self, input: bool) -> Self {
        self.status_only = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_status_only(mut self, input: ::std::option::Option<bool>) -> Self {
        self.status_only = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_status_only(&self) -> &::std::option::Option<bool> {
        &self.status_only
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn provider(mut self, input: crate::types::SubscriptionProvider) -> Self {
        self.provider = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_provider(mut self, input: ::std::option::Option<crate::types::SubscriptionProvider>) -> Self {
        self.provider = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_provider(&self) -> &::std::option::Option<crate::types::SubscriptionProvider> {
        &self.provider
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn subscription_type(mut self, input: crate::types::SubscriptionType) -> Self {
        self.subscription_type = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_subscription_type(mut self, input: ::std::option::Option<crate::types::SubscriptionType>) -> Self {
        self.subscription_type = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_subscription_type(&self) -> &::std::option::Option<crate::types::SubscriptionType> {
        &self.subscription_type
    }

    /// Consumes the builder and constructs a
    /// [`CreateSubscriptionTokenInput`](crate::operation::create_subscription_token::CreateSubscriptionTokenInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::create_subscription_token::CreateSubscriptionTokenInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(
            crate::operation::create_subscription_token::CreateSubscriptionTokenInput {
                client_token: self.client_token,
                status_only: self.status_only,
                provider: self.provider,
                subscription_type: self.subscription_type,
            },
        )
    }
}
