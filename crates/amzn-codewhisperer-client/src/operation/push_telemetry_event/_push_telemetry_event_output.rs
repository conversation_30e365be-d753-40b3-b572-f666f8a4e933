// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct PushTelemetryEventOutput {
    _request_id: Option<String>,
}
impl ::aws_types::request_id::RequestId for PushTelemetryEventOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl PushTelemetryEventOutput {
    /// Creates a new builder-style object to manufacture
    /// [`PushTelemetryEventOutput`](crate::operation::push_telemetry_event::PushTelemetryEventOutput).
    pub fn builder() -> crate::operation::push_telemetry_event::builders::PushTelemetryEventOutputBuilder {
        crate::operation::push_telemetry_event::builders::PushTelemetryEventOutputBuilder::default()
    }
}

/// A builder for
/// [`PushTelemetryEventOutput`](crate::operation::push_telemetry_event::PushTelemetryEventOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct PushTelemetryEventOutputBuilder {
    _request_id: Option<String>,
}
impl PushTelemetryEventOutputBuilder {
    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`PushTelemetryEventOutput`](crate::operation::push_telemetry_event::PushTelemetryEventOutput).
    pub fn build(self) -> crate::operation::push_telemetry_event::PushTelemetryEventOutput {
        crate::operation::push_telemetry_event::PushTelemetryEventOutput {
            _request_id: self._request_id,
        }
    }
}
