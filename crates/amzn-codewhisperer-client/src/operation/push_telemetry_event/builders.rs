// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::push_telemetry_event::_push_telemetry_event_input::PushTelemetryEventInputBuilder;
pub use crate::operation::push_telemetry_event::_push_telemetry_event_output::PushTelemetryEventOutputBuilder;

impl crate::operation::push_telemetry_event::builders::PushTelemetryEventInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::push_telemetry_event::PushTelemetryEventOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::push_telemetry_event::PushTelemetryEventError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.push_telemetry_event();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `PushTelemetryEvent`.
///
/// API to push telemetry events to CloudWatch, DataHub and EventBridge.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct PushTelemetryEventFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::push_telemetry_event::builders::PushTelemetryEventInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::push_telemetry_event::PushTelemetryEventOutput,
        crate::operation::push_telemetry_event::PushTelemetryEventError,
    > for PushTelemetryEventFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::push_telemetry_event::PushTelemetryEventOutput,
            crate::operation::push_telemetry_event::PushTelemetryEventError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl PushTelemetryEventFluentBuilder {
    /// Creates a new `PushTelemetryEventFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the PushTelemetryEvent as a reference.
    pub fn as_input(&self) -> &crate::operation::push_telemetry_event::builders::PushTelemetryEventInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::push_telemetry_event::PushTelemetryEventOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::push_telemetry_event::PushTelemetryEventError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins = crate::operation::push_telemetry_event::PushTelemetryEvent::operation_runtime_plugins(
            self.handle.runtime_plugins.clone(),
            &self.handle.conf,
            self.config_override,
        );
        crate::operation::push_telemetry_event::PushTelemetryEvent::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::push_telemetry_event::PushTelemetryEventOutput,
        crate::operation::push_telemetry_event::PushTelemetryEventError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.client_token(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_client_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_client_token(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_client_token(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_client_token()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn event_type(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.event_type(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_event_type(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_event_type(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_event_type(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_event_type()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn event(mut self, input: ::aws_smithy_types::Document) -> Self {
        self.inner = self.inner.event(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_event(mut self, input: ::std::option::Option<::aws_smithy_types::Document>) -> Self {
        self.inner = self.inner.set_event(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_event(&self) -> &::std::option::Option<::aws_smithy_types::Document> {
        self.inner.get_event()
    }
}
