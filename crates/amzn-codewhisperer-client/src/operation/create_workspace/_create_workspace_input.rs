// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct CreateWorkspaceInput {
    #[allow(missing_docs)] // documentation missing in model
    pub workspace_root: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub profile_arn: ::std::option::Option<::std::string::String>,
}
impl CreateWorkspaceInput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn workspace_root(&self) -> ::std::option::Option<&str> {
        self.workspace_root.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(&self) -> ::std::option::Option<&str> {
        self.profile_arn.as_deref()
    }
}
impl ::std::fmt::Debug for CreateWorkspaceInput {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("CreateWorkspaceInput");
        formatter.field("workspace_root", &"*** Sensitive Data Redacted ***");
        formatter.field("profile_arn", &self.profile_arn);
        formatter.finish()
    }
}
impl CreateWorkspaceInput {
    /// Creates a new builder-style object to manufacture
    /// [`CreateWorkspaceInput`](crate::operation::create_workspace::CreateWorkspaceInput).
    pub fn builder() -> crate::operation::create_workspace::builders::CreateWorkspaceInputBuilder {
        crate::operation::create_workspace::builders::CreateWorkspaceInputBuilder::default()
    }
}

/// A builder for
/// [`CreateWorkspaceInput`](crate::operation::create_workspace::CreateWorkspaceInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct CreateWorkspaceInputBuilder {
    pub(crate) workspace_root: ::std::option::Option<::std::string::String>,
    pub(crate) profile_arn: ::std::option::Option<::std::string::String>,
}
impl CreateWorkspaceInputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn workspace_root(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.workspace_root = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_workspace_root(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.workspace_root = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_workspace_root(&self) -> &::std::option::Option<::std::string::String> {
        &self.workspace_root
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.profile_arn = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.profile_arn = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.profile_arn
    }

    /// Consumes the builder and constructs a
    /// [`CreateWorkspaceInput`](crate::operation::create_workspace::CreateWorkspaceInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::create_workspace::CreateWorkspaceInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::create_workspace::CreateWorkspaceInput {
            workspace_root: self.workspace_root,
            profile_arn: self.profile_arn,
        })
    }
}
impl ::std::fmt::Debug for CreateWorkspaceInputBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("CreateWorkspaceInputBuilder");
        formatter.field("workspace_root", &"*** Sensitive Data Redacted ***");
        formatter.field("profile_arn", &self.profile_arn);
        formatter.finish()
    }
}
