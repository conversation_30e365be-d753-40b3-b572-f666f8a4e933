// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct DeleteUserMemoryEntryOutput {
    _request_id: Option<String>,
}
impl ::aws_types::request_id::RequestId for DeleteUserMemoryEntryOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl DeleteUserMemoryEntryOutput {
    /// Creates a new builder-style object to manufacture
    /// [`DeleteUserMemoryEntryOutput`](crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryOutput).
    pub fn builder() -> crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryOutputBuilder {
        crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryOutputBuilder::default()
    }
}

/// A builder for
/// [`DeleteUserMemoryEntryOutput`](crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct DeleteUserMemoryEntryOutputBuilder {
    _request_id: Option<String>,
}
impl DeleteUserMemoryEntryOutputBuilder {
    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`DeleteUserMemoryEntryOutput`](crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryOutput).
    pub fn build(self) -> crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryOutput {
        crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryOutput {
            _request_id: self._request_id,
        }
    }
}
