// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct DeleteUserMemoryEntryInput {
    #[allow(missing_docs)] // documentation missing in model
    pub id: ::std::option::Option<::std::string::String>,
    /// ProfileArn for the managing Q Profile
    pub profile_arn: ::std::option::Option<::std::string::String>,
}
impl DeleteUserMemoryEntryInput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn id(&self) -> ::std::option::Option<&str> {
        self.id.as_deref()
    }

    /// ProfileArn for the managing Q Profile
    pub fn profile_arn(&self) -> ::std::option::Option<&str> {
        self.profile_arn.as_deref()
    }
}
impl DeleteUserMemoryEntryInput {
    /// Creates a new builder-style object to manufacture
    /// [`DeleteUserMemoryEntryInput`](crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryInput).
    pub fn builder() -> crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryInputBuilder {
        crate::operation::delete_user_memory_entry::builders::DeleteUserMemoryEntryInputBuilder::default()
    }
}

/// A builder for
/// [`DeleteUserMemoryEntryInput`](crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct DeleteUserMemoryEntryInputBuilder {
    pub(crate) id: ::std::option::Option<::std::string::String>,
    pub(crate) profile_arn: ::std::option::Option<::std::string::String>,
}
impl DeleteUserMemoryEntryInputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.id = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.id = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.id
    }

    /// ProfileArn for the managing Q Profile
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.profile_arn = ::std::option::Option::Some(input.into());
        self
    }

    /// ProfileArn for the managing Q Profile
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.profile_arn = input;
        self
    }

    /// ProfileArn for the managing Q Profile
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.profile_arn
    }

    /// Consumes the builder and constructs a
    /// [`DeleteUserMemoryEntryInput`](crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::delete_user_memory_entry::DeleteUserMemoryEntryInput {
            id: self.id,
            profile_arn: self.profile_arn,
        })
    }
}
