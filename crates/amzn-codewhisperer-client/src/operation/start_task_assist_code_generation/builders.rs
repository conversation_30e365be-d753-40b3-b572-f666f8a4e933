// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::start_task_assist_code_generation::_start_task_assist_code_generation_input::StartTaskAssistCodeGenerationInputBuilder;
pub use crate::operation::start_task_assist_code_generation::_start_task_assist_code_generation_output::StartTaskAssistCodeGenerationOutputBuilder;

impl crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.start_task_assist_code_generation();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `StartTaskAssistCodeGeneration`.
///
/// API to start task assist code generation.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct StartTaskAssistCodeGenerationFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput,
        crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationError,
    > for StartTaskAssistCodeGenerationFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput,
            crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl StartTaskAssistCodeGenerationFluentBuilder {
    /// Creates a new `StartTaskAssistCodeGenerationFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the StartTaskAssistCodeGeneration as a reference.
    pub fn as_input(
        &self,
    ) -> &crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins = crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGeneration::operation_runtime_plugins(
            self.handle.runtime_plugins.clone(),
            &self.handle.conf,
            self.config_override,
        );
        crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGeneration::orchestrate(
            &runtime_plugins,
            input,
        )
        .await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput,
        crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    /// Structure to represent the current state of a chat conversation.
    pub fn conversation_state(mut self, input: crate::types::ConversationState) -> Self {
        self.inner = self.inner.conversation_state(input);
        self
    }

    /// Structure to represent the current state of a chat conversation.
    pub fn set_conversation_state(mut self, input: ::std::option::Option<crate::types::ConversationState>) -> Self {
        self.inner = self.inner.set_conversation_state(input);
        self
    }

    /// Structure to represent the current state of a chat conversation.
    pub fn get_conversation_state(&self) -> &::std::option::Option<crate::types::ConversationState> {
        self.inner.get_conversation_state()
    }

    /// Represents a Workspace state uploaded to S3 for Async Code Actions
    pub fn workspace_state(mut self, input: crate::types::WorkspaceState) -> Self {
        self.inner = self.inner.workspace_state(input);
        self
    }

    /// Represents a Workspace state uploaded to S3 for Async Code Actions
    pub fn set_workspace_state(mut self, input: ::std::option::Option<crate::types::WorkspaceState>) -> Self {
        self.inner = self.inner.set_workspace_state(input);
        self
    }

    /// Represents a Workspace state uploaded to S3 for Async Code Actions
    pub fn get_workspace_state(&self) -> &::std::option::Option<crate::types::WorkspaceState> {
        self.inner.get_workspace_state()
    }

    /// Appends an item to `taskAssistPlan`.
    ///
    /// To override the contents of this collection use
    /// [`set_task_assist_plan`](Self::set_task_assist_plan).
    #[allow(missing_docs)] // documentation missing in model
    pub fn task_assist_plan(mut self, input: crate::types::TaskAssistPlanStep) -> Self {
        self.inner = self.inner.task_assist_plan(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_task_assist_plan(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::TaskAssistPlanStep>>,
    ) -> Self {
        self.inner = self.inner.set_task_assist_plan(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_task_assist_plan(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::TaskAssistPlanStep>> {
        self.inner.get_task_assist_plan()
    }

    /// ID which represents a single code generation in a conversation
    pub fn code_generation_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.code_generation_id(input.into());
        self
    }

    /// ID which represents a single code generation in a conversation
    pub fn set_code_generation_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_code_generation_id(input);
        self
    }

    /// ID which represents a single code generation in a conversation
    pub fn get_code_generation_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_code_generation_id()
    }

    /// ID which represents a single code generation in a conversation
    pub fn current_code_generation_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.current_code_generation_id(input.into());
        self
    }

    /// ID which represents a single code generation in a conversation
    pub fn set_current_code_generation_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_current_code_generation_id(input);
        self
    }

    /// ID which represents a single code generation in a conversation
    pub fn get_current_code_generation_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_current_code_generation_id()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn intent(mut self, input: crate::types::Intent) -> Self {
        self.inner = self.inner.intent(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_intent(mut self, input: ::std::option::Option<crate::types::Intent>) -> Self {
        self.inner = self.inner.set_intent(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_intent(&self) -> &::std::option::Option<crate::types::Intent> {
        self.inner.get_intent()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn intent_context(mut self, input: crate::types::IntentContext) -> Self {
        self.inner = self.inner.intent_context(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_intent_context(mut self, input: ::std::option::Option<crate::types::IntentContext>) -> Self {
        self.inner = self.inner.set_intent_context(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_intent_context(&self) -> &::std::option::Option<crate::types::IntentContext> {
        self.inner.get_intent_context()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }
}
