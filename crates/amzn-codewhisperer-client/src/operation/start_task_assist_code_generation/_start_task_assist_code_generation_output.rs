// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Structure to represent start code generation response.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct StartTaskAssistCodeGenerationOutput {
    /// ID which represents a multi-turn conversation
    pub conversation_id: ::std::string::String,
    /// ID which represents a single code generation in a conversation
    pub code_generation_id: ::std::string::String,
    _request_id: Option<String>,
}
impl StartTaskAssistCodeGenerationOutput {
    /// ID which represents a multi-turn conversation
    pub fn conversation_id(&self) -> &str {
        use std::ops::Deref;
        self.conversation_id.deref()
    }

    /// ID which represents a single code generation in a conversation
    pub fn code_generation_id(&self) -> &str {
        use std::ops::Deref;
        self.code_generation_id.deref()
    }
}
impl ::aws_types::request_id::RequestId for StartTaskAssistCodeGenerationOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl StartTaskAssistCodeGenerationOutput {
    /// Creates a new builder-style object to manufacture
    /// [`StartTaskAssistCodeGenerationOutput`](crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput).
    pub fn builder()
    -> crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationOutputBuilder {
        crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationOutputBuilder::default()
    }
}

/// A builder for
/// [`StartTaskAssistCodeGenerationOutput`](crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct StartTaskAssistCodeGenerationOutputBuilder {
    pub(crate) conversation_id: ::std::option::Option<::std::string::String>,
    pub(crate) code_generation_id: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl StartTaskAssistCodeGenerationOutputBuilder {
    /// ID which represents a multi-turn conversation
    /// This field is required.
    pub fn conversation_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.conversation_id = ::std::option::Option::Some(input.into());
        self
    }

    /// ID which represents a multi-turn conversation
    pub fn set_conversation_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.conversation_id = input;
        self
    }

    /// ID which represents a multi-turn conversation
    pub fn get_conversation_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.conversation_id
    }

    /// ID which represents a single code generation in a conversation
    /// This field is required.
    pub fn code_generation_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.code_generation_id = ::std::option::Option::Some(input.into());
        self
    }

    /// ID which represents a single code generation in a conversation
    pub fn set_code_generation_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.code_generation_id = input;
        self
    }

    /// ID which represents a single code generation in a conversation
    pub fn get_code_generation_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.code_generation_id
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`StartTaskAssistCodeGenerationOutput`](crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`conversation_id`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationOutputBuilder::conversation_id)
    /// - [`code_generation_id`](crate::operation::start_task_assist_code_generation::builders::StartTaskAssistCodeGenerationOutputBuilder::code_generation_id)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::start_task_assist_code_generation::StartTaskAssistCodeGenerationOutput {
            conversation_id: self.conversation_id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "conversation_id",
                    "conversation_id was not specified but it is required when building StartTaskAssistCodeGenerationOutput",
                )
            })?,
            code_generation_id: self.code_generation_id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "code_generation_id",
                    "code_generation_id was not specified but it is required when building StartTaskAssistCodeGenerationOutput",
                )
            })?,
            _request_id: self._request_id,
        })
    }
}
