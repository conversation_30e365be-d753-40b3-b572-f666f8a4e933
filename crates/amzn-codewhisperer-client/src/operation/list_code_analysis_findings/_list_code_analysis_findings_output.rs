// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct ListCodeAnalysisFindingsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub next_token: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub code_analysis_findings: ::std::string::String,
    _request_id: Option<String>,
}
impl ListCodeAnalysisFindingsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(&self) -> ::std::option::Option<&str> {
        self.next_token.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn code_analysis_findings(&self) -> &str {
        use std::ops::Deref;
        self.code_analysis_findings.deref()
    }
}
impl ::std::fmt::Debug for ListCodeAnalysisFindingsOutput {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("ListCodeAnalysisFindingsOutput");
        formatter.field("next_token", &self.next_token);
        formatter.field("code_analysis_findings", &"*** Sensitive Data Redacted ***");
        formatter.field("_request_id", &self._request_id);
        formatter.finish()
    }
}
impl ::aws_types::request_id::RequestId for ListCodeAnalysisFindingsOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl ListCodeAnalysisFindingsOutput {
    /// Creates a new builder-style object to manufacture
    /// [`ListCodeAnalysisFindingsOutput`](crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsOutput).
    pub fn builder() -> crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsOutputBuilder {
        crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsOutputBuilder::default()
    }
}

/// A builder for
/// [`ListCodeAnalysisFindingsOutput`](crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct ListCodeAnalysisFindingsOutputBuilder {
    pub(crate) next_token: ::std::option::Option<::std::string::String>,
    pub(crate) code_analysis_findings: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListCodeAnalysisFindingsOutputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.next_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.next_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.next_token
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn code_analysis_findings(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.code_analysis_findings = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_code_analysis_findings(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.code_analysis_findings = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_code_analysis_findings(&self) -> &::std::option::Option<::std::string::String> {
        &self.code_analysis_findings
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ListCodeAnalysisFindingsOutput`](crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`code_analysis_findings`](crate::operation::list_code_analysis_findings::builders::ListCodeAnalysisFindingsOutputBuilder::code_analysis_findings)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::list_code_analysis_findings::ListCodeAnalysisFindingsOutput {
            next_token: self.next_token,
            code_analysis_findings: self.code_analysis_findings.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "code_analysis_findings",
                    "code_analysis_findings was not specified but it is required when building ListCodeAnalysisFindingsOutput",
                )
            })?,
            _request_id: self._request_id,
        })
    }
}
impl ::std::fmt::Debug for ListCodeAnalysisFindingsOutputBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("ListCodeAnalysisFindingsOutputBuilder");
        formatter.field("next_token", &self.next_token);
        formatter.field("code_analysis_findings", &"*** Sensitive Data Redacted ***");
        formatter.field("_request_id", &self._request_id);
        formatter.finish()
    }
}
