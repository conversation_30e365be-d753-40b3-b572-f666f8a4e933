// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct GetUsageLimitsInput {
    /// The ARN of the Q Developer profile. Required for enterprise customers, optional for Builder
    /// ID users.
    pub profile_arn: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub resource_type: ::std::option::Option<crate::types::ResourceType>,
}
impl GetUsageLimitsInput {
    /// The ARN of the Q Developer profile. Required for enterprise customers, optional for Builder
    /// ID users.
    pub fn profile_arn(&self) -> ::std::option::Option<&str> {
        self.profile_arn.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn resource_type(&self) -> ::std::option::Option<&crate::types::ResourceType> {
        self.resource_type.as_ref()
    }
}
impl GetUsageLimitsInput {
    /// Creates a new builder-style object to manufacture
    /// [`GetUsageLimitsInput`](crate::operation::get_usage_limits::GetUsageLimitsInput).
    pub fn builder() -> crate::operation::get_usage_limits::builders::GetUsageLimitsInputBuilder {
        crate::operation::get_usage_limits::builders::GetUsageLimitsInputBuilder::default()
    }
}

/// A builder for [`GetUsageLimitsInput`](crate::operation::get_usage_limits::GetUsageLimitsInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct GetUsageLimitsInputBuilder {
    pub(crate) profile_arn: ::std::option::Option<::std::string::String>,
    pub(crate) resource_type: ::std::option::Option<crate::types::ResourceType>,
}
impl GetUsageLimitsInputBuilder {
    /// The ARN of the Q Developer profile. Required for enterprise customers, optional for Builder
    /// ID users.
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.profile_arn = ::std::option::Option::Some(input.into());
        self
    }

    /// The ARN of the Q Developer profile. Required for enterprise customers, optional for Builder
    /// ID users.
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.profile_arn = input;
        self
    }

    /// The ARN of the Q Developer profile. Required for enterprise customers, optional for Builder
    /// ID users.
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.profile_arn
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn resource_type(mut self, input: crate::types::ResourceType) -> Self {
        self.resource_type = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_resource_type(mut self, input: ::std::option::Option<crate::types::ResourceType>) -> Self {
        self.resource_type = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_resource_type(&self) -> &::std::option::Option<crate::types::ResourceType> {
        &self.resource_type
    }

    /// Consumes the builder and constructs a
    /// [`GetUsageLimitsInput`](crate::operation::get_usage_limits::GetUsageLimitsInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::get_usage_limits::GetUsageLimitsInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::get_usage_limits::GetUsageLimitsInput {
            profile_arn: self.profile_arn,
            resource_type: self.resource_type,
        })
    }
}
