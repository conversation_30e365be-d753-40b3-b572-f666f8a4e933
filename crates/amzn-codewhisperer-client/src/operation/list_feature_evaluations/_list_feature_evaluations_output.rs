// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ListFeatureEvaluationsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub feature_evaluations: ::std::vec::Vec<crate::types::FeatureEvaluation>,
    _request_id: Option<String>,
}
impl ListFeatureEvaluationsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn feature_evaluations(&self) -> &[crate::types::FeatureEvaluation] {
        use std::ops::Deref;
        self.feature_evaluations.deref()
    }
}
impl ::aws_types::request_id::RequestId for ListFeatureEvaluationsOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl ListFeatureEvaluationsOutput {
    /// Creates a new builder-style object to manufacture
    /// [`ListFeatureEvaluationsOutput`](crate::operation::list_feature_evaluations::ListFeatureEvaluationsOutput).
    pub fn builder() -> crate::operation::list_feature_evaluations::builders::ListFeatureEvaluationsOutputBuilder {
        crate::operation::list_feature_evaluations::builders::ListFeatureEvaluationsOutputBuilder::default()
    }
}

/// A builder for
/// [`ListFeatureEvaluationsOutput`](crate::operation::list_feature_evaluations::ListFeatureEvaluationsOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ListFeatureEvaluationsOutputBuilder {
    pub(crate) feature_evaluations: ::std::option::Option<::std::vec::Vec<crate::types::FeatureEvaluation>>,
    _request_id: Option<String>,
}
impl ListFeatureEvaluationsOutputBuilder {
    /// Appends an item to `feature_evaluations`.
    ///
    /// To override the contents of this collection use
    /// [`set_feature_evaluations`](Self::set_feature_evaluations).
    pub fn feature_evaluations(mut self, input: crate::types::FeatureEvaluation) -> Self {
        let mut v = self.feature_evaluations.unwrap_or_default();
        v.push(input);
        self.feature_evaluations = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_feature_evaluations(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::FeatureEvaluation>>,
    ) -> Self {
        self.feature_evaluations = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_feature_evaluations(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::FeatureEvaluation>> {
        &self.feature_evaluations
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ListFeatureEvaluationsOutput`](crate::operation::list_feature_evaluations::ListFeatureEvaluationsOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`feature_evaluations`](crate::operation::list_feature_evaluations::builders::ListFeatureEvaluationsOutputBuilder::feature_evaluations)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_feature_evaluations::ListFeatureEvaluationsOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::list_feature_evaluations::ListFeatureEvaluationsOutput {
            feature_evaluations: self.feature_evaluations.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "feature_evaluations",
                    "feature_evaluations was not specified but it is required when building ListFeatureEvaluationsOutput",
                )
            })?,
            _request_id: self._request_id,
        })
    }
}
