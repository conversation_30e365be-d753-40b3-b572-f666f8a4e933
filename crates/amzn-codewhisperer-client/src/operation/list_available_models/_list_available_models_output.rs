// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ListAvailableModelsOutput {
    /// List of available models
    pub models: ::std::vec::Vec<crate::types::Model>,
    /// Default model set by the client
    pub default_model: ::std::option::Option<crate::types::Model>,
    /// Token for retrieving the next page of results
    pub next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListAvailableModelsOutput {
    /// List of available models
    pub fn models(&self) -> &[crate::types::Model] {
        use std::ops::Deref;
        self.models.deref()
    }

    /// Default model set by the client
    pub fn default_model(&self) -> ::std::option::Option<&crate::types::Model> {
        self.default_model.as_ref()
    }

    /// Token for retrieving the next page of results
    pub fn next_token(&self) -> ::std::option::Option<&str> {
        self.next_token.as_deref()
    }
}
impl ::aws_types::request_id::RequestId for ListAvailableModelsOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl ListAvailableModelsOutput {
    /// Creates a new builder-style object to manufacture
    /// [`ListAvailableModelsOutput`](crate::operation::list_available_models::ListAvailableModelsOutput).
    pub fn builder() -> crate::operation::list_available_models::builders::ListAvailableModelsOutputBuilder {
        crate::operation::list_available_models::builders::ListAvailableModelsOutputBuilder::default()
    }
}

/// A builder for
/// [`ListAvailableModelsOutput`](crate::operation::list_available_models::ListAvailableModelsOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ListAvailableModelsOutputBuilder {
    pub(crate) models: ::std::option::Option<::std::vec::Vec<crate::types::Model>>,
    pub(crate) default_model: ::std::option::Option<crate::types::Model>,
    pub(crate) next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListAvailableModelsOutputBuilder {
    /// Appends an item to `models`.
    ///
    /// To override the contents of this collection use [`set_models`](Self::set_models).
    ///
    /// List of available models
    pub fn models(mut self, input: crate::types::Model) -> Self {
        let mut v = self.models.unwrap_or_default();
        v.push(input);
        self.models = ::std::option::Option::Some(v);
        self
    }

    /// List of available models
    pub fn set_models(mut self, input: ::std::option::Option<::std::vec::Vec<crate::types::Model>>) -> Self {
        self.models = input;
        self
    }

    /// List of available models
    pub fn get_models(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::Model>> {
        &self.models
    }

    /// Default model set by the client
    pub fn default_model(mut self, input: crate::types::Model) -> Self {
        self.default_model = ::std::option::Option::Some(input);
        self
    }

    /// Default model set by the client
    pub fn set_default_model(mut self, input: ::std::option::Option<crate::types::Model>) -> Self {
        self.default_model = input;
        self
    }

    /// Default model set by the client
    pub fn get_default_model(&self) -> &::std::option::Option<crate::types::Model> {
        &self.default_model
    }

    /// Token for retrieving the next page of results
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.next_token = ::std::option::Option::Some(input.into());
        self
    }

    /// Token for retrieving the next page of results
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.next_token = input;
        self
    }

    /// Token for retrieving the next page of results
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.next_token
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ListAvailableModelsOutput`](crate::operation::list_available_models::ListAvailableModelsOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`models`](crate::operation::list_available_models::builders::ListAvailableModelsOutputBuilder::models)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_available_models::ListAvailableModelsOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::list_available_models::ListAvailableModelsOutput {
            models: self.models.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "models",
                    "models was not specified but it is required when building ListAvailableModelsOutput",
                )
            })?,
            default_model: self.default_model,
            next_token: self.next_token,
            _request_id: self._request_id,
        })
    }
}
