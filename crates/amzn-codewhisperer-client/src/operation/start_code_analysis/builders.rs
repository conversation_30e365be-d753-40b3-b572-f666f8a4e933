// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::start_code_analysis::_start_code_analysis_input::StartCodeAnalysisInputBuilder;
pub use crate::operation::start_code_analysis::_start_code_analysis_output::StartCodeAnalysisOutputBuilder;

impl crate::operation::start_code_analysis::builders::StartCodeAnalysisInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::start_code_analysis::StartCodeAnalysisOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_code_analysis::StartCodeAnalysisError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.start_code_analysis();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `StartCodeAnalysis`.
///
/// Starts a code analysis job
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct StartCodeAnalysisFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::start_code_analysis::builders::StartCodeAnalysisInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::start_code_analysis::StartCodeAnalysisOutput,
        crate::operation::start_code_analysis::StartCodeAnalysisError,
    > for StartCodeAnalysisFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::start_code_analysis::StartCodeAnalysisOutput,
            crate::operation::start_code_analysis::StartCodeAnalysisError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl StartCodeAnalysisFluentBuilder {
    /// Creates a new `StartCodeAnalysisFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the StartCodeAnalysis as a reference.
    pub fn as_input(&self) -> &crate::operation::start_code_analysis::builders::StartCodeAnalysisInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::start_code_analysis::StartCodeAnalysisOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_code_analysis::StartCodeAnalysisError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins = crate::operation::start_code_analysis::StartCodeAnalysis::operation_runtime_plugins(
            self.handle.runtime_plugins.clone(),
            &self.handle.conf,
            self.config_override,
        );
        crate::operation::start_code_analysis::StartCodeAnalysis::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::start_code_analysis::StartCodeAnalysisOutput,
        crate::operation::start_code_analysis::StartCodeAnalysisError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    /// Adds a key-value pair to `artifacts`.
    ///
    /// To override the contents of this collection use [`set_artifacts`](Self::set_artifacts).
    #[allow(missing_docs)] // documentation missing in model
    pub fn artifacts(
        mut self,
        k: crate::types::ArtifactType,
        v: impl ::std::convert::Into<::std::string::String>,
    ) -> Self {
        self.inner = self.inner.artifacts(k, v.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_artifacts(
        mut self,
        input: ::std::option::Option<::std::collections::HashMap<crate::types::ArtifactType, ::std::string::String>>,
    ) -> Self {
        self.inner = self.inner.set_artifacts(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_artifacts(
        &self,
    ) -> &::std::option::Option<::std::collections::HashMap<crate::types::ArtifactType, ::std::string::String>> {
        self.inner.get_artifacts()
    }

    /// Programming Languages supported by CodeWhisperer
    pub fn programming_language(mut self, input: crate::types::ProgrammingLanguage) -> Self {
        self.inner = self.inner.programming_language(input);
        self
    }

    /// Programming Languages supported by CodeWhisperer
    pub fn set_programming_language(mut self, input: ::std::option::Option<crate::types::ProgrammingLanguage>) -> Self {
        self.inner = self.inner.set_programming_language(input);
        self
    }

    /// Programming Languages supported by CodeWhisperer
    pub fn get_programming_language(&self) -> &::std::option::Option<crate::types::ProgrammingLanguage> {
        self.inner.get_programming_language()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.client_token(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_client_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_client_token(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_client_token(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_client_token()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn scope(mut self, input: crate::types::CodeAnalysisScope) -> Self {
        self.inner = self.inner.scope(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_scope(mut self, input: ::std::option::Option<crate::types::CodeAnalysisScope>) -> Self {
        self.inner = self.inner.set_scope(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_scope(&self) -> &::std::option::Option<crate::types::CodeAnalysisScope> {
        self.inner.get_scope()
    }

    /// Code analysis scan name
    pub fn code_scan_name(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.code_scan_name(input.into());
        self
    }

    /// Code analysis scan name
    pub fn set_code_scan_name(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_code_scan_name(input);
        self
    }

    /// Code analysis scan name
    pub fn get_code_scan_name(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_code_scan_name()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn code_diff_metadata(mut self, input: crate::types::CodeDiffMetadata) -> Self {
        self.inner = self.inner.code_diff_metadata(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_code_diff_metadata(mut self, input: ::std::option::Option<crate::types::CodeDiffMetadata>) -> Self {
        self.inner = self.inner.set_code_diff_metadata(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_code_diff_metadata(&self) -> &::std::option::Option<crate::types::CodeDiffMetadata> {
        self.inner.get_code_diff_metadata()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }
}
