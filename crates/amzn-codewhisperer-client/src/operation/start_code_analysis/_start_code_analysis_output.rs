// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct StartCodeAnalysisOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub job_id: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub status: crate::types::CodeAnalysisStatus,
    #[allow(missing_docs)] // documentation missing in model
    pub error_message: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl StartCodeAnalysisOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn job_id(&self) -> &str {
        use std::ops::Deref;
        self.job_id.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn status(&self) -> &crate::types::CodeAnalysisStatus {
        &self.status
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn error_message(&self) -> ::std::option::Option<&str> {
        self.error_message.as_deref()
    }
}
impl ::std::fmt::Debug for StartCodeAnalysisOutput {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("StartCodeAnalysisOutput");
        formatter.field("job_id", &self.job_id);
        formatter.field("status", &self.status);
        formatter.field("error_message", &"*** Sensitive Data Redacted ***");
        formatter.field("_request_id", &self._request_id);
        formatter.finish()
    }
}
impl ::aws_types::request_id::RequestId for StartCodeAnalysisOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl StartCodeAnalysisOutput {
    /// Creates a new builder-style object to manufacture
    /// [`StartCodeAnalysisOutput`](crate::operation::start_code_analysis::StartCodeAnalysisOutput).
    pub fn builder() -> crate::operation::start_code_analysis::builders::StartCodeAnalysisOutputBuilder {
        crate::operation::start_code_analysis::builders::StartCodeAnalysisOutputBuilder::default()
    }
}

/// A builder for
/// [`StartCodeAnalysisOutput`](crate::operation::start_code_analysis::StartCodeAnalysisOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct StartCodeAnalysisOutputBuilder {
    pub(crate) job_id: ::std::option::Option<::std::string::String>,
    pub(crate) status: ::std::option::Option<crate::types::CodeAnalysisStatus>,
    pub(crate) error_message: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl StartCodeAnalysisOutputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn job_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.job_id = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_job_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.job_id = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_job_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.job_id
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn status(mut self, input: crate::types::CodeAnalysisStatus) -> Self {
        self.status = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_status(mut self, input: ::std::option::Option<crate::types::CodeAnalysisStatus>) -> Self {
        self.status = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_status(&self) -> &::std::option::Option<crate::types::CodeAnalysisStatus> {
        &self.status
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn error_message(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.error_message = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_error_message(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.error_message = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_error_message(&self) -> &::std::option::Option<::std::string::String> {
        &self.error_message
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`StartCodeAnalysisOutput`](crate::operation::start_code_analysis::StartCodeAnalysisOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`job_id`](crate::operation::start_code_analysis::builders::StartCodeAnalysisOutputBuilder::job_id)
    /// - [`status`](crate::operation::start_code_analysis::builders::StartCodeAnalysisOutputBuilder::status)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::start_code_analysis::StartCodeAnalysisOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::start_code_analysis::StartCodeAnalysisOutput {
            job_id: self.job_id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "job_id",
                    "job_id was not specified but it is required when building StartCodeAnalysisOutput",
                )
            })?,
            status: self.status.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "status",
                    "status was not specified but it is required when building StartCodeAnalysisOutput",
                )
            })?,
            error_message: self.error_message,
            _request_id: self._request_id,
        })
    }
}
impl ::std::fmt::Debug for StartCodeAnalysisOutputBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("StartCodeAnalysisOutputBuilder");
        formatter.field("job_id", &self.job_id);
        formatter.field("status", &self.status);
        formatter.field("error_message", &"*** Sensitive Data Redacted ***");
        formatter.field("_request_id", &self._request_id);
        formatter.finish()
    }
}
