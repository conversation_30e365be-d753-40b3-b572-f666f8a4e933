// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub use crate::operation::start_code_fix_job::_start_code_fix_job_input::StartCodeFixJobInputBuilder;
pub use crate::operation::start_code_fix_job::_start_code_fix_job_output::StartCodeFixJobOutputBuilder;

impl crate::operation::start_code_fix_job::builders::StartCodeFixJobInputBuilder {
    /// Sends a request with this input using the given client.
    pub async fn send_with(
        self,
        client: &crate::Client,
    ) -> ::std::result::Result<
        crate::operation::start_code_fix_job::StartCodeFixJobOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_code_fix_job::StartCodeFixJobError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let mut fluent_builder = client.start_code_fix_job();
        fluent_builder.inner = self;
        fluent_builder.send().await
    }
}
/// Fluent builder constructing a request to `StartCodeFixJob`.
#[derive(::std::clone::Clone, ::std::fmt::Debug)]
pub struct StartCodeFixJobFluentBuilder {
    handle: ::std::sync::Arc<crate::client::Handle>,
    inner: crate::operation::start_code_fix_job::builders::StartCodeFixJobInputBuilder,
    config_override: ::std::option::Option<crate::config::Builder>,
}
impl
    crate::client::customize::internal::CustomizableSend<
        crate::operation::start_code_fix_job::StartCodeFixJobOutput,
        crate::operation::start_code_fix_job::StartCodeFixJobError,
    > for StartCodeFixJobFluentBuilder
{
    fn send(
        self,
        config_override: crate::config::Builder,
    ) -> crate::client::customize::internal::BoxFuture<
        crate::client::customize::internal::SendResult<
            crate::operation::start_code_fix_job::StartCodeFixJobOutput,
            crate::operation::start_code_fix_job::StartCodeFixJobError,
        >,
    > {
        ::std::boxed::Box::pin(async move { self.config_override(config_override).send().await })
    }
}
impl StartCodeFixJobFluentBuilder {
    /// Creates a new `StartCodeFixJobFluentBuilder`.
    pub(crate) fn new(handle: ::std::sync::Arc<crate::client::Handle>) -> Self {
        Self {
            handle,
            inner: ::std::default::Default::default(),
            config_override: ::std::option::Option::None,
        }
    }

    /// Access the StartCodeFixJob as a reference.
    pub fn as_input(&self) -> &crate::operation::start_code_fix_job::builders::StartCodeFixJobInputBuilder {
        &self.inner
    }

    /// Sends the request and returns the response.
    ///
    /// If an error occurs, an `SdkError` will be returned with additional details that
    /// can be matched against.
    ///
    /// By default, any retryable failures will be retried twice. Retry behavior
    /// is configurable with the [RetryConfig](aws_smithy_types::retry::RetryConfig), which can be
    /// set when configuring the client.
    pub async fn send(
        self,
    ) -> ::std::result::Result<
        crate::operation::start_code_fix_job::StartCodeFixJobOutput,
        ::aws_smithy_runtime_api::client::result::SdkError<
            crate::operation::start_code_fix_job::StartCodeFixJobError,
            ::aws_smithy_runtime_api::client::orchestrator::HttpResponse,
        >,
    > {
        let input = self
            .inner
            .build()
            .map_err(::aws_smithy_runtime_api::client::result::SdkError::construction_failure)?;
        let runtime_plugins = crate::operation::start_code_fix_job::StartCodeFixJob::operation_runtime_plugins(
            self.handle.runtime_plugins.clone(),
            &self.handle.conf,
            self.config_override,
        );
        crate::operation::start_code_fix_job::StartCodeFixJob::orchestrate(&runtime_plugins, input).await
    }

    /// Consumes this builder, creating a customizable operation that can be modified before being
    /// sent.
    pub fn customize(
        self,
    ) -> crate::client::customize::CustomizableOperation<
        crate::operation::start_code_fix_job::StartCodeFixJobOutput,
        crate::operation::start_code_fix_job::StartCodeFixJobError,
        Self,
    > {
        crate::client::customize::CustomizableOperation::new(self)
    }

    pub(crate) fn config_override(
        mut self,
        config_override: impl ::std::convert::Into<crate::config::Builder>,
    ) -> Self {
        self.set_config_override(::std::option::Option::Some(config_override.into()));
        self
    }

    pub(crate) fn set_config_override(
        &mut self,
        config_override: ::std::option::Option<crate::config::Builder>,
    ) -> &mut Self {
        self.config_override = config_override;
        self
    }

    /// Indicates Range / Span in a Text Document
    pub fn snippet_range(mut self, input: crate::types::Range) -> Self {
        self.inner = self.inner.snippet_range(input);
        self
    }

    /// Indicates Range / Span in a Text Document
    pub fn set_snippet_range(mut self, input: ::std::option::Option<crate::types::Range>) -> Self {
        self.inner = self.inner.set_snippet_range(input);
        self
    }

    /// Indicates Range / Span in a Text Document
    pub fn get_snippet_range(&self) -> &::std::option::Option<crate::types::Range> {
        self.inner.get_snippet_range()
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn upload_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.upload_id(input.into());
        self
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn set_upload_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_upload_id(input);
        self
    }

    /// Upload ID returned by CreateUploadUrl API
    pub fn get_upload_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_upload_id()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn description(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.description(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_description(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_description(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_description(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_description()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn rule_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.rule_id(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_rule_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_rule_id(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_rule_id(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_rule_id()
    }

    /// Code fix name
    pub fn code_fix_name(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.code_fix_name(input.into());
        self
    }

    /// Code fix name
    pub fn set_code_fix_name(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_code_fix_name(input);
        self
    }

    /// Code fix name
    pub fn get_code_fix_name(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_code_fix_name()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn reference_tracker_configuration(mut self, input: crate::types::ReferenceTrackerConfiguration) -> Self {
        self.inner = self.inner.reference_tracker_configuration(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_reference_tracker_configuration(
        mut self,
        input: ::std::option::Option<crate::types::ReferenceTrackerConfiguration>,
    ) -> Self {
        self.inner = self.inner.set_reference_tracker_configuration(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_reference_tracker_configuration(
        &self,
    ) -> &::std::option::Option<crate::types::ReferenceTrackerConfiguration> {
        self.inner.get_reference_tracker_configuration()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.inner = self.inner.profile_arn(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.inner = self.inner.set_profile_arn(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        self.inner.get_profile_arn()
    }
}
