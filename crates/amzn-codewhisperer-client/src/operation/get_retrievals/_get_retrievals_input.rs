// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct GetRetrievalsInput {
    #[allow(missing_docs)] // documentation missing in model
    pub customization_arn: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub query: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub language_name: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub max_results: ::std::option::Option<i32>,
}
impl GetRetrievalsInput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn customization_arn(&self) -> ::std::option::Option<&str> {
        self.customization_arn.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn query(&self) -> ::std::option::Option<&str> {
        self.query.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn language_name(&self) -> ::std::option::Option<&str> {
        self.language_name.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn max_results(&self) -> ::std::option::Option<i32> {
        self.max_results
    }
}
impl GetRetrievalsInput {
    /// Creates a new builder-style object to manufacture
    /// [`GetRetrievalsInput`](crate::operation::get_retrievals::GetRetrievalsInput).
    pub fn builder() -> crate::operation::get_retrievals::builders::GetRetrievalsInputBuilder {
        crate::operation::get_retrievals::builders::GetRetrievalsInputBuilder::default()
    }
}

/// A builder for [`GetRetrievalsInput`](crate::operation::get_retrievals::GetRetrievalsInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct GetRetrievalsInputBuilder {
    pub(crate) customization_arn: ::std::option::Option<::std::string::String>,
    pub(crate) query: ::std::option::Option<::std::string::String>,
    pub(crate) language_name: ::std::option::Option<::std::string::String>,
    pub(crate) max_results: ::std::option::Option<i32>,
}
impl GetRetrievalsInputBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn customization_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.customization_arn = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_customization_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.customization_arn = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_customization_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.customization_arn
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn query(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.query = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_query(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.query = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_query(&self) -> &::std::option::Option<::std::string::String> {
        &self.query
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn language_name(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.language_name = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_language_name(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.language_name = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_language_name(&self) -> &::std::option::Option<::std::string::String> {
        &self.language_name
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn max_results(mut self, input: i32) -> Self {
        self.max_results = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_max_results(mut self, input: ::std::option::Option<i32>) -> Self {
        self.max_results = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_max_results(&self) -> &::std::option::Option<i32> {
        &self.max_results
    }

    /// Consumes the builder and constructs a
    /// [`GetRetrievalsInput`](crate::operation::get_retrievals::GetRetrievalsInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::get_retrievals::GetRetrievalsInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::get_retrievals::GetRetrievalsInput {
            customization_arn: self.customization_arn,
            query: self.query,
            language_name: self.language_name,
            max_results: self.max_results,
        })
    }
}
