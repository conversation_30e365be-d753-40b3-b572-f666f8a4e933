// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct GetRetrievalsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub retrieval_map: ::std::collections::HashMap<::std::string::String, ::std::vec::Vec<crate::types::Retrieval>>,
    _request_id: Option<String>,
}
impl GetRetrievalsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn retrieval_map(
        &self,
    ) -> &::std::collections::HashMap<::std::string::String, ::std::vec::Vec<crate::types::Retrieval>> {
        &self.retrieval_map
    }
}
impl ::aws_types::request_id::RequestId for GetRetrievalsOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl GetRetrievalsOutput {
    /// Creates a new builder-style object to manufacture
    /// [`GetRetrievalsOutput`](crate::operation::get_retrievals::GetRetrievalsOutput).
    pub fn builder() -> crate::operation::get_retrievals::builders::GetRetrievalsOutputBuilder {
        crate::operation::get_retrievals::builders::GetRetrievalsOutputBuilder::default()
    }
}

/// A builder for [`GetRetrievalsOutput`](crate::operation::get_retrievals::GetRetrievalsOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct GetRetrievalsOutputBuilder {
    pub(crate) retrieval_map: ::std::option::Option<
        ::std::collections::HashMap<::std::string::String, ::std::vec::Vec<crate::types::Retrieval>>,
    >,
    _request_id: Option<String>,
}
impl GetRetrievalsOutputBuilder {
    /// Adds a key-value pair to `retrieval_map`.
    ///
    /// To override the contents of this collection use
    /// [`set_retrieval_map`](Self::set_retrieval_map).
    pub fn retrieval_map(
        mut self,
        k: impl ::std::convert::Into<::std::string::String>,
        v: ::std::vec::Vec<crate::types::Retrieval>,
    ) -> Self {
        let mut hash_map = self.retrieval_map.unwrap_or_default();
        hash_map.insert(k.into(), v);
        self.retrieval_map = ::std::option::Option::Some(hash_map);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_retrieval_map(
        mut self,
        input: ::std::option::Option<
            ::std::collections::HashMap<::std::string::String, ::std::vec::Vec<crate::types::Retrieval>>,
        >,
    ) -> Self {
        self.retrieval_map = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_retrieval_map(
        &self,
    ) -> &::std::option::Option<
        ::std::collections::HashMap<::std::string::String, ::std::vec::Vec<crate::types::Retrieval>>,
    > {
        &self.retrieval_map
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`GetRetrievalsOutput`](crate::operation::get_retrievals::GetRetrievalsOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`retrieval_map`](crate::operation::get_retrievals::builders::GetRetrievalsOutputBuilder::retrieval_map)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::get_retrievals::GetRetrievalsOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::get_retrievals::GetRetrievalsOutput {
            retrieval_map: self.retrieval_map.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "retrieval_map",
                    "retrieval_map was not specified but it is required when building GetRetrievalsOutput",
                )
            })?,
            _request_id: self._request_id,
        })
    }
}
