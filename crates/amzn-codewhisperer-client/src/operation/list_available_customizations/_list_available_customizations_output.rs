// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ListAvailableCustomizationsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub customizations: ::std::vec::Vec<crate::types::Customization>,
    #[allow(missing_docs)] // documentation missing in model
    pub next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListAvailableCustomizationsOutput {
    #[allow(missing_docs)] // documentation missing in model
    pub fn customizations(&self) -> &[crate::types::Customization] {
        use std::ops::Deref;
        self.customizations.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(&self) -> ::std::option::Option<&str> {
        self.next_token.as_deref()
    }
}
impl ::aws_types::request_id::RequestId for ListAvailableCustomizationsOutput {
    fn request_id(&self) -> Option<&str> {
        self._request_id.as_deref()
    }
}
impl ListAvailableCustomizationsOutput {
    /// Creates a new builder-style object to manufacture
    /// [`ListAvailableCustomizationsOutput`](crate::operation::list_available_customizations::ListAvailableCustomizationsOutput).
    pub fn builder()
    -> crate::operation::list_available_customizations::builders::ListAvailableCustomizationsOutputBuilder {
        crate::operation::list_available_customizations::builders::ListAvailableCustomizationsOutputBuilder::default()
    }
}

/// A builder for
/// [`ListAvailableCustomizationsOutput`](crate::operation::list_available_customizations::ListAvailableCustomizationsOutput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ListAvailableCustomizationsOutputBuilder {
    pub(crate) customizations: ::std::option::Option<::std::vec::Vec<crate::types::Customization>>,
    pub(crate) next_token: ::std::option::Option<::std::string::String>,
    _request_id: Option<String>,
}
impl ListAvailableCustomizationsOutputBuilder {
    /// Appends an item to `customizations`.
    ///
    /// To override the contents of this collection use
    /// [`set_customizations`](Self::set_customizations).
    pub fn customizations(mut self, input: crate::types::Customization) -> Self {
        let mut v = self.customizations.unwrap_or_default();
        v.push(input);
        self.customizations = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_customizations(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::Customization>>,
    ) -> Self {
        self.customizations = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_customizations(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::Customization>> {
        &self.customizations
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn next_token(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.next_token = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_next_token(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.next_token = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_next_token(&self) -> &::std::option::Option<::std::string::String> {
        &self.next_token
    }

    pub(crate) fn _request_id(mut self, request_id: impl Into<String>) -> Self {
        self._request_id = Some(request_id.into());
        self
    }

    pub(crate) fn _set_request_id(&mut self, request_id: Option<String>) -> &mut Self {
        self._request_id = request_id;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ListAvailableCustomizationsOutput`](crate::operation::list_available_customizations::ListAvailableCustomizationsOutput).
    /// This method will fail if any of the following fields are not set:
    /// - [`customizations`](crate::operation::list_available_customizations::builders::ListAvailableCustomizationsOutputBuilder::customizations)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::list_available_customizations::ListAvailableCustomizationsOutput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::operation::list_available_customizations::ListAvailableCustomizationsOutput {
            customizations: self.customizations.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "customizations",
                    "customizations was not specified but it is required when building ListAvailableCustomizationsOutput",
                )
            })?,
            next_token: self.next_token,
            _request_id: self._request_id,
        })
    }
}
