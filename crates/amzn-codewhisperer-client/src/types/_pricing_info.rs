// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct PricingInfo {
    #[allow(missing_docs)] // documentation missing in model
    pub amount: f64,
    #[allow(missing_docs)] // documentation missing in model
    pub currency: crate::types::Currency,
}
impl PricingInfo {
    #[allow(missing_docs)] // documentation missing in model
    pub fn amount(&self) -> f64 {
        self.amount
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn currency(&self) -> &crate::types::Currency {
        &self.currency
    }
}
impl PricingInfo {
    /// Creates a new builder-style object to manufacture
    /// [`PricingInfo`](crate::types::PricingInfo).
    pub fn builder() -> crate::types::builders::PricingInfoBuilder {
        crate::types::builders::PricingInfoBuilder::default()
    }
}

/// A builder for [`PricingInfo`](crate::types::PricingInfo).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct PricingInfoBuilder {
    pub(crate) amount: ::std::option::Option<f64>,
    pub(crate) currency: ::std::option::Option<crate::types::Currency>,
}
impl PricingInfoBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn amount(mut self, input: f64) -> Self {
        self.amount = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_amount(mut self, input: ::std::option::Option<f64>) -> Self {
        self.amount = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_amount(&self) -> &::std::option::Option<f64> {
        &self.amount
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn currency(mut self, input: crate::types::Currency) -> Self {
        self.currency = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_currency(mut self, input: ::std::option::Option<crate::types::Currency>) -> Self {
        self.currency = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_currency(&self) -> &::std::option::Option<crate::types::Currency> {
        &self.currency
    }

    /// Consumes the builder and constructs a [`PricingInfo`](crate::types::PricingInfo).
    /// This method will fail if any of the following fields are not set:
    /// - [`amount`](crate::types::builders::PricingInfoBuilder::amount)
    /// - [`currency`](crate::types::builders::PricingInfoBuilder::currency)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::PricingInfo, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::PricingInfo {
            amount: self.amount.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "amount",
                    "amount was not specified but it is required when building PricingInfo",
                )
            })?,
            currency: self.currency.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "currency",
                    "currency was not specified but it is required when building PricingInfo",
                )
            })?,
        })
    }
}
