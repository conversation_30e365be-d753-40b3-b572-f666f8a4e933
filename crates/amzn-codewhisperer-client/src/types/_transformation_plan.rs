// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct TransformationPlan {
    #[allow(missing_docs)] // documentation missing in model
    pub transformation_steps: ::std::vec::Vec<crate::types::TransformationStep>,
}
impl TransformationPlan {
    #[allow(missing_docs)] // documentation missing in model
    pub fn transformation_steps(&self) -> &[crate::types::TransformationStep] {
        use std::ops::Deref;
        self.transformation_steps.deref()
    }
}
impl TransformationPlan {
    /// Creates a new builder-style object to manufacture
    /// [`TransformationPlan`](crate::types::TransformationPlan).
    pub fn builder() -> crate::types::builders::TransformationPlanBuilder {
        crate::types::builders::TransformationPlanBuilder::default()
    }
}

/// A builder for [`TransformationPlan`](crate::types::TransformationPlan).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct TransformationPlanBuilder {
    pub(crate) transformation_steps: ::std::option::Option<::std::vec::Vec<crate::types::TransformationStep>>,
}
impl TransformationPlanBuilder {
    /// Appends an item to `transformation_steps`.
    ///
    /// To override the contents of this collection use
    /// [`set_transformation_steps`](Self::set_transformation_steps).
    pub fn transformation_steps(mut self, input: crate::types::TransformationStep) -> Self {
        let mut v = self.transformation_steps.unwrap_or_default();
        v.push(input);
        self.transformation_steps = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_transformation_steps(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::TransformationStep>>,
    ) -> Self {
        self.transformation_steps = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_transformation_steps(
        &self,
    ) -> &::std::option::Option<::std::vec::Vec<crate::types::TransformationStep>> {
        &self.transformation_steps
    }

    /// Consumes the builder and constructs a
    /// [`TransformationPlan`](crate::types::TransformationPlan). This method will fail if any
    /// of the following fields are not set:
    /// - [`transformation_steps`](crate::types::builders::TransformationPlanBuilder::transformation_steps)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::TransformationPlan, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::TransformationPlan {
            transformation_steps: self.transformation_steps.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "transformation_steps",
                    "transformation_steps was not specified but it is required when building TransformationPlan",
                )
            })?,
        })
    }
}
