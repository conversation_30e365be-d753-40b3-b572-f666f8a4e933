// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// The specification for the tool.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct ToolSpecification {
    /// The input schema for the tool in JSON format.
    pub input_schema: crate::types::ToolInputSchema,
    /// The name for the tool.
    pub name: ::std::string::String,
    /// The description for the tool.
    pub description: ::std::option::Option<::std::string::String>,
}
impl ToolSpecification {
    /// The input schema for the tool in JSON format.
    pub fn input_schema(&self) -> &crate::types::ToolInputSchema {
        &self.input_schema
    }

    /// The name for the tool.
    pub fn name(&self) -> &str {
        use std::ops::Deref;
        self.name.deref()
    }

    /// The description for the tool.
    pub fn description(&self) -> ::std::option::Option<&str> {
        self.description.as_deref()
    }
}
impl ::std::fmt::Debug for ToolSpecification {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("ToolSpecification");
        formatter.field("input_schema", &self.input_schema);
        formatter.field("name", &"*** Sensitive Data Redacted ***");
        formatter.field("description", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
impl ToolSpecification {
    /// Creates a new builder-style object to manufacture
    /// [`ToolSpecification`](crate::types::ToolSpecification).
    pub fn builder() -> crate::types::builders::ToolSpecificationBuilder {
        crate::types::builders::ToolSpecificationBuilder::default()
    }
}

/// A builder for [`ToolSpecification`](crate::types::ToolSpecification).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct ToolSpecificationBuilder {
    pub(crate) input_schema: ::std::option::Option<crate::types::ToolInputSchema>,
    pub(crate) name: ::std::option::Option<::std::string::String>,
    pub(crate) description: ::std::option::Option<::std::string::String>,
}
impl ToolSpecificationBuilder {
    /// The input schema for the tool in JSON format.
    /// This field is required.
    pub fn input_schema(mut self, input: crate::types::ToolInputSchema) -> Self {
        self.input_schema = ::std::option::Option::Some(input);
        self
    }

    /// The input schema for the tool in JSON format.
    pub fn set_input_schema(mut self, input: ::std::option::Option<crate::types::ToolInputSchema>) -> Self {
        self.input_schema = input;
        self
    }

    /// The input schema for the tool in JSON format.
    pub fn get_input_schema(&self) -> &::std::option::Option<crate::types::ToolInputSchema> {
        &self.input_schema
    }

    /// The name for the tool.
    /// This field is required.
    pub fn name(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.name = ::std::option::Option::Some(input.into());
        self
    }

    /// The name for the tool.
    pub fn set_name(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.name = input;
        self
    }

    /// The name for the tool.
    pub fn get_name(&self) -> &::std::option::Option<::std::string::String> {
        &self.name
    }

    /// The description for the tool.
    pub fn description(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.description = ::std::option::Option::Some(input.into());
        self
    }

    /// The description for the tool.
    pub fn set_description(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.description = input;
        self
    }

    /// The description for the tool.
    pub fn get_description(&self) -> &::std::option::Option<::std::string::String> {
        &self.description
    }

    /// Consumes the builder and constructs a
    /// [`ToolSpecification`](crate::types::ToolSpecification). This method will fail if any of
    /// the following fields are not set:
    /// - [`input_schema`](crate::types::builders::ToolSpecificationBuilder::input_schema)
    /// - [`name`](crate::types::builders::ToolSpecificationBuilder::name)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::ToolSpecification, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::ToolSpecification {
            input_schema: self.input_schema.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "input_schema",
                    "input_schema was not specified but it is required when building ToolSpecification",
                )
            })?,
            name: self.name.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "name",
                    "name was not specified but it is required when building ToolSpecification",
                )
            })?,
            description: self.description,
        })
    }
}
impl ::std::fmt::Debug for ToolSpecificationBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("ToolSpecificationBuilder");
        formatter.field("input_schema", &self.input_schema);
        formatter.field("name", &"*** Sensitive Data Redacted ***");
        formatter.field("description", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
