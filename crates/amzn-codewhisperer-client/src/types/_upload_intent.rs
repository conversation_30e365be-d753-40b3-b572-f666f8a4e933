// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// When writing a match expression against `UploadIntent`, it is important to ensure
/// your code is forward-compatible. That is, if a match arm handles a case for a
/// feature that is supported by the service but has not been represented as an enum
/// variant in a current version of SDK, your code should continue to work when you
/// upgrade SDK to a future version in which the enum does include a variant for that
/// feature.
///
/// Here is an example of how you can make a match expression forward-compatible:
///
/// ```text
/// # let uploadintent = unimplemented!();
/// match uploadintent {
///     UploadIntent::AgenticCodeReview => { /* ... */ },
///     UploadIntent::AutomaticFileSecurityScan => { /* ... */ },
///     UploadIntent::CodeFixGeneration => { /* ... */ },
///     UploadIntent::FullProjectSecurityScan => { /* ... */ },
///     UploadIntent::TaskAssistPlanning => { /* ... */ },
///     UploadIntent::Transformation => { /* ... */ },
///     UploadIntent::UnitTestsGeneration => { /* ... */ },
///     UploadIntent::WorkspaceContext => { /* ... */ },
///     other @ _ if other.as_str() == "NewFeature" => { /* handles a case for `NewFeature` */ },
///     _ => { /* ... */ },
/// }
/// ```
/// The above code demonstrates that when `uploadintent` represents
/// `NewFeature`, the execution path will lead to the second last match arm,
/// even though the enum does not contain a variant `UploadIntent::NewFeature`
/// in the current version of SDK. The reason is that the variable `other`,
/// created by the `@` operator, is bound to
/// `UploadIntent::Unknown(UnknownVariantValue("NewFeature".to_owned()))`
/// and calling `as_str` on it yields `"NewFeature"`.
/// This match expression is forward-compatible when executed with a newer
/// version of SDK where the variant `UploadIntent::NewFeature` is defined.
/// Specifically, when `uploadintent` represents `NewFeature`,
/// the execution path will hit the second last match arm as before by virtue of
/// calling `as_str` on `UploadIntent::NewFeature` also yielding `"NewFeature"`.
///
/// Explicitly matching on the `Unknown` variant should
/// be avoided for two reasons:
/// - The inner data `UnknownVariantValue` is opaque, and no further information can be extracted.
/// - It might inadvertently shadow other intended match arms.
///
/// Upload Intent
#[non_exhaustive]
#[derive(
    ::std::clone::Clone,
    ::std::cmp::Eq,
    ::std::cmp::Ord,
    ::std::cmp::PartialEq,
    ::std::cmp::PartialOrd,
    ::std::fmt::Debug,
    ::std::hash::Hash,
)]
pub enum UploadIntent {
    /// Agentic Code Review
    AgenticCodeReview,
    /// Automatic File Security Scan
    AutomaticFileSecurityScan,
    /// Code Fix Generation
    CodeFixGeneration,
    /// Full Project Security Scan
    FullProjectSecurityScan,
    /// Code Task Assist
    TaskAssistPlanning,
    /// Code Transformation
    Transformation,
    /// Unit Test Generation
    UnitTestsGeneration,
    /// Workspace Context
    WorkspaceContext,
    /// `Unknown` contains new variants that have been added since this code was generated.
    #[deprecated(
        note = "Don't directly match on `Unknown`. See the docs on this enum for the correct way to handle unknown variants."
    )]
    Unknown(crate::primitives::sealed_enum_unknown::UnknownVariantValue),
}
impl ::std::convert::From<&str> for UploadIntent {
    fn from(s: &str) -> Self {
        match s {
            "AGENTIC_CODE_REVIEW" => UploadIntent::AgenticCodeReview,
            "AUTOMATIC_FILE_SECURITY_SCAN" => UploadIntent::AutomaticFileSecurityScan,
            "CODE_FIX_GENERATION" => UploadIntent::CodeFixGeneration,
            "FULL_PROJECT_SECURITY_SCAN" => UploadIntent::FullProjectSecurityScan,
            "TASK_ASSIST_PLANNING" => UploadIntent::TaskAssistPlanning,
            "TRANSFORMATION" => UploadIntent::Transformation,
            "UNIT_TESTS_GENERATION" => UploadIntent::UnitTestsGeneration,
            "WORKSPACE_CONTEXT" => UploadIntent::WorkspaceContext,
            other => UploadIntent::Unknown(crate::primitives::sealed_enum_unknown::UnknownVariantValue(
                other.to_owned(),
            )),
        }
    }
}
impl ::std::str::FromStr for UploadIntent {
    type Err = ::std::convert::Infallible;

    fn from_str(s: &str) -> ::std::result::Result<Self, <Self as ::std::str::FromStr>::Err> {
        ::std::result::Result::Ok(UploadIntent::from(s))
    }
}
impl UploadIntent {
    /// Returns the `&str` value of the enum member.
    pub fn as_str(&self) -> &str {
        match self {
            UploadIntent::AgenticCodeReview => "AGENTIC_CODE_REVIEW",
            UploadIntent::AutomaticFileSecurityScan => "AUTOMATIC_FILE_SECURITY_SCAN",
            UploadIntent::CodeFixGeneration => "CODE_FIX_GENERATION",
            UploadIntent::FullProjectSecurityScan => "FULL_PROJECT_SECURITY_SCAN",
            UploadIntent::TaskAssistPlanning => "TASK_ASSIST_PLANNING",
            UploadIntent::Transformation => "TRANSFORMATION",
            UploadIntent::UnitTestsGeneration => "UNIT_TESTS_GENERATION",
            UploadIntent::WorkspaceContext => "WORKSPACE_CONTEXT",
            UploadIntent::Unknown(value) => value.as_str(),
        }
    }

    /// Returns all the `&str` representations of the enum members.
    pub const fn values() -> &'static [&'static str] {
        &[
            "AGENTIC_CODE_REVIEW",
            "AUTOMATIC_FILE_SECURITY_SCAN",
            "CODE_FIX_GENERATION",
            "FULL_PROJECT_SECURITY_SCAN",
            "TASK_ASSIST_PLANNING",
            "TRANSFORMATION",
            "UNIT_TESTS_GENERATION",
            "WORKSPACE_CONTEXT",
        ]
    }
}
impl ::std::convert::AsRef<str> for UploadIntent {
    fn as_ref(&self) -> &str {
        self.as_str()
    }
}
impl UploadIntent {
    /// Parses the enum value while disallowing unknown variants.
    ///
    /// Unknown variants will result in an error.
    pub fn try_parse(value: &str) -> ::std::result::Result<Self, crate::error::UnknownVariantError> {
        match Self::from(value) {
            #[allow(deprecated)]
            Self::Unknown(_) => ::std::result::Result::Err(crate::error::UnknownVariantError::new(value)),
            known => Ok(known),
        }
    }
}
impl ::std::fmt::Display for UploadIntent {
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        match self {
            UploadIntent::AgenticCodeReview => write!(f, "AGENTIC_CODE_REVIEW"),
            UploadIntent::AutomaticFileSecurityScan => write!(f, "AUTOMATIC_FILE_SECURITY_SCAN"),
            UploadIntent::CodeFixGeneration => write!(f, "CODE_FIX_GENERATION"),
            UploadIntent::FullProjectSecurityScan => write!(f, "FULL_PROJECT_SECURITY_SCAN"),
            UploadIntent::TaskAssistPlanning => write!(f, "TASK_ASSIST_PLANNING"),
            UploadIntent::Transformation => write!(f, "TRANSFORMATION"),
            UploadIntent::UnitTestsGeneration => write!(f, "UNIT_TESTS_GENERATION"),
            UploadIntent::WorkspaceContext => write!(f, "WORKSPACE_CONTEXT"),
            UploadIntent::Unknown(value) => write!(f, "{}", value),
        }
    }
}
