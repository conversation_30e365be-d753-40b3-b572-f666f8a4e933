// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct SubscriptionPlanDescription {
    #[allow(missing_docs)] // documentation missing in model
    pub title: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub feature_header: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub features: ::std::option::Option<::std::vec::Vec<::std::string::String>>,
}
impl SubscriptionPlanDescription {
    #[allow(missing_docs)] // documentation missing in model
    pub fn title(&self) -> ::std::option::Option<&str> {
        self.title.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn feature_header(&self) -> ::std::option::Option<&str> {
        self.feature_header.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    /// If no value was sent for this field, a default will be set. If you want to determine if no
    /// value was sent, use `.features.is_none()`.
    pub fn features(&self) -> &[::std::string::String] {
        self.features.as_deref().unwrap_or_default()
    }
}
impl SubscriptionPlanDescription {
    /// Creates a new builder-style object to manufacture
    /// [`SubscriptionPlanDescription`](crate::types::SubscriptionPlanDescription).
    pub fn builder() -> crate::types::builders::SubscriptionPlanDescriptionBuilder {
        crate::types::builders::SubscriptionPlanDescriptionBuilder::default()
    }
}

/// A builder for [`SubscriptionPlanDescription`](crate::types::SubscriptionPlanDescription).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct SubscriptionPlanDescriptionBuilder {
    pub(crate) title: ::std::option::Option<::std::string::String>,
    pub(crate) feature_header: ::std::option::Option<::std::string::String>,
    pub(crate) features: ::std::option::Option<::std::vec::Vec<::std::string::String>>,
}
impl SubscriptionPlanDescriptionBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn title(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.title = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_title(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.title = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_title(&self) -> &::std::option::Option<::std::string::String> {
        &self.title
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn feature_header(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.feature_header = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_feature_header(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.feature_header = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_feature_header(&self) -> &::std::option::Option<::std::string::String> {
        &self.feature_header
    }

    /// Appends an item to `features`.
    ///
    /// To override the contents of this collection use [`set_features`](Self::set_features).
    pub fn features(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        let mut v = self.features.unwrap_or_default();
        v.push(input.into());
        self.features = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_features(mut self, input: ::std::option::Option<::std::vec::Vec<::std::string::String>>) -> Self {
        self.features = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_features(&self) -> &::std::option::Option<::std::vec::Vec<::std::string::String>> {
        &self.features
    }

    /// Consumes the builder and constructs a
    /// [`SubscriptionPlanDescription`](crate::types::SubscriptionPlanDescription).
    pub fn build(self) -> crate::types::SubscriptionPlanDescription {
        crate::types::SubscriptionPlanDescription {
            title: self.title,
            feature_header: self.feature_header,
            features: self.features,
        }
    }
}
