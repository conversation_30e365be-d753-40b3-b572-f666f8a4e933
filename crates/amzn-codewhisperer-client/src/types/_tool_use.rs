// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Contains information about a tool that the model is requesting be run. The model uses the result
/// from the tool to generate a response.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct ToolUse {
    /// The ID for the tool request.
    pub tool_use_id: ::std::string::String,
    /// The name for the tool.
    pub name: ::std::string::String,
    /// The input to pass to the tool.
    pub input: ::aws_smithy_types::Document,
}
impl ToolUse {
    /// The ID for the tool request.
    pub fn tool_use_id(&self) -> &str {
        use std::ops::Deref;
        self.tool_use_id.deref()
    }

    /// The name for the tool.
    pub fn name(&self) -> &str {
        use std::ops::Deref;
        self.name.deref()
    }

    /// The input to pass to the tool.
    pub fn input(&self) -> &::aws_smithy_types::Document {
        &self.input
    }
}
impl ::std::fmt::Debug for ToolUse {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("ToolUse");
        formatter.field("tool_use_id", &self.tool_use_id);
        formatter.field("name", &"*** Sensitive Data Redacted ***");
        formatter.field("input", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
impl ToolUse {
    /// Creates a new builder-style object to manufacture [`ToolUse`](crate::types::ToolUse).
    pub fn builder() -> crate::types::builders::ToolUseBuilder {
        crate::types::builders::ToolUseBuilder::default()
    }
}

/// A builder for [`ToolUse`](crate::types::ToolUse).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct ToolUseBuilder {
    pub(crate) tool_use_id: ::std::option::Option<::std::string::String>,
    pub(crate) name: ::std::option::Option<::std::string::String>,
    pub(crate) input: ::std::option::Option<::aws_smithy_types::Document>,
}
impl ToolUseBuilder {
    /// The ID for the tool request.
    /// This field is required.
    pub fn tool_use_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.tool_use_id = ::std::option::Option::Some(input.into());
        self
    }

    /// The ID for the tool request.
    pub fn set_tool_use_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.tool_use_id = input;
        self
    }

    /// The ID for the tool request.
    pub fn get_tool_use_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.tool_use_id
    }

    /// The name for the tool.
    /// This field is required.
    pub fn name(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.name = ::std::option::Option::Some(input.into());
        self
    }

    /// The name for the tool.
    pub fn set_name(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.name = input;
        self
    }

    /// The name for the tool.
    pub fn get_name(&self) -> &::std::option::Option<::std::string::String> {
        &self.name
    }

    /// The input to pass to the tool.
    /// This field is required.
    pub fn input(mut self, input: ::aws_smithy_types::Document) -> Self {
        self.input = ::std::option::Option::Some(input);
        self
    }

    /// The input to pass to the tool.
    pub fn set_input(mut self, input: ::std::option::Option<::aws_smithy_types::Document>) -> Self {
        self.input = input;
        self
    }

    /// The input to pass to the tool.
    pub fn get_input(&self) -> &::std::option::Option<::aws_smithy_types::Document> {
        &self.input
    }

    /// Consumes the builder and constructs a [`ToolUse`](crate::types::ToolUse).
    /// This method will fail if any of the following fields are not set:
    /// - [`tool_use_id`](crate::types::builders::ToolUseBuilder::tool_use_id)
    /// - [`name`](crate::types::builders::ToolUseBuilder::name)
    /// - [`input`](crate::types::builders::ToolUseBuilder::input)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::ToolUse, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::ToolUse {
            tool_use_id: self.tool_use_id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "tool_use_id",
                    "tool_use_id was not specified but it is required when building ToolUse",
                )
            })?,
            name: self.name.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "name",
                    "name was not specified but it is required when building ToolUse",
                )
            })?,
            input: self.input.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "input",
                    "input was not specified but it is required when building ToolUse",
                )
            })?,
        })
    }
}
impl ::std::fmt::Debug for ToolUseBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("ToolUseBuilder");
        formatter.field("tool_use_id", &self.tool_use_id);
        formatter.field("name", &"*** Sensitive Data Redacted ***");
        formatter.field("input", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
