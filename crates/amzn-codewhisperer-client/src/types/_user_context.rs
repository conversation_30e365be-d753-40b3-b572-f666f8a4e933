// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct UserContext {
    #[allow(missing_docs)] // documentation missing in model
    pub ide_category: crate::types::IdeCategory,
    #[allow(missing_docs)] // documentation missing in model
    pub operating_system: crate::types::OperatingSystem,
    #[allow(missing_docs)] // documentation missing in model
    pub product: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub client_id: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub ide_version: ::std::option::Option<::std::string::String>,
}
impl UserContext {
    #[allow(missing_docs)] // documentation missing in model
    pub fn ide_category(&self) -> &crate::types::IdeCategory {
        &self.ide_category
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn operating_system(&self) -> &crate::types::OperatingSystem {
        &self.operating_system
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn product(&self) -> &str {
        use std::ops::Deref;
        self.product.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_id(&self) -> ::std::option::Option<&str> {
        self.client_id.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn ide_version(&self) -> ::std::option::Option<&str> {
        self.ide_version.as_deref()
    }
}
impl UserContext {
    /// Creates a new builder-style object to manufacture
    /// [`UserContext`](crate::types::UserContext).
    pub fn builder() -> crate::types::builders::UserContextBuilder {
        crate::types::builders::UserContextBuilder::default()
    }
}

/// A builder for [`UserContext`](crate::types::UserContext).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct UserContextBuilder {
    pub(crate) ide_category: ::std::option::Option<crate::types::IdeCategory>,
    pub(crate) operating_system: ::std::option::Option<crate::types::OperatingSystem>,
    pub(crate) product: ::std::option::Option<::std::string::String>,
    pub(crate) client_id: ::std::option::Option<::std::string::String>,
    pub(crate) ide_version: ::std::option::Option<::std::string::String>,
}
impl UserContextBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn ide_category(mut self, input: crate::types::IdeCategory) -> Self {
        self.ide_category = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_ide_category(mut self, input: ::std::option::Option<crate::types::IdeCategory>) -> Self {
        self.ide_category = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_ide_category(&self) -> &::std::option::Option<crate::types::IdeCategory> {
        &self.ide_category
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn operating_system(mut self, input: crate::types::OperatingSystem) -> Self {
        self.operating_system = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_operating_system(mut self, input: ::std::option::Option<crate::types::OperatingSystem>) -> Self {
        self.operating_system = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_operating_system(&self) -> &::std::option::Option<crate::types::OperatingSystem> {
        &self.operating_system
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn product(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.product = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_product(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.product = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_product(&self) -> &::std::option::Option<::std::string::String> {
        &self.product
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn client_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.client_id = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_client_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.client_id = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_client_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.client_id
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn ide_version(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.ide_version = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_ide_version(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.ide_version = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_ide_version(&self) -> &::std::option::Option<::std::string::String> {
        &self.ide_version
    }

    /// Consumes the builder and constructs a [`UserContext`](crate::types::UserContext).
    /// This method will fail if any of the following fields are not set:
    /// - [`ide_category`](crate::types::builders::UserContextBuilder::ide_category)
    /// - [`operating_system`](crate::types::builders::UserContextBuilder::operating_system)
    /// - [`product`](crate::types::builders::UserContextBuilder::product)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::UserContext, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::UserContext {
            ide_category: self.ide_category.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "ide_category",
                    "ide_category was not specified but it is required when building UserContext",
                )
            })?,
            operating_system: self.operating_system.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "operating_system",
                    "operating_system was not specified but it is required when building UserContext",
                )
            })?,
            product: self.product.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "product",
                    "product was not specified but it is required when building UserContext",
                )
            })?,
            client_id: self.client_id,
            ide_version: self.ide_version,
        })
    }
}
