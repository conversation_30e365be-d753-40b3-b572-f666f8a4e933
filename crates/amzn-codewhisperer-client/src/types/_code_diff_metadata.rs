// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct CodeDiffMetadata {
    #[allow(missing_docs)] // documentation missing in model
    pub code_diff_path: ::std::option::Option<::std::string::String>,
}
impl CodeDiffMetadata {
    #[allow(missing_docs)] // documentation missing in model
    pub fn code_diff_path(&self) -> ::std::option::Option<&str> {
        self.code_diff_path.as_deref()
    }
}
impl CodeDiffMetadata {
    /// Creates a new builder-style object to manufacture
    /// [`CodeDiffMetadata`](crate::types::CodeDiffMetadata).
    pub fn builder() -> crate::types::builders::CodeDiffMetadataBuilder {
        crate::types::builders::CodeDiffMetadataBuilder::default()
    }
}

/// A builder for [`CodeDiffMetadata`](crate::types::CodeDiffMetadata).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct CodeDiffMetadataBuilder {
    pub(crate) code_diff_path: ::std::option::Option<::std::string::String>,
}
impl CodeDiffMetadataBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn code_diff_path(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.code_diff_path = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_code_diff_path(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.code_diff_path = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_code_diff_path(&self) -> &::std::option::Option<::std::string::String> {
        &self.code_diff_path
    }

    /// Consumes the builder and constructs a [`CodeDiffMetadata`](crate::types::CodeDiffMetadata).
    pub fn build(self) -> crate::types::CodeDiffMetadata {
        crate::types::CodeDiffMetadata {
            code_diff_path: self.code_diff_path,
        }
    }
}
