// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct SubscriptionInfo {
    /// Granted subscription type
    pub r#type: crate::types::SubscriptionType,
}
impl SubscriptionInfo {
    /// Granted subscription type
    pub fn r#type(&self) -> &crate::types::SubscriptionType {
        &self.r#type
    }
}
impl SubscriptionInfo {
    /// Creates a new builder-style object to manufacture
    /// [`SubscriptionInfo`](crate::types::SubscriptionInfo).
    pub fn builder() -> crate::types::builders::SubscriptionInfoBuilder {
        crate::types::builders::SubscriptionInfoBuilder::default()
    }
}

/// A builder for [`SubscriptionInfo`](crate::types::SubscriptionInfo).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct SubscriptionInfoBuilder {
    pub(crate) r#type: ::std::option::Option<crate::types::SubscriptionType>,
}
impl SubscriptionInfoBuilder {
    /// Granted subscription type
    /// This field is required.
    pub fn r#type(mut self, input: crate::types::SubscriptionType) -> Self {
        self.r#type = ::std::option::Option::Some(input);
        self
    }

    /// Granted subscription type
    pub fn set_type(mut self, input: ::std::option::Option<crate::types::SubscriptionType>) -> Self {
        self.r#type = input;
        self
    }

    /// Granted subscription type
    pub fn get_type(&self) -> &::std::option::Option<crate::types::SubscriptionType> {
        &self.r#type
    }

    /// Consumes the builder and constructs a [`SubscriptionInfo`](crate::types::SubscriptionInfo).
    /// This method will fail if any of the following fields are not set:
    /// - [`r#type`](crate::types::builders::SubscriptionInfoBuilder::type)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::SubscriptionInfo, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::SubscriptionInfo {
            r#type: self.r#type.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "r#type",
                    "r#type was not specified but it is required when building SubscriptionInfo",
                )
            })?,
        })
    }
}
