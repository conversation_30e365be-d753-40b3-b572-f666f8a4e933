// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Represents a Workspace state uploaded to S3 for Async Code Actions
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct WorkspaceState {
    /// Upload ID representing an Upload using a PreSigned URL
    pub upload_id: ::std::string::String,
    /// Primary programming language of the Workspace
    pub programming_language: crate::types::ProgrammingLanguage,
    /// Workspace context truncation schemes based on usecase
    pub context_truncation_scheme: ::std::option::Option<crate::types::ContextTruncationScheme>,
}
impl WorkspaceState {
    /// Upload ID representing an Upload using a PreSigned URL
    pub fn upload_id(&self) -> &str {
        use std::ops::Deref;
        self.upload_id.deref()
    }

    /// Primary programming language of the Workspace
    pub fn programming_language(&self) -> &crate::types::ProgrammingLanguage {
        &self.programming_language
    }

    /// Workspace context truncation schemes based on usecase
    pub fn context_truncation_scheme(&self) -> ::std::option::Option<&crate::types::ContextTruncationScheme> {
        self.context_truncation_scheme.as_ref()
    }
}
impl WorkspaceState {
    /// Creates a new builder-style object to manufacture
    /// [`WorkspaceState`](crate::types::WorkspaceState).
    pub fn builder() -> crate::types::builders::WorkspaceStateBuilder {
        crate::types::builders::WorkspaceStateBuilder::default()
    }
}

/// A builder for [`WorkspaceState`](crate::types::WorkspaceState).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct WorkspaceStateBuilder {
    pub(crate) upload_id: ::std::option::Option<::std::string::String>,
    pub(crate) programming_language: ::std::option::Option<crate::types::ProgrammingLanguage>,
    pub(crate) context_truncation_scheme: ::std::option::Option<crate::types::ContextTruncationScheme>,
}
impl WorkspaceStateBuilder {
    /// Upload ID representing an Upload using a PreSigned URL
    /// This field is required.
    pub fn upload_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.upload_id = ::std::option::Option::Some(input.into());
        self
    }

    /// Upload ID representing an Upload using a PreSigned URL
    pub fn set_upload_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.upload_id = input;
        self
    }

    /// Upload ID representing an Upload using a PreSigned URL
    pub fn get_upload_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.upload_id
    }

    /// Primary programming language of the Workspace
    /// This field is required.
    pub fn programming_language(mut self, input: crate::types::ProgrammingLanguage) -> Self {
        self.programming_language = ::std::option::Option::Some(input);
        self
    }

    /// Primary programming language of the Workspace
    pub fn set_programming_language(mut self, input: ::std::option::Option<crate::types::ProgrammingLanguage>) -> Self {
        self.programming_language = input;
        self
    }

    /// Primary programming language of the Workspace
    pub fn get_programming_language(&self) -> &::std::option::Option<crate::types::ProgrammingLanguage> {
        &self.programming_language
    }

    /// Workspace context truncation schemes based on usecase
    pub fn context_truncation_scheme(mut self, input: crate::types::ContextTruncationScheme) -> Self {
        self.context_truncation_scheme = ::std::option::Option::Some(input);
        self
    }

    /// Workspace context truncation schemes based on usecase
    pub fn set_context_truncation_scheme(
        mut self,
        input: ::std::option::Option<crate::types::ContextTruncationScheme>,
    ) -> Self {
        self.context_truncation_scheme = input;
        self
    }

    /// Workspace context truncation schemes based on usecase
    pub fn get_context_truncation_scheme(&self) -> &::std::option::Option<crate::types::ContextTruncationScheme> {
        &self.context_truncation_scheme
    }

    /// Consumes the builder and constructs a [`WorkspaceState`](crate::types::WorkspaceState).
    /// This method will fail if any of the following fields are not set:
    /// - [`upload_id`](crate::types::builders::WorkspaceStateBuilder::upload_id)
    /// - [`programming_language`](crate::types::builders::WorkspaceStateBuilder::programming_language)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::WorkspaceState, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::WorkspaceState {
            upload_id: self.upload_id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "upload_id",
                    "upload_id was not specified but it is required when building WorkspaceState",
                )
            })?,
            programming_language: self.programming_language.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "programming_language",
                    "programming_language was not specified but it is required when building WorkspaceState",
                )
            })?,
            context_truncation_scheme: self.context_truncation_scheme,
        })
    }
}
