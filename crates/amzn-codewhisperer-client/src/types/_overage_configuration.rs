// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct OverageConfiguration {
    #[allow(missing_docs)] // documentation missing in model
    pub overage_status: crate::types::OverageStatus,
}
impl OverageConfiguration {
    #[allow(missing_docs)] // documentation missing in model
    pub fn overage_status(&self) -> &crate::types::OverageStatus {
        &self.overage_status
    }
}
impl OverageConfiguration {
    /// Creates a new builder-style object to manufacture
    /// [`OverageConfiguration`](crate::types::OverageConfiguration).
    pub fn builder() -> crate::types::builders::OverageConfigurationBuilder {
        crate::types::builders::OverageConfigurationBuilder::default()
    }
}

/// A builder for [`OverageConfiguration`](crate::types::OverageConfiguration).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct OverageConfigurationBuilder {
    pub(crate) overage_status: ::std::option::Option<crate::types::OverageStatus>,
}
impl OverageConfigurationBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn overage_status(mut self, input: crate::types::OverageStatus) -> Self {
        self.overage_status = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_overage_status(mut self, input: ::std::option::Option<crate::types::OverageStatus>) -> Self {
        self.overage_status = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_overage_status(&self) -> &::std::option::Option<crate::types::OverageStatus> {
        &self.overage_status
    }

    /// Consumes the builder and constructs a
    /// [`OverageConfiguration`](crate::types::OverageConfiguration). This method will fail if
    /// any of the following fields are not set:
    /// - [`overage_status`](crate::types::builders::OverageConfigurationBuilder::overage_status)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::OverageConfiguration, ::aws_smithy_types::error::operation::BuildError>
    {
        ::std::result::Result::Ok(crate::types::OverageConfiguration {
            overage_status: self.overage_status.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "overage_status",
                    "overage_status was not specified but it is required when building OverageConfiguration",
                )
            })?,
        })
    }
}
