// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Settings information passed by the Q widget
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct UserSettings {
    #[allow(missing_docs)] // documentation missing in model
    pub has_consented_to_cross_region_calls: ::std::option::Option<bool>,
}
impl UserSettings {
    #[allow(missing_docs)] // documentation missing in model
    pub fn has_consented_to_cross_region_calls(&self) -> ::std::option::Option<bool> {
        self.has_consented_to_cross_region_calls
    }
}
impl UserSettings {
    /// Creates a new builder-style object to manufacture
    /// [`UserSettings`](crate::types::UserSettings).
    pub fn builder() -> crate::types::builders::UserSettingsBuilder {
        crate::types::builders::UserSettingsBuilder::default()
    }
}

/// A builder for [`UserSettings`](crate::types::UserSettings).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct UserSettingsBuilder {
    pub(crate) has_consented_to_cross_region_calls: ::std::option::Option<bool>,
}
impl UserSettingsBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn has_consented_to_cross_region_calls(mut self, input: bool) -> Self {
        self.has_consented_to_cross_region_calls = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_has_consented_to_cross_region_calls(mut self, input: ::std::option::Option<bool>) -> Self {
        self.has_consented_to_cross_region_calls = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_has_consented_to_cross_region_calls(&self) -> &::std::option::Option<bool> {
        &self.has_consented_to_cross_region_calls
    }

    /// Consumes the builder and constructs a [`UserSettings`](crate::types::UserSettings).
    pub fn build(self) -> crate::types::UserSettings {
        crate::types::UserSettings {
            has_consented_to_cross_region_calls: self.has_consented_to_cross_region_calls,
        }
    }
}
