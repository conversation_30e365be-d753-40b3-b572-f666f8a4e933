// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct WorkspaceMetadata {
    #[allow(missing_docs)] // documentation missing in model
    pub workspace_id: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub workspace_status: crate::types::WorkspaceStatus,
    #[allow(missing_docs)] // documentation missing in model
    pub environment_address: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub environment_id: ::std::option::Option<::std::string::String>,
}
impl WorkspaceMetadata {
    #[allow(missing_docs)] // documentation missing in model
    pub fn workspace_id(&self) -> &str {
        use std::ops::Deref;
        self.workspace_id.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn workspace_status(&self) -> &crate::types::WorkspaceStatus {
        &self.workspace_status
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn environment_address(&self) -> ::std::option::Option<&str> {
        self.environment_address.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn environment_id(&self) -> ::std::option::Option<&str> {
        self.environment_id.as_deref()
    }
}
impl ::std::fmt::Debug for WorkspaceMetadata {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("WorkspaceMetadata");
        formatter.field("workspace_id", &self.workspace_id);
        formatter.field("workspace_status", &self.workspace_status);
        formatter.field("environment_address", &"*** Sensitive Data Redacted ***");
        formatter.field("environment_id", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
impl WorkspaceMetadata {
    /// Creates a new builder-style object to manufacture
    /// [`WorkspaceMetadata`](crate::types::WorkspaceMetadata).
    pub fn builder() -> crate::types::builders::WorkspaceMetadataBuilder {
        crate::types::builders::WorkspaceMetadataBuilder::default()
    }
}

/// A builder for [`WorkspaceMetadata`](crate::types::WorkspaceMetadata).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct WorkspaceMetadataBuilder {
    pub(crate) workspace_id: ::std::option::Option<::std::string::String>,
    pub(crate) workspace_status: ::std::option::Option<crate::types::WorkspaceStatus>,
    pub(crate) environment_address: ::std::option::Option<::std::string::String>,
    pub(crate) environment_id: ::std::option::Option<::std::string::String>,
}
impl WorkspaceMetadataBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn workspace_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.workspace_id = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_workspace_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.workspace_id = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_workspace_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.workspace_id
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn workspace_status(mut self, input: crate::types::WorkspaceStatus) -> Self {
        self.workspace_status = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_workspace_status(mut self, input: ::std::option::Option<crate::types::WorkspaceStatus>) -> Self {
        self.workspace_status = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_workspace_status(&self) -> &::std::option::Option<crate::types::WorkspaceStatus> {
        &self.workspace_status
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn environment_address(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.environment_address = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_environment_address(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.environment_address = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_environment_address(&self) -> &::std::option::Option<::std::string::String> {
        &self.environment_address
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn environment_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.environment_id = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_environment_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.environment_id = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_environment_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.environment_id
    }

    /// Consumes the builder and constructs a
    /// [`WorkspaceMetadata`](crate::types::WorkspaceMetadata). This method will fail if any of
    /// the following fields are not set:
    /// - [`workspace_id`](crate::types::builders::WorkspaceMetadataBuilder::workspace_id)
    /// - [`workspace_status`](crate::types::builders::WorkspaceMetadataBuilder::workspace_status)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::WorkspaceMetadata, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::WorkspaceMetadata {
            workspace_id: self.workspace_id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "workspace_id",
                    "workspace_id was not specified but it is required when building WorkspaceMetadata",
                )
            })?,
            workspace_status: self.workspace_status.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "workspace_status",
                    "workspace_status was not specified but it is required when building WorkspaceMetadata",
                )
            })?,
            environment_address: self.environment_address,
            environment_id: self.environment_id,
        })
    }
}
impl ::std::fmt::Debug for WorkspaceMetadataBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("WorkspaceMetadataBuilder");
        formatter.field("workspace_id", &self.workspace_id);
        formatter.field("workspace_status", &self.workspace_status);
        formatter.field("environment_address", &"*** Sensitive Data Redacted ***");
        formatter.field("environment_id", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
