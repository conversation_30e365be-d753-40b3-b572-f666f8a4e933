// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// When writing a match expression against `TransformationDotNetRuntimeEnv`, it is important to
/// ensure your code is forward-compatible. That is, if a match arm handles a case for a
/// feature that is supported by the service but has not been represented as an enum
/// variant in a current version of SDK, your code should continue to work when you
/// upgrade SDK to a future version in which the enum does include a variant for that
/// feature.
///
/// Here is an example of how you can make a match expression forward-compatible:
///
/// ```text
/// # let transformationdotnetruntimeenv = unimplemented!();
/// match transformationdotnetruntimeenv {
///     TransformationDotNetRuntimeEnv::Net50 => { /* ... */ },
///     TransformationDotNetRuntimeEnv::Net60 => { /* ... */ },
///     TransformationDotNetRuntimeEnv::Net70 => { /* ... */ },
///     TransformationDotNetRuntimeEnv::Net80 => { /* ... */ },
///     TransformationDotNetRuntimeEnv::Net90 => { /* ... */ },
///     TransformationDotNetRuntimeEnv::NetStandard20 => { /* ... */ },
///     other @ _ if other.as_str() == "NewFeature" => { /* handles a case for `NewFeature` */ },
///     _ => { /* ... */ },
/// }
/// ```
/// The above code demonstrates that when `transformationdotnetruntimeenv` represents
/// `NewFeature`, the execution path will lead to the second last match arm,
/// even though the enum does not contain a variant `TransformationDotNetRuntimeEnv::NewFeature`
/// in the current version of SDK. The reason is that the variable `other`,
/// created by the `@` operator, is bound to
/// `TransformationDotNetRuntimeEnv::Unknown(UnknownVariantValue("NewFeature".to_owned()))`
/// and calling `as_str` on it yields `"NewFeature"`.
/// This match expression is forward-compatible when executed with a newer
/// version of SDK where the variant `TransformationDotNetRuntimeEnv::NewFeature` is defined.
/// Specifically, when `transformationdotnetruntimeenv` represents `NewFeature`,
/// the execution path will hit the second last match arm as before by virtue of
/// calling `as_str` on `TransformationDotNetRuntimeEnv::NewFeature` also yielding `"NewFeature"`.
///
/// Explicitly matching on the `Unknown` variant should
/// be avoided for two reasons:
/// - The inner data `UnknownVariantValue` is opaque, and no further information can be extracted.
/// - It might inadvertently shadow other intended match arms.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(
    ::std::clone::Clone,
    ::std::cmp::Eq,
    ::std::cmp::Ord,
    ::std::cmp::PartialEq,
    ::std::cmp::PartialOrd,
    ::std::fmt::Debug,
    ::std::hash::Hash,
)]
pub enum TransformationDotNetRuntimeEnv {
    #[allow(missing_docs)] // documentation missing in model
    Net50,
    #[allow(missing_docs)] // documentation missing in model
    Net60,
    #[allow(missing_docs)] // documentation missing in model
    Net70,
    #[allow(missing_docs)] // documentation missing in model
    Net80,
    #[allow(missing_docs)] // documentation missing in model
    Net90,
    #[allow(missing_docs)] // documentation missing in model
    NetStandard20,
    /// `Unknown` contains new variants that have been added since this code was generated.
    #[deprecated(
        note = "Don't directly match on `Unknown`. See the docs on this enum for the correct way to handle unknown variants."
    )]
    Unknown(crate::primitives::sealed_enum_unknown::UnknownVariantValue),
}
impl ::std::convert::From<&str> for TransformationDotNetRuntimeEnv {
    fn from(s: &str) -> Self {
        match s {
            "NET_5_0" => TransformationDotNetRuntimeEnv::Net50,
            "NET_6_0" => TransformationDotNetRuntimeEnv::Net60,
            "NET_7_0" => TransformationDotNetRuntimeEnv::Net70,
            "NET_8_0" => TransformationDotNetRuntimeEnv::Net80,
            "NET_9_0" => TransformationDotNetRuntimeEnv::Net90,
            "NET_STANDARD_2_0" => TransformationDotNetRuntimeEnv::NetStandard20,
            other => TransformationDotNetRuntimeEnv::Unknown(
                crate::primitives::sealed_enum_unknown::UnknownVariantValue(other.to_owned()),
            ),
        }
    }
}
impl ::std::str::FromStr for TransformationDotNetRuntimeEnv {
    type Err = ::std::convert::Infallible;

    fn from_str(s: &str) -> ::std::result::Result<Self, <Self as ::std::str::FromStr>::Err> {
        ::std::result::Result::Ok(TransformationDotNetRuntimeEnv::from(s))
    }
}
impl TransformationDotNetRuntimeEnv {
    /// Returns the `&str` value of the enum member.
    pub fn as_str(&self) -> &str {
        match self {
            TransformationDotNetRuntimeEnv::Net50 => "NET_5_0",
            TransformationDotNetRuntimeEnv::Net60 => "NET_6_0",
            TransformationDotNetRuntimeEnv::Net70 => "NET_7_0",
            TransformationDotNetRuntimeEnv::Net80 => "NET_8_0",
            TransformationDotNetRuntimeEnv::Net90 => "NET_9_0",
            TransformationDotNetRuntimeEnv::NetStandard20 => "NET_STANDARD_2_0",
            TransformationDotNetRuntimeEnv::Unknown(value) => value.as_str(),
        }
    }

    /// Returns all the `&str` representations of the enum members.
    pub const fn values() -> &'static [&'static str] {
        &[
            "NET_5_0",
            "NET_6_0",
            "NET_7_0",
            "NET_8_0",
            "NET_9_0",
            "NET_STANDARD_2_0",
        ]
    }
}
impl ::std::convert::AsRef<str> for TransformationDotNetRuntimeEnv {
    fn as_ref(&self) -> &str {
        self.as_str()
    }
}
impl TransformationDotNetRuntimeEnv {
    /// Parses the enum value while disallowing unknown variants.
    ///
    /// Unknown variants will result in an error.
    pub fn try_parse(value: &str) -> ::std::result::Result<Self, crate::error::UnknownVariantError> {
        match Self::from(value) {
            #[allow(deprecated)]
            Self::Unknown(_) => ::std::result::Result::Err(crate::error::UnknownVariantError::new(value)),
            known => Ok(known),
        }
    }
}
impl ::std::fmt::Display for TransformationDotNetRuntimeEnv {
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        match self {
            TransformationDotNetRuntimeEnv::Net50 => write!(f, "NET_5_0"),
            TransformationDotNetRuntimeEnv::Net60 => write!(f, "NET_6_0"),
            TransformationDotNetRuntimeEnv::Net70 => write!(f, "NET_7_0"),
            TransformationDotNetRuntimeEnv::Net80 => write!(f, "NET_8_0"),
            TransformationDotNetRuntimeEnv::Net90 => write!(f, "NET_9_0"),
            TransformationDotNetRuntimeEnv::NetStandard20 => write!(f, "NET_STANDARD_2_0"),
            TransformationDotNetRuntimeEnv::Unknown(value) => write!(f, "{}", value),
        }
    }
}
