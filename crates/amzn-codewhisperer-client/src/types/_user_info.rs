// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct UserInfo {
    #[allow(missing_docs)] // documentation missing in model
    pub user_id: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub email: ::std::string::String,
}
impl UserInfo {
    #[allow(missing_docs)] // documentation missing in model
    pub fn user_id(&self) -> &str {
        use std::ops::Deref;
        self.user_id.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn email(&self) -> &str {
        use std::ops::Deref;
        self.email.deref()
    }
}
impl ::std::fmt::Debug for UserInfo {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("UserInfo");
        formatter.field("user_id", &self.user_id);
        formatter.field("email", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
impl UserInfo {
    /// Creates a new builder-style object to manufacture [`UserInfo`](crate::types::UserInfo).
    pub fn builder() -> crate::types::builders::UserInfoBuilder {
        crate::types::builders::UserInfoBuilder::default()
    }
}

/// A builder for [`UserInfo`](crate::types::UserInfo).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct UserInfoBuilder {
    pub(crate) user_id: ::std::option::Option<::std::string::String>,
    pub(crate) email: ::std::option::Option<::std::string::String>,
}
impl UserInfoBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn user_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.user_id = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_user_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.user_id = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_user_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.user_id
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn email(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.email = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_email(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.email = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_email(&self) -> &::std::option::Option<::std::string::String> {
        &self.email
    }

    /// Consumes the builder and constructs a [`UserInfo`](crate::types::UserInfo).
    /// This method will fail if any of the following fields are not set:
    /// - [`user_id`](crate::types::builders::UserInfoBuilder::user_id)
    /// - [`email`](crate::types::builders::UserInfoBuilder::email)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::UserInfo, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::UserInfo {
            user_id: self.user_id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "user_id",
                    "user_id was not specified but it is required when building UserInfo",
                )
            })?,
            email: self.email.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "email",
                    "email was not specified but it is required when building UserInfo",
                )
            })?,
        })
    }
}
impl ::std::fmt::Debug for UserInfoBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("UserInfoBuilder");
        formatter.field("user_id", &self.user_id);
        formatter.field("email", &"*** Sensitive Data Redacted ***");
        formatter.finish()
    }
}
