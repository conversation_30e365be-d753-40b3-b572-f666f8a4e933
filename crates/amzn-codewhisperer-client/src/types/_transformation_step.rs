// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct TransformationStep {
    #[allow(missing_docs)] // documentation missing in model
    pub id: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub name: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub description: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub status: crate::types::TransformationStepStatus,
    #[allow(missing_docs)] // documentation missing in model
    pub progress_updates: ::std::option::Option<::std::vec::Vec<crate::types::TransformationProgressUpdate>>,
    #[allow(missing_docs)] // documentation missing in model
    pub start_time: ::std::option::Option<::aws_smithy_types::DateTime>,
    #[allow(missing_docs)] // documentation missing in model
    pub end_time: ::std::option::Option<::aws_smithy_types::DateTime>,
}
impl TransformationStep {
    #[allow(missing_docs)] // documentation missing in model
    pub fn id(&self) -> &str {
        use std::ops::Deref;
        self.id.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn name(&self) -> &str {
        use std::ops::Deref;
        self.name.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn description(&self) -> &str {
        use std::ops::Deref;
        self.description.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn status(&self) -> &crate::types::TransformationStepStatus {
        &self.status
    }

    #[allow(missing_docs)] // documentation missing in model
    /// If no value was sent for this field, a default will be set. If you want to determine if no
    /// value was sent, use `.progress_updates.is_none()`.
    pub fn progress_updates(&self) -> &[crate::types::TransformationProgressUpdate] {
        self.progress_updates.as_deref().unwrap_or_default()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn start_time(&self) -> ::std::option::Option<&::aws_smithy_types::DateTime> {
        self.start_time.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn end_time(&self) -> ::std::option::Option<&::aws_smithy_types::DateTime> {
        self.end_time.as_ref()
    }
}
impl TransformationStep {
    /// Creates a new builder-style object to manufacture
    /// [`TransformationStep`](crate::types::TransformationStep).
    pub fn builder() -> crate::types::builders::TransformationStepBuilder {
        crate::types::builders::TransformationStepBuilder::default()
    }
}

/// A builder for [`TransformationStep`](crate::types::TransformationStep).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct TransformationStepBuilder {
    pub(crate) id: ::std::option::Option<::std::string::String>,
    pub(crate) name: ::std::option::Option<::std::string::String>,
    pub(crate) description: ::std::option::Option<::std::string::String>,
    pub(crate) status: ::std::option::Option<crate::types::TransformationStepStatus>,
    pub(crate) progress_updates: ::std::option::Option<::std::vec::Vec<crate::types::TransformationProgressUpdate>>,
    pub(crate) start_time: ::std::option::Option<::aws_smithy_types::DateTime>,
    pub(crate) end_time: ::std::option::Option<::aws_smithy_types::DateTime>,
}
impl TransformationStepBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.id = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.id = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.id
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn name(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.name = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_name(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.name = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_name(&self) -> &::std::option::Option<::std::string::String> {
        &self.name
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn description(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.description = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_description(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.description = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_description(&self) -> &::std::option::Option<::std::string::String> {
        &self.description
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn status(mut self, input: crate::types::TransformationStepStatus) -> Self {
        self.status = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_status(mut self, input: ::std::option::Option<crate::types::TransformationStepStatus>) -> Self {
        self.status = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_status(&self) -> &::std::option::Option<crate::types::TransformationStepStatus> {
        &self.status
    }

    /// Appends an item to `progress_updates`.
    ///
    /// To override the contents of this collection use
    /// [`set_progress_updates`](Self::set_progress_updates).
    pub fn progress_updates(mut self, input: crate::types::TransformationProgressUpdate) -> Self {
        let mut v = self.progress_updates.unwrap_or_default();
        v.push(input);
        self.progress_updates = ::std::option::Option::Some(v);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_progress_updates(
        mut self,
        input: ::std::option::Option<::std::vec::Vec<crate::types::TransformationProgressUpdate>>,
    ) -> Self {
        self.progress_updates = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_progress_updates(
        &self,
    ) -> &::std::option::Option<::std::vec::Vec<crate::types::TransformationProgressUpdate>> {
        &self.progress_updates
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn start_time(mut self, input: ::aws_smithy_types::DateTime) -> Self {
        self.start_time = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_start_time(mut self, input: ::std::option::Option<::aws_smithy_types::DateTime>) -> Self {
        self.start_time = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_start_time(&self) -> &::std::option::Option<::aws_smithy_types::DateTime> {
        &self.start_time
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn end_time(mut self, input: ::aws_smithy_types::DateTime) -> Self {
        self.end_time = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_end_time(mut self, input: ::std::option::Option<::aws_smithy_types::DateTime>) -> Self {
        self.end_time = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_end_time(&self) -> &::std::option::Option<::aws_smithy_types::DateTime> {
        &self.end_time
    }

    /// Consumes the builder and constructs a
    /// [`TransformationStep`](crate::types::TransformationStep). This method will fail if any
    /// of the following fields are not set:
    /// - [`id`](crate::types::builders::TransformationStepBuilder::id)
    /// - [`name`](crate::types::builders::TransformationStepBuilder::name)
    /// - [`description`](crate::types::builders::TransformationStepBuilder::description)
    /// - [`status`](crate::types::builders::TransformationStepBuilder::status)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::TransformationStep, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::TransformationStep {
            id: self.id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "id",
                    "id was not specified but it is required when building TransformationStep",
                )
            })?,
            name: self.name.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "name",
                    "name was not specified but it is required when building TransformationStep",
                )
            })?,
            description: self.description.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "description",
                    "description was not specified but it is required when building TransformationStep",
                )
            })?,
            status: self.status.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "status",
                    "status was not specified but it is required when building TransformationStep",
                )
            })?,
            progress_updates: self.progress_updates,
            start_time: self.start_time,
            end_time: self.end_time,
        })
    }
}
