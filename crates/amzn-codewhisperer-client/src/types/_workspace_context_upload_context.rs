// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct WorkspaceContextUploadContext {
    #[allow(missing_docs)] // documentation missing in model
    pub workspace_id: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub relative_path: ::std::string::String,
    /// Programming Languages supported by CodeWhisperer
    pub programming_language: crate::types::ProgrammingLanguage,
}
impl WorkspaceContextUploadContext {
    #[allow(missing_docs)] // documentation missing in model
    pub fn workspace_id(&self) -> &str {
        use std::ops::Deref;
        self.workspace_id.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn relative_path(&self) -> &str {
        use std::ops::Deref;
        self.relative_path.deref()
    }

    /// Programming Languages supported by CodeWhisperer
    pub fn programming_language(&self) -> &crate::types::ProgrammingLanguage {
        &self.programming_language
    }
}
impl ::std::fmt::Debug for WorkspaceContextUploadContext {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("WorkspaceContextUploadContext");
        formatter.field("workspace_id", &self.workspace_id);
        formatter.field("relative_path", &"*** Sensitive Data Redacted ***");
        formatter.field("programming_language", &self.programming_language);
        formatter.finish()
    }
}
impl WorkspaceContextUploadContext {
    /// Creates a new builder-style object to manufacture
    /// [`WorkspaceContextUploadContext`](crate::types::WorkspaceContextUploadContext).
    pub fn builder() -> crate::types::builders::WorkspaceContextUploadContextBuilder {
        crate::types::builders::WorkspaceContextUploadContextBuilder::default()
    }
}

/// A builder for [`WorkspaceContextUploadContext`](crate::types::WorkspaceContextUploadContext).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct WorkspaceContextUploadContextBuilder {
    pub(crate) workspace_id: ::std::option::Option<::std::string::String>,
    pub(crate) relative_path: ::std::option::Option<::std::string::String>,
    pub(crate) programming_language: ::std::option::Option<crate::types::ProgrammingLanguage>,
}
impl WorkspaceContextUploadContextBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn workspace_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.workspace_id = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_workspace_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.workspace_id = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_workspace_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.workspace_id
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn relative_path(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.relative_path = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_relative_path(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.relative_path = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_relative_path(&self) -> &::std::option::Option<::std::string::String> {
        &self.relative_path
    }

    /// Programming Languages supported by CodeWhisperer
    /// This field is required.
    pub fn programming_language(mut self, input: crate::types::ProgrammingLanguage) -> Self {
        self.programming_language = ::std::option::Option::Some(input);
        self
    }

    /// Programming Languages supported by CodeWhisperer
    pub fn set_programming_language(mut self, input: ::std::option::Option<crate::types::ProgrammingLanguage>) -> Self {
        self.programming_language = input;
        self
    }

    /// Programming Languages supported by CodeWhisperer
    pub fn get_programming_language(&self) -> &::std::option::Option<crate::types::ProgrammingLanguage> {
        &self.programming_language
    }

    /// Consumes the builder and constructs a
    /// [`WorkspaceContextUploadContext`](crate::types::WorkspaceContextUploadContext).
    /// This method will fail if any of the following fields are not set:
    /// - [`workspace_id`](crate::types::builders::WorkspaceContextUploadContextBuilder::workspace_id)
    /// - [`relative_path`](crate::types::builders::WorkspaceContextUploadContextBuilder::relative_path)
    /// - [`programming_language`](crate::types::builders::WorkspaceContextUploadContextBuilder::programming_language)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::types::WorkspaceContextUploadContext,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::types::WorkspaceContextUploadContext {
            workspace_id: self.workspace_id.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "workspace_id",
                    "workspace_id was not specified but it is required when building WorkspaceContextUploadContext",
                )
            })?,
            relative_path: self.relative_path.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "relative_path",
                    "relative_path was not specified but it is required when building WorkspaceContextUploadContext",
                )
            })?,
            programming_language: self.programming_language.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "programming_language",
                    "programming_language was not specified but it is required when building WorkspaceContextUploadContext",
                )
            })?,
        })
    }
}
impl ::std::fmt::Debug for WorkspaceContextUploadContextBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("WorkspaceContextUploadContextBuilder");
        formatter.field("workspace_id", &self.workspace_id);
        formatter.field("relative_path", &"*** Sensitive Data Redacted ***");
        formatter.field("programming_language", &self.programming_language);
        formatter.finish()
    }
}
