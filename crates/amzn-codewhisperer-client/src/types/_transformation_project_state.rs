// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct TransformationProjectState {
    #[allow(missing_docs)] // documentation missing in model
    pub language: ::std::option::Option<crate::types::TransformationLanguage>,
    #[allow(missing_docs)] // documentation missing in model
    pub runtime_env: ::std::option::Option<crate::types::TransformationRuntimeEnv>,
    #[allow(missing_docs)] // documentation missing in model
    pub platform_config: ::std::option::Option<crate::types::TransformationPlatformConfig>,
    #[allow(missing_docs)] // documentation missing in model
    pub project_artifact: ::std::option::Option<crate::types::TransformationProjectArtifactDescriptor>,
}
impl TransformationProjectState {
    #[allow(missing_docs)] // documentation missing in model
    pub fn language(&self) -> ::std::option::Option<&crate::types::TransformationLanguage> {
        self.language.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn runtime_env(&self) -> ::std::option::Option<&crate::types::TransformationRuntimeEnv> {
        self.runtime_env.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn platform_config(&self) -> ::std::option::Option<&crate::types::TransformationPlatformConfig> {
        self.platform_config.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn project_artifact(&self) -> ::std::option::Option<&crate::types::TransformationProjectArtifactDescriptor> {
        self.project_artifact.as_ref()
    }
}
impl TransformationProjectState {
    /// Creates a new builder-style object to manufacture
    /// [`TransformationProjectState`](crate::types::TransformationProjectState).
    pub fn builder() -> crate::types::builders::TransformationProjectStateBuilder {
        crate::types::builders::TransformationProjectStateBuilder::default()
    }
}

/// A builder for [`TransformationProjectState`](crate::types::TransformationProjectState).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct TransformationProjectStateBuilder {
    pub(crate) language: ::std::option::Option<crate::types::TransformationLanguage>,
    pub(crate) runtime_env: ::std::option::Option<crate::types::TransformationRuntimeEnv>,
    pub(crate) platform_config: ::std::option::Option<crate::types::TransformationPlatformConfig>,
    pub(crate) project_artifact: ::std::option::Option<crate::types::TransformationProjectArtifactDescriptor>,
}
impl TransformationProjectStateBuilder {
    #[allow(missing_docs)] // documentation missing in model
    pub fn language(mut self, input: crate::types::TransformationLanguage) -> Self {
        self.language = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_language(mut self, input: ::std::option::Option<crate::types::TransformationLanguage>) -> Self {
        self.language = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_language(&self) -> &::std::option::Option<crate::types::TransformationLanguage> {
        &self.language
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn runtime_env(mut self, input: crate::types::TransformationRuntimeEnv) -> Self {
        self.runtime_env = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_runtime_env(mut self, input: ::std::option::Option<crate::types::TransformationRuntimeEnv>) -> Self {
        self.runtime_env = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_runtime_env(&self) -> &::std::option::Option<crate::types::TransformationRuntimeEnv> {
        &self.runtime_env
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn platform_config(mut self, input: crate::types::TransformationPlatformConfig) -> Self {
        self.platform_config = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_platform_config(
        mut self,
        input: ::std::option::Option<crate::types::TransformationPlatformConfig>,
    ) -> Self {
        self.platform_config = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_platform_config(&self) -> &::std::option::Option<crate::types::TransformationPlatformConfig> {
        &self.platform_config
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn project_artifact(mut self, input: crate::types::TransformationProjectArtifactDescriptor) -> Self {
        self.project_artifact = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_project_artifact(
        mut self,
        input: ::std::option::Option<crate::types::TransformationProjectArtifactDescriptor>,
    ) -> Self {
        self.project_artifact = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_project_artifact(
        &self,
    ) -> &::std::option::Option<crate::types::TransformationProjectArtifactDescriptor> {
        &self.project_artifact
    }

    /// Consumes the builder and constructs a
    /// [`TransformationProjectState`](crate::types::TransformationProjectState).
    pub fn build(self) -> crate::types::TransformationProjectState {
        crate::types::TransformationProjectState {
            language: self.language,
            runtime_env: self.runtime_env,
            platform_config: self.platform_config,
            project_artifact: self.project_artifact,
        }
    }
}
