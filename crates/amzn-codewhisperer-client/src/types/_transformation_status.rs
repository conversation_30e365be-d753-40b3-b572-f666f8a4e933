// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// When writing a match expression against `TransformationStatus`, it is important to ensure
/// your code is forward-compatible. That is, if a match arm handles a case for a
/// feature that is supported by the service but has not been represented as an enum
/// variant in a current version of SDK, your code should continue to work when you
/// upgrade SDK to a future version in which the enum does include a variant for that
/// feature.
///
/// Here is an example of how you can make a match expression forward-compatible:
///
/// ```text
/// # let transformationstatus = unimplemented!();
/// match transformationstatus {
///     TransformationStatus::Accepted => { /* ... */ },
///     TransformationStatus::Completed => { /* ... */ },
///     TransformationStatus::Created => { /* ... */ },
///     TransformationStatus::Failed => { /* ... */ },
///     TransformationStatus::PartiallyCompleted => { /* ... */ },
///     TransformationStatus::Paused => { /* ... */ },
///     TransformationStatus::Planned => { /* ... */ },
///     TransformationStatus::Planning => { /* ... */ },
///     TransformationStatus::Prepared => { /* ... */ },
///     TransformationStatus::Preparing => { /* ... */ },
///     TransformationStatus::Rejected => { /* ... */ },
///     TransformationStatus::Resumed => { /* ... */ },
///     TransformationStatus::Started => { /* ... */ },
///     TransformationStatus::Stopped => { /* ... */ },
///     TransformationStatus::Stopping => { /* ... */ },
///     TransformationStatus::Transformed => { /* ... */ },
///     TransformationStatus::Transforming => { /* ... */ },
///     other @ _ if other.as_str() == "NewFeature" => { /* handles a case for `NewFeature` */ },
///     _ => { /* ... */ },
/// }
/// ```
/// The above code demonstrates that when `transformationstatus` represents
/// `NewFeature`, the execution path will lead to the second last match arm,
/// even though the enum does not contain a variant `TransformationStatus::NewFeature`
/// in the current version of SDK. The reason is that the variable `other`,
/// created by the `@` operator, is bound to
/// `TransformationStatus::Unknown(UnknownVariantValue("NewFeature".to_owned()))`
/// and calling `as_str` on it yields `"NewFeature"`.
/// This match expression is forward-compatible when executed with a newer
/// version of SDK where the variant `TransformationStatus::NewFeature` is defined.
/// Specifically, when `transformationstatus` represents `NewFeature`,
/// the execution path will hit the second last match arm as before by virtue of
/// calling `as_str` on `TransformationStatus::NewFeature` also yielding `"NewFeature"`.
///
/// Explicitly matching on the `Unknown` variant should
/// be avoided for two reasons:
/// - The inner data `UnknownVariantValue` is opaque, and no further information can be extracted.
/// - It might inadvertently shadow other intended match arms.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(
    ::std::clone::Clone,
    ::std::cmp::Eq,
    ::std::cmp::Ord,
    ::std::cmp::PartialEq,
    ::std::cmp::PartialOrd,
    ::std::fmt::Debug,
    ::std::hash::Hash,
)]
pub enum TransformationStatus {
    #[allow(missing_docs)] // documentation missing in model
    Accepted,
    #[allow(missing_docs)] // documentation missing in model
    Completed,
    #[allow(missing_docs)] // documentation missing in model
    Created,
    #[allow(missing_docs)] // documentation missing in model
    Failed,
    #[allow(missing_docs)] // documentation missing in model
    PartiallyCompleted,
    #[allow(missing_docs)] // documentation missing in model
    Paused,
    #[allow(missing_docs)] // documentation missing in model
    Planned,
    #[allow(missing_docs)] // documentation missing in model
    Planning,
    #[allow(missing_docs)] // documentation missing in model
    Prepared,
    #[allow(missing_docs)] // documentation missing in model
    Preparing,
    #[allow(missing_docs)] // documentation missing in model
    Rejected,
    #[allow(missing_docs)] // documentation missing in model
    Resumed,
    #[allow(missing_docs)] // documentation missing in model
    Started,
    #[allow(missing_docs)] // documentation missing in model
    Stopped,
    #[allow(missing_docs)] // documentation missing in model
    Stopping,
    #[allow(missing_docs)] // documentation missing in model
    Transformed,
    #[allow(missing_docs)] // documentation missing in model
    Transforming,
    /// `Unknown` contains new variants that have been added since this code was generated.
    #[deprecated(
        note = "Don't directly match on `Unknown`. See the docs on this enum for the correct way to handle unknown variants."
    )]
    Unknown(crate::primitives::sealed_enum_unknown::UnknownVariantValue),
}
impl ::std::convert::From<&str> for TransformationStatus {
    fn from(s: &str) -> Self {
        match s {
            "ACCEPTED" => TransformationStatus::Accepted,
            "COMPLETED" => TransformationStatus::Completed,
            "CREATED" => TransformationStatus::Created,
            "FAILED" => TransformationStatus::Failed,
            "PARTIALLY_COMPLETED" => TransformationStatus::PartiallyCompleted,
            "PAUSED" => TransformationStatus::Paused,
            "PLANNED" => TransformationStatus::Planned,
            "PLANNING" => TransformationStatus::Planning,
            "PREPARED" => TransformationStatus::Prepared,
            "PREPARING" => TransformationStatus::Preparing,
            "REJECTED" => TransformationStatus::Rejected,
            "RESUMED" => TransformationStatus::Resumed,
            "STARTED" => TransformationStatus::Started,
            "STOPPED" => TransformationStatus::Stopped,
            "STOPPING" => TransformationStatus::Stopping,
            "TRANSFORMED" => TransformationStatus::Transformed,
            "TRANSFORMING" => TransformationStatus::Transforming,
            other => TransformationStatus::Unknown(crate::primitives::sealed_enum_unknown::UnknownVariantValue(
                other.to_owned(),
            )),
        }
    }
}
impl ::std::str::FromStr for TransformationStatus {
    type Err = ::std::convert::Infallible;

    fn from_str(s: &str) -> ::std::result::Result<Self, <Self as ::std::str::FromStr>::Err> {
        ::std::result::Result::Ok(TransformationStatus::from(s))
    }
}
impl TransformationStatus {
    /// Returns the `&str` value of the enum member.
    pub fn as_str(&self) -> &str {
        match self {
            TransformationStatus::Accepted => "ACCEPTED",
            TransformationStatus::Completed => "COMPLETED",
            TransformationStatus::Created => "CREATED",
            TransformationStatus::Failed => "FAILED",
            TransformationStatus::PartiallyCompleted => "PARTIALLY_COMPLETED",
            TransformationStatus::Paused => "PAUSED",
            TransformationStatus::Planned => "PLANNED",
            TransformationStatus::Planning => "PLANNING",
            TransformationStatus::Prepared => "PREPARED",
            TransformationStatus::Preparing => "PREPARING",
            TransformationStatus::Rejected => "REJECTED",
            TransformationStatus::Resumed => "RESUMED",
            TransformationStatus::Started => "STARTED",
            TransformationStatus::Stopped => "STOPPED",
            TransformationStatus::Stopping => "STOPPING",
            TransformationStatus::Transformed => "TRANSFORMED",
            TransformationStatus::Transforming => "TRANSFORMING",
            TransformationStatus::Unknown(value) => value.as_str(),
        }
    }

    /// Returns all the `&str` representations of the enum members.
    pub const fn values() -> &'static [&'static str] {
        &[
            "ACCEPTED",
            "COMPLETED",
            "CREATED",
            "FAILED",
            "PARTIALLY_COMPLETED",
            "PAUSED",
            "PLANNED",
            "PLANNING",
            "PREPARED",
            "PREPARING",
            "REJECTED",
            "RESUMED",
            "STARTED",
            "STOPPED",
            "STOPPING",
            "TRANSFORMED",
            "TRANSFORMING",
        ]
    }
}
impl ::std::convert::AsRef<str> for TransformationStatus {
    fn as_ref(&self) -> &str {
        self.as_str()
    }
}
impl TransformationStatus {
    /// Parses the enum value while disallowing unknown variants.
    ///
    /// Unknown variants will result in an error.
    pub fn try_parse(value: &str) -> ::std::result::Result<Self, crate::error::UnknownVariantError> {
        match Self::from(value) {
            #[allow(deprecated)]
            Self::Unknown(_) => ::std::result::Result::Err(crate::error::UnknownVariantError::new(value)),
            known => Ok(known),
        }
    }
}
impl ::std::fmt::Display for TransformationStatus {
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        match self {
            TransformationStatus::Accepted => write!(f, "ACCEPTED"),
            TransformationStatus::Completed => write!(f, "COMPLETED"),
            TransformationStatus::Created => write!(f, "CREATED"),
            TransformationStatus::Failed => write!(f, "FAILED"),
            TransformationStatus::PartiallyCompleted => write!(f, "PARTIALLY_COMPLETED"),
            TransformationStatus::Paused => write!(f, "PAUSED"),
            TransformationStatus::Planned => write!(f, "PLANNED"),
            TransformationStatus::Planning => write!(f, "PLANNING"),
            TransformationStatus::Prepared => write!(f, "PREPARED"),
            TransformationStatus::Preparing => write!(f, "PREPARING"),
            TransformationStatus::Rejected => write!(f, "REJECTED"),
            TransformationStatus::Resumed => write!(f, "RESUMED"),
            TransformationStatus::Started => write!(f, "STARTED"),
            TransformationStatus::Stopped => write!(f, "STOPPED"),
            TransformationStatus::Stopping => write!(f, "STOPPING"),
            TransformationStatus::Transformed => write!(f, "TRANSFORMED"),
            TransformationStatus::Transforming => write!(f, "TRANSFORMING"),
            TransformationStatus::Unknown(value) => write!(f, "{}", value),
        }
    }
}
