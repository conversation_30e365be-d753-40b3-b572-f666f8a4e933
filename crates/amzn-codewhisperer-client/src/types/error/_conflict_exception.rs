// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// This exception is thrown when the action to perform could not be completed because the resource
/// is in a conflicting state.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ConflictError {
    #[allow(missing_docs)] // documentation missing in model
    pub message: ::std::string::String,
    /// Reason for ConflictException
    pub reason: ::std::option::Option<crate::types::ConflictExceptionReason>,
    pub(crate) meta: ::aws_smithy_types::error::ErrorMetadata,
}
impl ConflictError {
    /// Reason for ConflictException
    pub fn reason(&self) -> ::std::option::Option<&crate::types::ConflictExceptionReason> {
        self.reason.as_ref()
    }
}
impl ConflictError {
    /// Returns the error message.
    pub fn message(&self) -> &str {
        &self.message
    }
}
impl ::std::fmt::Display for ConflictError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        ::std::write!(f, "ConflictError [ConflictException]")?;
        {
            ::std::write!(f, ": {}", &self.message)?;
        }
        Ok(())
    }
}
impl ::std::error::Error for ConflictError {}
impl ::aws_types::request_id::RequestId for crate::types::error::ConflictError {
    fn request_id(&self) -> Option<&str> {
        use ::aws_smithy_types::error::metadata::ProvideErrorMetadata;
        self.meta().request_id()
    }
}
impl ::aws_smithy_types::error::metadata::ProvideErrorMetadata for ConflictError {
    fn meta(&self) -> &::aws_smithy_types::error::ErrorMetadata {
        &self.meta
    }
}
impl ConflictError {
    /// Creates a new builder-style object to manufacture
    /// [`ConflictError`](crate::types::error::ConflictError).
    pub fn builder() -> crate::types::error::builders::ConflictErrorBuilder {
        crate::types::error::builders::ConflictErrorBuilder::default()
    }
}

/// A builder for [`ConflictError`](crate::types::error::ConflictError).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ConflictErrorBuilder {
    pub(crate) message: ::std::option::Option<::std::string::String>,
    pub(crate) reason: ::std::option::Option<crate::types::ConflictExceptionReason>,
    meta: std::option::Option<::aws_smithy_types::error::ErrorMetadata>,
}
impl ConflictErrorBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn message(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.message = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_message(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.message = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_message(&self) -> &::std::option::Option<::std::string::String> {
        &self.message
    }

    /// Reason for ConflictException
    pub fn reason(mut self, input: crate::types::ConflictExceptionReason) -> Self {
        self.reason = ::std::option::Option::Some(input);
        self
    }

    /// Reason for ConflictException
    pub fn set_reason(mut self, input: ::std::option::Option<crate::types::ConflictExceptionReason>) -> Self {
        self.reason = input;
        self
    }

    /// Reason for ConflictException
    pub fn get_reason(&self) -> &::std::option::Option<crate::types::ConflictExceptionReason> {
        &self.reason
    }

    /// Sets error metadata
    pub fn meta(mut self, meta: ::aws_smithy_types::error::ErrorMetadata) -> Self {
        self.meta = Some(meta);
        self
    }

    /// Sets error metadata
    pub fn set_meta(&mut self, meta: std::option::Option<::aws_smithy_types::error::ErrorMetadata>) -> &mut Self {
        self.meta = meta;
        self
    }

    /// Consumes the builder and constructs a [`ConflictError`](crate::types::error::ConflictError).
    /// This method will fail if any of the following fields are not set:
    /// - [`message`](crate::types::error::builders::ConflictErrorBuilder::message)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::error::ConflictError, ::aws_smithy_types::error::operation::BuildError>
    {
        ::std::result::Result::Ok(crate::types::error::ConflictError {
            message: self.message.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "message",
                    "message was not specified but it is required when building ConflictError",
                )
            })?,
            reason: self.reason,
            meta: self.meta.unwrap_or_default(),
        })
    }
}
