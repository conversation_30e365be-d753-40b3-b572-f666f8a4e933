// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// This exception is thrown when the input fails to satisfy the constraints specified by the
/// service.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ValidationError {
    #[allow(missing_docs)] // documentation missing in model
    pub message: ::std::string::String,
    /// Reason for ValidationException
    pub reason: ::std::option::Option<crate::types::ValidationExceptionReason>,
    pub(crate) meta: ::aws_smithy_types::error::ErrorMetadata,
}
impl ValidationError {
    /// Reason for ValidationException
    pub fn reason(&self) -> ::std::option::Option<&crate::types::ValidationExceptionReason> {
        self.reason.as_ref()
    }
}
impl ValidationError {
    /// Returns the error message.
    pub fn message(&self) -> &str {
        &self.message
    }
}
impl ::std::fmt::Display for ValidationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        ::std::write!(f, "ValidationError [ValidationException]")?;
        {
            ::std::write!(f, ": {}", &self.message)?;
        }
        Ok(())
    }
}
impl ::std::error::Error for ValidationError {}
impl ::aws_types::request_id::RequestId for crate::types::error::ValidationError {
    fn request_id(&self) -> Option<&str> {
        use ::aws_smithy_types::error::metadata::ProvideErrorMetadata;
        self.meta().request_id()
    }
}
impl ::aws_smithy_types::error::metadata::ProvideErrorMetadata for ValidationError {
    fn meta(&self) -> &::aws_smithy_types::error::ErrorMetadata {
        &self.meta
    }
}
impl ValidationError {
    /// Creates a new builder-style object to manufacture
    /// [`ValidationError`](crate::types::error::ValidationError).
    pub fn builder() -> crate::types::error::builders::ValidationErrorBuilder {
        crate::types::error::builders::ValidationErrorBuilder::default()
    }
}

/// A builder for [`ValidationError`](crate::types::error::ValidationError).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ValidationErrorBuilder {
    pub(crate) message: ::std::option::Option<::std::string::String>,
    pub(crate) reason: ::std::option::Option<crate::types::ValidationExceptionReason>,
    meta: std::option::Option<::aws_smithy_types::error::ErrorMetadata>,
}
impl ValidationErrorBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn message(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.message = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_message(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.message = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_message(&self) -> &::std::option::Option<::std::string::String> {
        &self.message
    }

    /// Reason for ValidationException
    pub fn reason(mut self, input: crate::types::ValidationExceptionReason) -> Self {
        self.reason = ::std::option::Option::Some(input);
        self
    }

    /// Reason for ValidationException
    pub fn set_reason(mut self, input: ::std::option::Option<crate::types::ValidationExceptionReason>) -> Self {
        self.reason = input;
        self
    }

    /// Reason for ValidationException
    pub fn get_reason(&self) -> &::std::option::Option<crate::types::ValidationExceptionReason> {
        &self.reason
    }

    /// Sets error metadata
    pub fn meta(mut self, meta: ::aws_smithy_types::error::ErrorMetadata) -> Self {
        self.meta = Some(meta);
        self
    }

    /// Sets error metadata
    pub fn set_meta(&mut self, meta: std::option::Option<::aws_smithy_types::error::ErrorMetadata>) -> &mut Self {
        self.meta = meta;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ValidationError`](crate::types::error::ValidationError). This method will fail if any
    /// of the following fields are not set:
    /// - [`message`](crate::types::error::builders::ValidationErrorBuilder::message)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::error::ValidationError, ::aws_smithy_types::error::operation::BuildError>
    {
        ::std::result::Result::Ok(crate::types::error::ValidationError {
            message: self.message.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "message",
                    "message was not specified but it is required when building ValidationError",
                )
            })?,
            reason: self.reason,
            meta: self.meta.unwrap_or_default(),
        })
    }
}
