// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// This exception is thrown when request was denied due to caller exceeding their usage limits
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ServiceQuotaExceededError {
    #[allow(missing_docs)] // documentation missing in model
    pub message: ::std::string::String,
    /// Reason for ServiceQuotaExceededException
    pub reason: ::std::option::Option<crate::types::ServiceQuotaExceededExceptionReason>,
    pub(crate) meta: ::aws_smithy_types::error::ErrorMetadata,
}
impl ServiceQuotaExceededError {
    /// Reason for ServiceQuotaExceededException
    pub fn reason(&self) -> ::std::option::Option<&crate::types::ServiceQuotaExceededExceptionReason> {
        self.reason.as_ref()
    }
}
impl ServiceQuotaExceededError {
    /// Returns the error message.
    pub fn message(&self) -> &str {
        &self.message
    }
}
impl ::std::fmt::Display for ServiceQuotaExceededError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        ::std::write!(f, "ServiceQuotaExceededError [ServiceQuotaExceededException]")?;
        {
            ::std::write!(f, ": {}", &self.message)?;
        }
        Ok(())
    }
}
impl ::std::error::Error for ServiceQuotaExceededError {}
impl ::aws_types::request_id::RequestId for crate::types::error::ServiceQuotaExceededError {
    fn request_id(&self) -> Option<&str> {
        use ::aws_smithy_types::error::metadata::ProvideErrorMetadata;
        self.meta().request_id()
    }
}
impl ::aws_smithy_types::error::metadata::ProvideErrorMetadata for ServiceQuotaExceededError {
    fn meta(&self) -> &::aws_smithy_types::error::ErrorMetadata {
        &self.meta
    }
}
impl ServiceQuotaExceededError {
    /// Creates a new builder-style object to manufacture
    /// [`ServiceQuotaExceededError`](crate::types::error::ServiceQuotaExceededError).
    pub fn builder() -> crate::types::error::builders::ServiceQuotaExceededErrorBuilder {
        crate::types::error::builders::ServiceQuotaExceededErrorBuilder::default()
    }
}

/// A builder for [`ServiceQuotaExceededError`](crate::types::error::ServiceQuotaExceededError).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ServiceQuotaExceededErrorBuilder {
    pub(crate) message: ::std::option::Option<::std::string::String>,
    pub(crate) reason: ::std::option::Option<crate::types::ServiceQuotaExceededExceptionReason>,
    meta: std::option::Option<::aws_smithy_types::error::ErrorMetadata>,
}
impl ServiceQuotaExceededErrorBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn message(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.message = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_message(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.message = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_message(&self) -> &::std::option::Option<::std::string::String> {
        &self.message
    }

    /// Reason for ServiceQuotaExceededException
    pub fn reason(mut self, input: crate::types::ServiceQuotaExceededExceptionReason) -> Self {
        self.reason = ::std::option::Option::Some(input);
        self
    }

    /// Reason for ServiceQuotaExceededException
    pub fn set_reason(
        mut self,
        input: ::std::option::Option<crate::types::ServiceQuotaExceededExceptionReason>,
    ) -> Self {
        self.reason = input;
        self
    }

    /// Reason for ServiceQuotaExceededException
    pub fn get_reason(&self) -> &::std::option::Option<crate::types::ServiceQuotaExceededExceptionReason> {
        &self.reason
    }

    /// Sets error metadata
    pub fn meta(mut self, meta: ::aws_smithy_types::error::ErrorMetadata) -> Self {
        self.meta = Some(meta);
        self
    }

    /// Sets error metadata
    pub fn set_meta(&mut self, meta: std::option::Option<::aws_smithy_types::error::ErrorMetadata>) -> &mut Self {
        self.meta = meta;
        self
    }

    /// Consumes the builder and constructs a
    /// [`ServiceQuotaExceededError`](crate::types::error::ServiceQuotaExceededError).
    /// This method will fail if any of the following fields are not set:
    /// - [`message`](crate::types::error::builders::ServiceQuotaExceededErrorBuilder::message)
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::types::error::ServiceQuotaExceededError,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(crate::types::error::ServiceQuotaExceededError {
            message: self.message.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "message",
                    "message was not specified but it is required when building ServiceQuotaExceededError",
                )
            })?,
            reason: self.reason,
            meta: self.meta.unwrap_or_default(),
        })
    }
}
