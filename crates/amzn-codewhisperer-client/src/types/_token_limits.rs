// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct TokenLimits {
    /// Maximum number of input tokens the model can process
    pub max_input_tokens: ::std::option::Option<i32>,
    /// Maximum number of output tokens the model can produce
    pub max_output_tokens: ::std::option::Option<i32>,
}
impl TokenLimits {
    /// Maximum number of input tokens the model can process
    pub fn max_input_tokens(&self) -> ::std::option::Option<i32> {
        self.max_input_tokens
    }

    /// Maximum number of output tokens the model can produce
    pub fn max_output_tokens(&self) -> ::std::option::Option<i32> {
        self.max_output_tokens
    }
}
impl TokenLimits {
    /// Creates a new builder-style object to manufacture
    /// [`TokenLimits`](crate::types::TokenLimits).
    pub fn builder() -> crate::types::builders::TokenLimitsBuilder {
        crate::types::builders::TokenLimitsBuilder::default()
    }
}

/// A builder for [`TokenLimits`](crate::types::TokenLimits).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct TokenLimitsBuilder {
    pub(crate) max_input_tokens: ::std::option::Option<i32>,
    pub(crate) max_output_tokens: ::std::option::Option<i32>,
}
impl TokenLimitsBuilder {
    /// Maximum number of input tokens the model can process
    pub fn max_input_tokens(mut self, input: i32) -> Self {
        self.max_input_tokens = ::std::option::Option::Some(input);
        self
    }

    /// Maximum number of input tokens the model can process
    pub fn set_max_input_tokens(mut self, input: ::std::option::Option<i32>) -> Self {
        self.max_input_tokens = input;
        self
    }

    /// Maximum number of input tokens the model can process
    pub fn get_max_input_tokens(&self) -> &::std::option::Option<i32> {
        &self.max_input_tokens
    }

    /// Maximum number of output tokens the model can produce
    pub fn max_output_tokens(mut self, input: i32) -> Self {
        self.max_output_tokens = ::std::option::Option::Some(input);
        self
    }

    /// Maximum number of output tokens the model can produce
    pub fn set_max_output_tokens(mut self, input: ::std::option::Option<i32>) -> Self {
        self.max_output_tokens = input;
        self
    }

    /// Maximum number of output tokens the model can produce
    pub fn get_max_output_tokens(&self) -> &::std::option::Option<i32> {
        &self.max_output_tokens
    }

    /// Consumes the builder and constructs a [`TokenLimits`](crate::types::TokenLimits).
    pub fn build(self) -> crate::types::TokenLimits {
        crate::types::TokenLimits {
            max_input_tokens: self.max_input_tokens,
            max_output_tokens: self.max_output_tokens,
        }
    }
}
