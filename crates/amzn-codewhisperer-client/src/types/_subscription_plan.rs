// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct SubscriptionPlan {
    #[allow(missing_docs)] // documentation missing in model
    pub name: crate::types::SubscriptionName,
    #[allow(missing_docs)] // documentation missing in model
    pub pricing: crate::types::PricingInfo,
    #[allow(missing_docs)] // documentation missing in model
    pub description: ::std::option::Option<crate::types::SubscriptionPlanDescription>,
    #[allow(missing_docs)] // documentation missing in model
    pub q_subscription_type: ::std::option::Option<crate::types::SubscriptionType>,
}
impl SubscriptionPlan {
    #[allow(missing_docs)] // documentation missing in model
    pub fn name(&self) -> &crate::types::SubscriptionName {
        &self.name
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn pricing(&self) -> &crate::types::PricingInfo {
        &self.pricing
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn description(&self) -> ::std::option::Option<&crate::types::SubscriptionPlanDescription> {
        self.description.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn q_subscription_type(&self) -> ::std::option::Option<&crate::types::SubscriptionType> {
        self.q_subscription_type.as_ref()
    }
}
impl SubscriptionPlan {
    /// Creates a new builder-style object to manufacture
    /// [`SubscriptionPlan`](crate::types::SubscriptionPlan).
    pub fn builder() -> crate::types::builders::SubscriptionPlanBuilder {
        crate::types::builders::SubscriptionPlanBuilder::default()
    }
}

/// A builder for [`SubscriptionPlan`](crate::types::SubscriptionPlan).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct SubscriptionPlanBuilder {
    pub(crate) name: ::std::option::Option<crate::types::SubscriptionName>,
    pub(crate) pricing: ::std::option::Option<crate::types::PricingInfo>,
    pub(crate) description: ::std::option::Option<crate::types::SubscriptionPlanDescription>,
    pub(crate) q_subscription_type: ::std::option::Option<crate::types::SubscriptionType>,
}
impl SubscriptionPlanBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn name(mut self, input: crate::types::SubscriptionName) -> Self {
        self.name = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_name(mut self, input: ::std::option::Option<crate::types::SubscriptionName>) -> Self {
        self.name = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_name(&self) -> &::std::option::Option<crate::types::SubscriptionName> {
        &self.name
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn pricing(mut self, input: crate::types::PricingInfo) -> Self {
        self.pricing = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_pricing(mut self, input: ::std::option::Option<crate::types::PricingInfo>) -> Self {
        self.pricing = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_pricing(&self) -> &::std::option::Option<crate::types::PricingInfo> {
        &self.pricing
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn description(mut self, input: crate::types::SubscriptionPlanDescription) -> Self {
        self.description = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_description(mut self, input: ::std::option::Option<crate::types::SubscriptionPlanDescription>) -> Self {
        self.description = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_description(&self) -> &::std::option::Option<crate::types::SubscriptionPlanDescription> {
        &self.description
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn q_subscription_type(mut self, input: crate::types::SubscriptionType) -> Self {
        self.q_subscription_type = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_q_subscription_type(mut self, input: ::std::option::Option<crate::types::SubscriptionType>) -> Self {
        self.q_subscription_type = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_q_subscription_type(&self) -> &::std::option::Option<crate::types::SubscriptionType> {
        &self.q_subscription_type
    }

    /// Consumes the builder and constructs a [`SubscriptionPlan`](crate::types::SubscriptionPlan).
    /// This method will fail if any of the following fields are not set:
    /// - [`name`](crate::types::builders::SubscriptionPlanBuilder::name)
    /// - [`pricing`](crate::types::builders::SubscriptionPlanBuilder::pricing)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::SubscriptionPlan, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::SubscriptionPlan {
            name: self.name.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "name",
                    "name was not specified but it is required when building SubscriptionPlan",
                )
            })?,
            pricing: self.pricing.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "pricing",
                    "pricing was not specified but it is required when building SubscriptionPlan",
                )
            })?,
            description: self.description,
            q_subscription_type: self.q_subscription_type,
        })
    }
}
