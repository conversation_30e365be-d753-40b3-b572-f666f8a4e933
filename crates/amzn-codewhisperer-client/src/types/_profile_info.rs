// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ProfileInfo {
    #[allow(missing_docs)] // documentation missing in model
    pub arn: ::std::string::String,
    #[allow(missing_docs)] // documentation missing in model
    pub profile_name: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub description: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub status: ::std::option::Option<crate::types::ProfileStatus>,
    #[allow(missing_docs)] // documentation missing in model
    pub profile_type: ::std::option::Option<crate::types::ProfileType>,
    #[allow(missing_docs)] // documentation missing in model
    pub opt_in_features: ::std::option::Option<crate::types::OptInFeatures>,
}
impl ProfileInfo {
    #[allow(missing_docs)] // documentation missing in model
    pub fn arn(&self) -> &str {
        use std::ops::Deref;
        self.arn.deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_name(&self) -> ::std::option::Option<&str> {
        self.profile_name.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn description(&self) -> ::std::option::Option<&str> {
        self.description.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn status(&self) -> ::std::option::Option<&crate::types::ProfileStatus> {
        self.status.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_type(&self) -> ::std::option::Option<&crate::types::ProfileType> {
        self.profile_type.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn opt_in_features(&self) -> ::std::option::Option<&crate::types::OptInFeatures> {
        self.opt_in_features.as_ref()
    }
}
impl ProfileInfo {
    /// Creates a new builder-style object to manufacture
    /// [`ProfileInfo`](crate::types::ProfileInfo).
    pub fn builder() -> crate::types::builders::ProfileInfoBuilder {
        crate::types::builders::ProfileInfoBuilder::default()
    }
}

/// A builder for [`ProfileInfo`](crate::types::ProfileInfo).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ProfileInfoBuilder {
    pub(crate) arn: ::std::option::Option<::std::string::String>,
    pub(crate) profile_name: ::std::option::Option<::std::string::String>,
    pub(crate) description: ::std::option::Option<::std::string::String>,
    pub(crate) status: ::std::option::Option<crate::types::ProfileStatus>,
    pub(crate) profile_type: ::std::option::Option<crate::types::ProfileType>,
    pub(crate) opt_in_features: ::std::option::Option<crate::types::OptInFeatures>,
}
impl ProfileInfoBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.arn = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.arn = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.arn
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_name(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.profile_name = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_name(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.profile_name = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_name(&self) -> &::std::option::Option<::std::string::String> {
        &self.profile_name
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn description(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.description = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_description(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.description = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_description(&self) -> &::std::option::Option<::std::string::String> {
        &self.description
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn status(mut self, input: crate::types::ProfileStatus) -> Self {
        self.status = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_status(mut self, input: ::std::option::Option<crate::types::ProfileStatus>) -> Self {
        self.status = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_status(&self) -> &::std::option::Option<crate::types::ProfileStatus> {
        &self.status
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_type(mut self, input: crate::types::ProfileType) -> Self {
        self.profile_type = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_type(mut self, input: ::std::option::Option<crate::types::ProfileType>) -> Self {
        self.profile_type = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_type(&self) -> &::std::option::Option<crate::types::ProfileType> {
        &self.profile_type
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn opt_in_features(mut self, input: crate::types::OptInFeatures) -> Self {
        self.opt_in_features = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_opt_in_features(mut self, input: ::std::option::Option<crate::types::OptInFeatures>) -> Self {
        self.opt_in_features = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_opt_in_features(&self) -> &::std::option::Option<crate::types::OptInFeatures> {
        &self.opt_in_features
    }

    /// Consumes the builder and constructs a [`ProfileInfo`](crate::types::ProfileInfo).
    /// This method will fail if any of the following fields are not set:
    /// - [`arn`](crate::types::builders::ProfileInfoBuilder::arn)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::ProfileInfo, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::ProfileInfo {
            arn: self.arn.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "arn",
                    "arn was not specified but it is required when building ProfileInfo",
                )
            })?,
            profile_name: self.profile_name,
            description: self.description,
            status: self.status,
            profile_type: self.profile_type,
            opt_in_features: self.opt_in_features,
        })
    }
}
