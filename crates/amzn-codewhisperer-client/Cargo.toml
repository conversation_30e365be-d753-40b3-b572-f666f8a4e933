# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "amzn-codewhisperer-client"
version = "0.1.10016"
authors = ["<PERSON> <<EMAIL>>"]
build = false
exclude = [
    "/build",
    "/Config",
    "/build-tools/",
]
publish = ["brazil"]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Rust client bindings for the Amazon CodeWhisperer Service (codewhisperer.smithy)"
readme = false

[package.metadata.docs.rs]
all-features = true
targets = ["x86_64-unknown-linux-gnu"]

[package.metadata.smithy]
codegen-version = "unknown"

[features]
behavior-version-latest = []
default = [
    "rustls",
    "rt-tokio",
]
gated-tests = []
rt-tokio = [
    "aws-smithy-async/rt-tokio",
    "aws-smithy-types/rt-tokio",
]
rustls = ["aws-smithy-runtime/tls-rustls"]
test-util = ["aws-smithy-runtime/test-util"]

[lib]
name = "amzn_codewhisperer_client"
path = "src/lib.rs"

[dependencies.aws-credential-types]
version = "1.2.1"

[dependencies.aws-runtime]
version = "1.5.5"

[dependencies.aws-smithy-async]
version = "1.2.4"

[dependencies.aws-smithy-http]
version = "0.60.12"

[dependencies.aws-smithy-json]
version = "0.61.2"

[dependencies.aws-smithy-runtime]
version = "1.7.8"
features = [
    "client",
    "http-auth",
]

[dependencies.aws-smithy-runtime-api]
version = "1.7.3"
features = [
    "client",
    "http-02x",
    "http-auth",
]

[dependencies.aws-smithy-types]
version = "1.2.13"

[dependencies.aws-types]
version = "1.3.5"

[dependencies.bytes]
version = "1.4.0"

[dependencies.fastrand]
version = "2.0.0"

[dependencies.http]
version = "0.2.9"

[dependencies.tracing]
version = "0.1"

[dev-dependencies.aws-credential-types]
version = "1.2.1"
features = ["test-util"]
