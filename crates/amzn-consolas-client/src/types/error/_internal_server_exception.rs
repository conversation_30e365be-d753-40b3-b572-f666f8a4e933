// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// This exception is thrown when an unexpected error occurred during the processing of a request.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct InternalServerError {
    #[allow(missing_docs)] // documentation missing in model
    pub message: ::std::string::String,
    /// Reason for InternalServerException
    pub reason: ::std::option::Option<crate::types::InternalServerExceptionReason>,
    pub(crate) meta: ::aws_smithy_types::error::ErrorMetadata,
}
impl InternalServerError {
    /// Reason for InternalServerException
    pub fn reason(&self) -> ::std::option::Option<&crate::types::InternalServerExceptionReason> {
        self.reason.as_ref()
    }
}
impl InternalServerError {
    /// Returns `Some(ErrorKind)` if the error is retryable. Otherwise, returns `None`.
    pub fn retryable_error_kind(&self) -> ::aws_smithy_types::retry::ErrorKind {
        ::aws_smithy_types::retry::ErrorKind::ServerError
    }

    /// Returns the error message.
    pub fn message(&self) -> &str {
        &self.message
    }
}
impl ::std::fmt::Display for InternalServerError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        ::std::write!(f, "InternalServerError [InternalServerException]")?;
        {
            ::std::write!(f, ": {}", &self.message)?;
        }
        Ok(())
    }
}
impl ::std::error::Error for InternalServerError {}
impl ::aws_types::request_id::RequestId for crate::types::error::InternalServerError {
    fn request_id(&self) -> Option<&str> {
        use ::aws_smithy_types::error::metadata::ProvideErrorMetadata;
        self.meta().request_id()
    }
}
impl ::aws_smithy_types::error::metadata::ProvideErrorMetadata for InternalServerError {
    fn meta(&self) -> &::aws_smithy_types::error::ErrorMetadata {
        &self.meta
    }
}
impl InternalServerError {
    /// Creates a new builder-style object to manufacture
    /// [`InternalServerError`](crate::types::error::InternalServerError).
    pub fn builder() -> crate::types::error::builders::InternalServerErrorBuilder {
        crate::types::error::builders::InternalServerErrorBuilder::default()
    }
}

/// A builder for [`InternalServerError`](crate::types::error::InternalServerError).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct InternalServerErrorBuilder {
    pub(crate) message: ::std::option::Option<::std::string::String>,
    pub(crate) reason: ::std::option::Option<crate::types::InternalServerExceptionReason>,
    meta: std::option::Option<::aws_smithy_types::error::ErrorMetadata>,
}
impl InternalServerErrorBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn message(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.message = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_message(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.message = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_message(&self) -> &::std::option::Option<::std::string::String> {
        &self.message
    }

    /// Reason for InternalServerException
    pub fn reason(mut self, input: crate::types::InternalServerExceptionReason) -> Self {
        self.reason = ::std::option::Option::Some(input);
        self
    }

    /// Reason for InternalServerException
    pub fn set_reason(mut self, input: ::std::option::Option<crate::types::InternalServerExceptionReason>) -> Self {
        self.reason = input;
        self
    }

    /// Reason for InternalServerException
    pub fn get_reason(&self) -> &::std::option::Option<crate::types::InternalServerExceptionReason> {
        &self.reason
    }

    /// Sets error metadata
    pub fn meta(mut self, meta: ::aws_smithy_types::error::ErrorMetadata) -> Self {
        self.meta = Some(meta);
        self
    }

    /// Sets error metadata
    pub fn set_meta(&mut self, meta: std::option::Option<::aws_smithy_types::error::ErrorMetadata>) -> &mut Self {
        self.meta = meta;
        self
    }

    /// Consumes the builder and constructs a
    /// [`InternalServerError`](crate::types::error::InternalServerError). This method will fail
    /// if any of the following fields are not set:
    /// - [`message`](crate::types::error::builders::InternalServerErrorBuilder::message)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::error::InternalServerError, ::aws_smithy_types::error::operation::BuildError>
    {
        ::std::result::Result::Ok(crate::types::error::InternalServerError {
            message: self.message.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "message",
                    "message was not specified but it is required when building InternalServerError",
                )
            })?,
            reason: self.reason,
            meta: self.meta.unwrap_or_default(),
        })
    }
}
