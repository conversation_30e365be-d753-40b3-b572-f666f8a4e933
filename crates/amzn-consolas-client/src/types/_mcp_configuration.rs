// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct McpConfiguration {
    #[allow(missing_docs)] // documentation missing in model
    pub toggle: crate::types::OptInFeatureToggle,
}
impl McpConfiguration {
    #[allow(missing_docs)] // documentation missing in model
    pub fn toggle(&self) -> &crate::types::OptInFeatureToggle {
        &self.toggle
    }
}
impl McpConfiguration {
    /// Creates a new builder-style object to manufacture
    /// [`McpConfiguration`](crate::types::McpConfiguration).
    pub fn builder() -> crate::types::builders::McpConfigurationBuilder {
        crate::types::builders::McpConfigurationBuilder::default()
    }
}

/// A builder for [`McpConfiguration`](crate::types::McpConfiguration).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct McpConfigurationBuilder {
    pub(crate) toggle: ::std::option::Option<crate::types::OptInFeatureToggle>,
}
impl McpConfigurationBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn toggle(mut self, input: crate::types::OptInFeatureToggle) -> Self {
        self.toggle = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_toggle(mut self, input: ::std::option::Option<crate::types::OptInFeatureToggle>) -> Self {
        self.toggle = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_toggle(&self) -> &::std::option::Option<crate::types::OptInFeatureToggle> {
        &self.toggle
    }

    /// Consumes the builder and constructs a [`McpConfiguration`](crate::types::McpConfiguration).
    /// This method will fail if any of the following fields are not set:
    /// - [`toggle`](crate::types::builders::McpConfigurationBuilder::toggle)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::McpConfiguration, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::McpConfiguration {
            toggle: self.toggle.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "toggle",
                    "toggle was not specified but it is required when building McpConfiguration",
                )
            })?,
        })
    }
}
