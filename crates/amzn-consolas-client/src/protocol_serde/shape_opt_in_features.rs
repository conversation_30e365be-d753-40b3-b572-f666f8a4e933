// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_opt_in_features(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::OptInFeatures,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.prompt_logging {
        #[allow(unused_mut)]
        let mut object_2 = object.key("promptLogging").start_object();
        crate::protocol_serde::shape_prompt_logging::ser_prompt_logging(&mut object_2, var_1)?;
        object_2.finish();
    }
    if let Some(var_3) = &input.by_user_analytics {
        #[allow(unused_mut)]
        let mut object_4 = object.key("byUserAnalytics").start_object();
        crate::protocol_serde::shape_by_user_analytics::ser_by_user_analytics(&mut object_4, var_3)?;
        object_4.finish();
    }
    if let Some(var_5) = &input.dashboard_analytics {
        #[allow(unused_mut)]
        let mut object_6 = object.key("dashboardAnalytics").start_object();
        crate::protocol_serde::shape_dashboard_analytics::ser_dashboard_analytics(&mut object_6, var_5)?;
        object_6.finish();
    }
    if let Some(var_7) = &input.notifications {
        let mut array_8 = object.key("notifications").start_array();
        for item_9 in var_7 {
            {
                #[allow(unused_mut)]
                let mut object_10 = array_8.value().start_object();
                crate::protocol_serde::shape_notifications_feature::ser_notifications_feature(&mut object_10, item_9)?;
                object_10.finish();
            }
        }
        array_8.finish();
    }
    if let Some(var_11) = &input.workspace_context {
        #[allow(unused_mut)]
        let mut object_12 = object.key("workspaceContext").start_object();
        crate::protocol_serde::shape_workspace_context::ser_workspace_context(&mut object_12, var_11)?;
        object_12.finish();
    }
    if let Some(var_13) = &input.overage_configuration {
        #[allow(unused_mut)]
        let mut object_14 = object.key("overageConfiguration").start_object();
        crate::protocol_serde::shape_overage_configuration::ser_overage_configuration(&mut object_14, var_13)?;
        object_14.finish();
    }
    if let Some(var_15) = &input.mcp_configuration {
        #[allow(unused_mut)]
        let mut object_16 = object.key("mcpConfiguration").start_object();
        crate::protocol_serde::shape_mcp_configuration::ser_mcp_configuration(&mut object_16, var_15)?;
        object_16.finish();
    }
    Ok(())
}

pub(crate) fn de_opt_in_features<'a, I>(
    tokens: &mut ::std::iter::Peekable<I>,
) -> ::std::result::Result<Option<crate::types::OptInFeatures>, ::aws_smithy_json::deserialize::error::DeserializeError>
where
    I: Iterator<
        Item = Result<
            ::aws_smithy_json::deserialize::Token<'a>,
            ::aws_smithy_json::deserialize::error::DeserializeError,
        >,
    >,
{
    match tokens.next().transpose()? {
        Some(::aws_smithy_json::deserialize::Token::ValueNull { .. }) => Ok(None),
        Some(::aws_smithy_json::deserialize::Token::StartObject { .. }) => {
            #[allow(unused_mut)]
            let mut builder = crate::types::builders::OptInFeaturesBuilder::default();
            loop {
                match tokens.next().transpose()? {
                    Some(::aws_smithy_json::deserialize::Token::EndObject { .. }) => break,
                    Some(::aws_smithy_json::deserialize::Token::ObjectKey { key, .. }) => match key
                        .to_unescaped()?
                        .as_ref()
                    {
                        "promptLogging" => {
                            builder = builder.set_prompt_logging(
                                crate::protocol_serde::shape_prompt_logging::de_prompt_logging(tokens)?,
                            );
                        },
                        "byUserAnalytics" => {
                            builder = builder.set_by_user_analytics(
                                crate::protocol_serde::shape_by_user_analytics::de_by_user_analytics(tokens)?,
                            );
                        },
                        "dashboardAnalytics" => {
                            builder = builder.set_dashboard_analytics(
                                crate::protocol_serde::shape_dashboard_analytics::de_dashboard_analytics(tokens)?,
                            );
                        },
                        "notifications" => {
                            builder = builder.set_notifications(
                                crate::protocol_serde::shape_notifications::de_notifications(tokens)?,
                            );
                        },
                        "workspaceContext" => {
                            builder = builder.set_workspace_context(
                                crate::protocol_serde::shape_workspace_context::de_workspace_context(tokens)?,
                            );
                        },
                        "overageConfiguration" => {
                            builder = builder.set_overage_configuration(
                                crate::protocol_serde::shape_overage_configuration::de_overage_configuration(tokens)?,
                            );
                        },
                        "mcpConfiguration" => {
                            builder = builder.set_mcp_configuration(
                                crate::protocol_serde::shape_mcp_configuration::de_mcp_configuration(tokens)?,
                            );
                        },
                        _ => ::aws_smithy_json::deserialize::token::skip_value(tokens)?,
                    },
                    other => {
                        return Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
                            format!("expected object key or end object, found: {:?}", other),
                        ));
                    },
                }
            }
            Ok(Some(builder.build()))
        },
        _ => Err(::aws_smithy_json::deserialize::error::DeserializeError::custom(
            "expected start object or null",
        )),
    }
}
