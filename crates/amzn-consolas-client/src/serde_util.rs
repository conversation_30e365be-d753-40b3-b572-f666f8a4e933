// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub(crate) fn validation_exception_correct_errors(
    mut builder: crate::types::error::builders::ValidationErrorBuilder,
) -> crate::types::error::builders::ValidationErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn access_denied_exception_correct_errors(
    mut builder: crate::types::error::builders::AccessDeniedErrorBuilder,
) -> crate::types::error::builders::AccessDeniedErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn internal_server_exception_correct_errors(
    mut builder: crate::types::error::builders::InternalServerErrorBuilder,
) -> crate::types::error::builders::InternalServerErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn throttling_exception_correct_errors(
    mut builder: crate::types::error::builders::ThrottlingErrorBuilder,
) -> crate::types::error::builders::ThrottlingErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn resource_not_found_exception_correct_errors(
    mut builder: crate::types::error::builders::ResourceNotFoundErrorBuilder,
) -> crate::types::error::builders::ResourceNotFoundErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn conflict_exception_correct_errors(
    mut builder: crate::types::error::builders::ConflictErrorBuilder,
) -> crate::types::error::builders::ConflictErrorBuilder {
    if builder.message.is_none() {
        builder.message = Some(Default::default())
    }
    builder
}

pub(crate) fn create_customization_output_output_correct_errors(
    mut builder: crate::operation::create_customization::builders::CreateCustomizationOutputBuilder,
) -> crate::operation::create_customization::builders::CreateCustomizationOutputBuilder {
    if builder.customization_arn.is_none() {
        builder.customization_arn = Some(Default::default())
    }
    builder
}

pub(crate) fn create_profile_output_output_correct_errors(
    mut builder: crate::operation::create_profile::builders::CreateProfileOutputBuilder,
) -> crate::operation::create_profile::builders::CreateProfileOutputBuilder {
    if builder.profile_arn.is_none() {
        builder.profile_arn = Some(Default::default())
    }
    builder
}

pub(crate) fn get_customization_output_output_correct_errors(
    mut builder: crate::operation::get_customization::builders::GetCustomizationOutputBuilder,
) -> crate::operation::get_customization::builders::GetCustomizationOutputBuilder {
    if builder.arn.is_none() {
        builder.arn = Some(Default::default())
    }
    if builder.status.is_none() {
        builder.status = "no value was set".parse::<crate::types::CustomizationStatus>().ok()
    }
    if builder.data_reference.is_none() {
        builder.data_reference = Some(crate::types::DataReference::Unknown)
    }
    if builder.customization_name.is_none() {
        builder.customization_name = Some(Default::default())
    }
    if builder.profile_arn.is_none() {
        builder.profile_arn = Some(Default::default())
    }
    if builder.updated_at.is_none() {
        builder.updated_at = Some(::aws_smithy_types::DateTime::from_fractional_secs(0, 0_f64))
    }
    builder
}

pub(crate) fn list_customization_permissions_output_output_correct_errors(
    mut builder: crate::operation::list_customization_permissions::builders::ListCustomizationPermissionsOutputBuilder,
) -> crate::operation::list_customization_permissions::builders::ListCustomizationPermissionsOutputBuilder {
    if builder.permissions.is_none() {
        builder.permissions = Some(Default::default())
    }
    builder
}

pub(crate) fn list_customization_versions_output_output_correct_errors(
    mut builder: crate::operation::list_customization_versions::builders::ListCustomizationVersionsOutputBuilder,
) -> crate::operation::list_customization_versions::builders::ListCustomizationVersionsOutputBuilder {
    if builder.versions.is_none() {
        builder.versions = Some(Default::default())
    }
    builder
}

pub(crate) fn list_customizations_output_output_correct_errors(
    mut builder: crate::operation::list_customizations::builders::ListCustomizationsOutputBuilder,
) -> crate::operation::list_customizations::builders::ListCustomizationsOutputBuilder {
    if builder.customizations.is_none() {
        builder.customizations = Some(Default::default())
    }
    builder
}

pub(crate) fn list_profiles_output_output_correct_errors(
    mut builder: crate::operation::list_profiles::builders::ListProfilesOutputBuilder,
) -> crate::operation::list_profiles::builders::ListProfilesOutputBuilder {
    if builder.profiles.is_none() {
        builder.profiles = Some(Default::default())
    }
    builder
}

pub(crate) fn lock_service_linked_role_output_output_correct_errors(
    mut builder: crate::operation::lock_service_linked_role::builders::LockServiceLinkedRoleOutputBuilder,
) -> crate::operation::lock_service_linked_role::builders::LockServiceLinkedRoleOutputBuilder {
    if builder.can_be_deleted.is_none() {
        builder.can_be_deleted = Some(Default::default())
    }
    builder
}

pub(crate) fn update_profile_output_output_correct_errors(
    mut builder: crate::operation::update_profile::builders::UpdateProfileOutputBuilder,
) -> crate::operation::update_profile::builders::UpdateProfileOutputBuilder {
    if builder.profile_arn.is_none() {
        builder.profile_arn = Some(Default::default())
    }
    builder
}

pub(crate) fn evaluation_metrics_correct_errors(
    mut builder: crate::types::builders::EvaluationMetricsBuilder,
) -> crate::types::builders::EvaluationMetricsBuilder {
    if builder.composite_score.is_none() {
        builder.composite_score = Some(Default::default())
    }
    builder
}

pub(crate) fn code_star_reference_correct_errors(
    mut builder: crate::types::builders::CodeStarReferenceBuilder,
) -> crate::types::builders::CodeStarReferenceBuilder {
    if builder.connection_arn.is_none() {
        builder.connection_arn = Some(Default::default())
    }
    builder
}

pub(crate) fn customization_summary_correct_errors(
    mut builder: crate::types::builders::CustomizationSummaryBuilder,
) -> crate::types::builders::CustomizationSummaryBuilder {
    if builder.arn.is_none() {
        builder.arn = Some(Default::default())
    }
    if builder.customization_name.is_none() {
        builder.customization_name = Some(Default::default())
    }
    if builder.status.is_none() {
        builder.status = "no value was set".parse::<crate::types::CustomizationStatus>().ok()
    }
    if builder.updated_at.is_none() {
        builder.updated_at = Some(::aws_smithy_types::DateTime::from_fractional_secs(0, 0_f64))
    }
    builder
}

pub(crate) fn customization_version_summary_correct_errors(
    mut builder: crate::types::builders::CustomizationVersionSummaryBuilder,
) -> crate::types::builders::CustomizationVersionSummaryBuilder {
    if builder.version.is_none() {
        builder.version = Some(Default::default())
    }
    if builder.status.is_none() {
        builder.status = "no value was set".parse::<crate::types::CustomizationStatus>().ok()
    }
    if builder.data_reference.is_none() {
        builder.data_reference = Some(crate::types::DataReference::Unknown)
    }
    if builder.updated_at.is_none() {
        builder.updated_at = Some(::aws_smithy_types::DateTime::from_fractional_secs(0, 0_f64))
    }
    builder
}

pub(crate) fn profile_correct_errors(
    mut builder: crate::types::builders::ProfileBuilder,
) -> crate::types::builders::ProfileBuilder {
    if builder.arn.is_none() {
        builder.arn = Some(Default::default())
    }
    if builder.profile_name.is_none() {
        builder.profile_name = Some(Default::default())
    }
    builder
}

pub(crate) fn recommendation_correct_errors(
    mut builder: crate::types::builders::RecommendationBuilder,
) -> crate::types::builders::RecommendationBuilder {
    if builder.content.is_none() {
        builder.content = Some(Default::default())
    }
    builder
}

pub(crate) fn s3_reference_correct_errors(
    mut builder: crate::types::builders::S3ReferenceBuilder,
) -> crate::types::builders::S3ReferenceBuilder {
    if builder.uri.is_none() {
        builder.uri = Some(Default::default())
    }
    builder
}

pub(crate) fn tag_correct_errors(
    mut builder: crate::types::builders::TagBuilder,
) -> crate::types::builders::TagBuilder {
    if builder.key.is_none() {
        builder.key = Some(Default::default())
    }
    if builder.value.is_none() {
        builder.value = Some(Default::default())
    }
    builder
}

pub(crate) fn reference_tracker_configuration_correct_errors(
    mut builder: crate::types::builders::ReferenceTrackerConfigurationBuilder,
) -> crate::types::builders::ReferenceTrackerConfigurationBuilder {
    if builder.recommendations_with_references.is_none() {
        builder.recommendations_with_references = "no value was set"
            .parse::<crate::types::RecommendationsWithReferencesPreference>()
            .ok()
    }
    builder
}

pub(crate) fn resource_policy_correct_errors(
    mut builder: crate::types::builders::ResourcePolicyBuilder,
) -> crate::types::builders::ResourcePolicyBuilder {
    if builder.effect.is_none() {
        builder.effect = "no value was set".parse::<crate::types::ResourcePolicyEffect>().ok()
    }
    builder
}

pub(crate) fn application_properties_correct_errors(
    mut builder: crate::types::builders::ApplicationPropertiesBuilder,
) -> crate::types::builders::ApplicationPropertiesBuilder {
    if builder.tenant_id.is_none() {
        builder.tenant_id = Some(Default::default())
    }
    if builder.application_arn.is_none() {
        builder.application_arn = Some(Default::default())
    }
    if builder.tenant_url.is_none() {
        builder.tenant_url = Some(Default::default())
    }
    if builder.application_type.is_none() {
        builder.application_type = "no value was set".parse::<crate::types::FunctionalityName>().ok()
    }
    builder
}

pub(crate) fn by_user_analytics_correct_errors(
    mut builder: crate::types::builders::ByUserAnalyticsBuilder,
) -> crate::types::builders::ByUserAnalyticsBuilder {
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn dashboard_analytics_correct_errors(
    mut builder: crate::types::builders::DashboardAnalyticsBuilder,
) -> crate::types::builders::DashboardAnalyticsBuilder {
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn mcp_configuration_correct_errors(
    mut builder: crate::types::builders::McpConfigurationBuilder,
) -> crate::types::builders::McpConfigurationBuilder {
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn overage_configuration_correct_errors(
    mut builder: crate::types::builders::OverageConfigurationBuilder,
) -> crate::types::builders::OverageConfigurationBuilder {
    if builder.overage_status.is_none() {
        builder.overage_status = "no value was set".parse::<crate::types::OverageStatus>().ok()
    }
    builder
}

pub(crate) fn prompt_logging_correct_errors(
    mut builder: crate::types::builders::PromptLoggingBuilder,
) -> crate::types::builders::PromptLoggingBuilder {
    if builder.s3_uri.is_none() {
        builder.s3_uri = Some(Default::default())
    }
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn sso_identity_details_correct_errors(
    mut builder: crate::types::builders::SsoIdentityDetailsBuilder,
) -> crate::types::builders::SsoIdentityDetailsBuilder {
    if builder.instance_arn.is_none() {
        builder.instance_arn = Some(Default::default())
    }
    if builder.oidc_client_id.is_none() {
        builder.oidc_client_id = Some(Default::default())
    }
    builder
}

pub(crate) fn workspace_context_correct_errors(
    mut builder: crate::types::builders::WorkspaceContextBuilder,
) -> crate::types::builders::WorkspaceContextBuilder {
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}

pub(crate) fn notifications_feature_correct_errors(
    mut builder: crate::types::builders::NotificationsFeatureBuilder,
) -> crate::types::builders::NotificationsFeatureBuilder {
    if builder.feature.is_none() {
        builder.feature = Some(Default::default())
    }
    if builder.toggle.is_none() {
        builder.toggle = "no value was set".parse::<crate::types::OptInFeatureToggle>().ok()
    }
    builder
}
