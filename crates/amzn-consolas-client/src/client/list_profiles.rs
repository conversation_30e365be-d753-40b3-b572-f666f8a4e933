// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
impl super::Client {
    /// Constructs a fluent builder for the
    /// [`ListProfiles`](crate::operation::list_profiles::builders::ListProfilesFluentBuilder)
    /// operation. This operation supports pagination; See
    /// [`into_paginator()`](crate::operation::list_profiles::builders::ListProfilesFluentBuilder::into_paginator).
    ///
    ///
    /// - The fluent builder is configurable:
    ///   - [`max_results(i32)`](crate::operation::list_profiles::builders::ListProfilesFluentBuilder::max_results) / [`set_max_results(Option<i32>)`](crate::operation::list_profiles::builders::ListProfilesFluentBuilder::set_max_results):<br>required: **false**<br>(undocumented)<br>
    ///   - [`include_management_account(bool)`](crate::operation::list_profiles::builders::ListProfilesFluentBuilder::include_management_account) / [`set_include_management_account(Option<bool>)`](crate::operation::list_profiles::builders::ListProfilesFluentBuilder::set_include_management_account):<br>required: **false**<br>(undocumented)<br>
    ///   - [`next_token(impl Into<String>)`](crate::operation::list_profiles::builders::ListProfilesFluentBuilder::next_token) / [`set_next_token(Option<String>)`](crate::operation::list_profiles::builders::ListProfilesFluentBuilder::set_next_token):<br>required: **false**<br>(undocumented)<br>
    /// - On success, responds with
    ///   [`ListProfilesOutput`](crate::operation::list_profiles::ListProfilesOutput) with field(s):
    ///   - [`profiles(Vec::<Profile>)`](crate::operation::list_profiles::ListProfilesOutput::profiles): (undocumented)
    ///   - [`next_token(Option<String>)`](crate::operation::list_profiles::ListProfilesOutput::next_token): (undocumented)
    /// - On failure, responds with
    ///   [`SdkError<ListProfilesError>`](crate::operation::list_profiles::ListProfilesError)
    pub fn list_profiles(&self) -> crate::operation::list_profiles::builders::ListProfilesFluentBuilder {
        crate::operation::list_profiles::builders::ListProfilesFluentBuilder::new(self.handle.clone())
    }
}
