// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Structure to represent a chat input message from User.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq)]
pub struct UserInputMessage {
    /// The content of the chat message.
    pub content: ::std::string::String,
    /// Chat message context associated with the Chat Message.
    pub user_input_message_context: ::std::option::Option<crate::types::UserInputMessageContext>,
    /// User Intent.
    pub user_intent: ::std::option::Option<crate::types::UserIntent>,
    /// User Input Origin.
    pub origin: ::std::option::Option<crate::types::Origin>,
    /// Images associated with the Chat Message.
    pub images: ::std::option::Option<::std::vec::Vec<crate::types::ImageBlock>>,
    /// Unique identifier for the model used in this conversation
    pub model_id: ::std::option::Option<::std::string::String>,
    /// Indicates whether to add a cache point after the current message
    pub cache_point: ::std::option::Option<crate::types::CachePoint>,
    /// Client cache config
    pub client_cache_config: ::std::option::Option<crate::types::ClientCacheConfig>,
}
impl UserInputMessage {
    /// The content of the chat message.
    pub fn content(&self) -> &str {
        use std::ops::Deref;
        self.content.deref()
    }

    /// Chat message context associated with the Chat Message.
    pub fn user_input_message_context(&self) -> ::std::option::Option<&crate::types::UserInputMessageContext> {
        self.user_input_message_context.as_ref()
    }

    /// User Intent.
    pub fn user_intent(&self) -> ::std::option::Option<&crate::types::UserIntent> {
        self.user_intent.as_ref()
    }

    /// User Input Origin.
    pub fn origin(&self) -> ::std::option::Option<&crate::types::Origin> {
        self.origin.as_ref()
    }

    /// Images associated with the Chat Message.
    ///
    /// If no value was sent for this field, a default will be set. If you want to determine if no
    /// value was sent, use `.images.is_none()`.
    pub fn images(&self) -> &[crate::types::ImageBlock] {
        self.images.as_deref().unwrap_or_default()
    }

    /// Unique identifier for the model used in this conversation
    pub fn model_id(&self) -> ::std::option::Option<&str> {
        self.model_id.as_deref()
    }

    /// Indicates whether to add a cache point after the current message
    pub fn cache_point(&self) -> ::std::option::Option<&crate::types::CachePoint> {
        self.cache_point.as_ref()
    }

    /// Client cache config
    pub fn client_cache_config(&self) -> ::std::option::Option<&crate::types::ClientCacheConfig> {
        self.client_cache_config.as_ref()
    }
}
impl ::std::fmt::Debug for UserInputMessage {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("UserInputMessage");
        formatter.field("content", &"*** Sensitive Data Redacted ***");
        formatter.field("user_input_message_context", &self.user_input_message_context);
        formatter.field("user_intent", &self.user_intent);
        formatter.field("origin", &self.origin);
        formatter.field("images", &self.images);
        formatter.field("model_id", &self.model_id);
        formatter.field("cache_point", &self.cache_point);
        formatter.field("client_cache_config", &self.client_cache_config);
        formatter.finish()
    }
}
impl UserInputMessage {
    /// Creates a new builder-style object to manufacture
    /// [`UserInputMessage`](crate::types::UserInputMessage).
    pub fn builder() -> crate::types::builders::UserInputMessageBuilder {
        crate::types::builders::UserInputMessageBuilder::default()
    }
}

/// A builder for [`UserInputMessage`](crate::types::UserInputMessage).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default)]
#[non_exhaustive]
pub struct UserInputMessageBuilder {
    pub(crate) content: ::std::option::Option<::std::string::String>,
    pub(crate) user_input_message_context: ::std::option::Option<crate::types::UserInputMessageContext>,
    pub(crate) user_intent: ::std::option::Option<crate::types::UserIntent>,
    pub(crate) origin: ::std::option::Option<crate::types::Origin>,
    pub(crate) images: ::std::option::Option<::std::vec::Vec<crate::types::ImageBlock>>,
    pub(crate) model_id: ::std::option::Option<::std::string::String>,
    pub(crate) cache_point: ::std::option::Option<crate::types::CachePoint>,
    pub(crate) client_cache_config: ::std::option::Option<crate::types::ClientCacheConfig>,
}
impl UserInputMessageBuilder {
    /// The content of the chat message.
    /// This field is required.
    pub fn content(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.content = ::std::option::Option::Some(input.into());
        self
    }

    /// The content of the chat message.
    pub fn set_content(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.content = input;
        self
    }

    /// The content of the chat message.
    pub fn get_content(&self) -> &::std::option::Option<::std::string::String> {
        &self.content
    }

    /// Chat message context associated with the Chat Message.
    pub fn user_input_message_context(mut self, input: crate::types::UserInputMessageContext) -> Self {
        self.user_input_message_context = ::std::option::Option::Some(input);
        self
    }

    /// Chat message context associated with the Chat Message.
    pub fn set_user_input_message_context(
        mut self,
        input: ::std::option::Option<crate::types::UserInputMessageContext>,
    ) -> Self {
        self.user_input_message_context = input;
        self
    }

    /// Chat message context associated with the Chat Message.
    pub fn get_user_input_message_context(&self) -> &::std::option::Option<crate::types::UserInputMessageContext> {
        &self.user_input_message_context
    }

    /// User Intent.
    pub fn user_intent(mut self, input: crate::types::UserIntent) -> Self {
        self.user_intent = ::std::option::Option::Some(input);
        self
    }

    /// User Intent.
    pub fn set_user_intent(mut self, input: ::std::option::Option<crate::types::UserIntent>) -> Self {
        self.user_intent = input;
        self
    }

    /// User Intent.
    pub fn get_user_intent(&self) -> &::std::option::Option<crate::types::UserIntent> {
        &self.user_intent
    }

    /// User Input Origin.
    pub fn origin(mut self, input: crate::types::Origin) -> Self {
        self.origin = ::std::option::Option::Some(input);
        self
    }

    /// User Input Origin.
    pub fn set_origin(mut self, input: ::std::option::Option<crate::types::Origin>) -> Self {
        self.origin = input;
        self
    }

    /// User Input Origin.
    pub fn get_origin(&self) -> &::std::option::Option<crate::types::Origin> {
        &self.origin
    }

    /// Appends an item to `images`.
    ///
    /// To override the contents of this collection use [`set_images`](Self::set_images).
    ///
    /// Images associated with the Chat Message.
    pub fn images(mut self, input: crate::types::ImageBlock) -> Self {
        let mut v = self.images.unwrap_or_default();
        v.push(input);
        self.images = ::std::option::Option::Some(v);
        self
    }

    /// Images associated with the Chat Message.
    pub fn set_images(mut self, input: ::std::option::Option<::std::vec::Vec<crate::types::ImageBlock>>) -> Self {
        self.images = input;
        self
    }

    /// Images associated with the Chat Message.
    pub fn get_images(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::ImageBlock>> {
        &self.images
    }

    /// Unique identifier for the model used in this conversation
    pub fn model_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.model_id = ::std::option::Option::Some(input.into());
        self
    }

    /// Unique identifier for the model used in this conversation
    pub fn set_model_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.model_id = input;
        self
    }

    /// Unique identifier for the model used in this conversation
    pub fn get_model_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.model_id
    }

    /// Indicates whether to add a cache point after the current message
    pub fn cache_point(mut self, input: crate::types::CachePoint) -> Self {
        self.cache_point = ::std::option::Option::Some(input);
        self
    }

    /// Indicates whether to add a cache point after the current message
    pub fn set_cache_point(mut self, input: ::std::option::Option<crate::types::CachePoint>) -> Self {
        self.cache_point = input;
        self
    }

    /// Indicates whether to add a cache point after the current message
    pub fn get_cache_point(&self) -> &::std::option::Option<crate::types::CachePoint> {
        &self.cache_point
    }

    /// Client cache config
    pub fn client_cache_config(mut self, input: crate::types::ClientCacheConfig) -> Self {
        self.client_cache_config = ::std::option::Option::Some(input);
        self
    }

    /// Client cache config
    pub fn set_client_cache_config(mut self, input: ::std::option::Option<crate::types::ClientCacheConfig>) -> Self {
        self.client_cache_config = input;
        self
    }

    /// Client cache config
    pub fn get_client_cache_config(&self) -> &::std::option::Option<crate::types::ClientCacheConfig> {
        &self.client_cache_config
    }

    /// Consumes the builder and constructs a [`UserInputMessage`](crate::types::UserInputMessage).
    /// This method will fail if any of the following fields are not set:
    /// - [`content`](crate::types::builders::UserInputMessageBuilder::content)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::UserInputMessage, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::UserInputMessage {
            content: self.content.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "content",
                    "content was not specified but it is required when building UserInputMessage",
                )
            })?,
            user_input_message_context: self.user_input_message_context,
            user_intent: self.user_intent,
            origin: self.origin,
            images: self.images,
            model_id: self.model_id,
            cache_point: self.cache_point,
            client_cache_config: self.client_cache_config,
        })
    }
}
impl ::std::fmt::Debug for UserInputMessageBuilder {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        let mut formatter = f.debug_struct("UserInputMessageBuilder");
        formatter.field("content", &"*** Sensitive Data Redacted ***");
        formatter.field("user_input_message_context", &self.user_input_message_context);
        formatter.field("user_intent", &self.user_intent);
        formatter.field("origin", &self.origin);
        formatter.field("images", &self.images);
        formatter.field("model_id", &self.model_id);
        formatter.field("cache_point", &self.cache_point);
        formatter.field("client_cache_config", &self.client_cache_config);
        formatter.finish()
    }
}
