// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Structure to represent the current state of a chat conversation.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct ConversationState {
    /// Unique identifier for the chat conversation stream
    pub conversation_id: ::std::option::Option<::std::string::String>,
    /// Unique identifier for remote workspace
    pub workspace_id: ::std::option::Option<::std::string::String>,
    /// Holds the history of chat messages.
    pub history: ::std::option::Option<::std::vec::Vec<crate::types::ChatMessage>>,
    /// Holds the current message being processed or displayed.
    pub current_message: crate::types::ChatMessage,
    /// Trigger Reason for Chat
    pub chat_trigger_type: crate::types::ChatTriggerType,
    #[allow(missing_docs)] // documentation missing in model
    pub customization_arn: ::std::option::Option<::std::string::String>,
}
impl ConversationState {
    /// Unique identifier for the chat conversation stream
    pub fn conversation_id(&self) -> ::std::option::Option<&str> {
        self.conversation_id.as_deref()
    }

    /// Unique identifier for remote workspace
    pub fn workspace_id(&self) -> ::std::option::Option<&str> {
        self.workspace_id.as_deref()
    }

    /// Holds the history of chat messages.
    ///
    /// If no value was sent for this field, a default will be set. If you want to determine if no
    /// value was sent, use `.history.is_none()`.
    pub fn history(&self) -> &[crate::types::ChatMessage] {
        self.history.as_deref().unwrap_or_default()
    }

    /// Holds the current message being processed or displayed.
    pub fn current_message(&self) -> &crate::types::ChatMessage {
        &self.current_message
    }

    /// Trigger Reason for Chat
    pub fn chat_trigger_type(&self) -> &crate::types::ChatTriggerType {
        &self.chat_trigger_type
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn customization_arn(&self) -> ::std::option::Option<&str> {
        self.customization_arn.as_deref()
    }
}
impl ConversationState {
    /// Creates a new builder-style object to manufacture
    /// [`ConversationState`](crate::types::ConversationState).
    pub fn builder() -> crate::types::builders::ConversationStateBuilder {
        crate::types::builders::ConversationStateBuilder::default()
    }
}

/// A builder for [`ConversationState`](crate::types::ConversationState).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct ConversationStateBuilder {
    pub(crate) conversation_id: ::std::option::Option<::std::string::String>,
    pub(crate) workspace_id: ::std::option::Option<::std::string::String>,
    pub(crate) history: ::std::option::Option<::std::vec::Vec<crate::types::ChatMessage>>,
    pub(crate) current_message: ::std::option::Option<crate::types::ChatMessage>,
    pub(crate) chat_trigger_type: ::std::option::Option<crate::types::ChatTriggerType>,
    pub(crate) customization_arn: ::std::option::Option<::std::string::String>,
}
impl ConversationStateBuilder {
    /// Unique identifier for the chat conversation stream
    pub fn conversation_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.conversation_id = ::std::option::Option::Some(input.into());
        self
    }

    /// Unique identifier for the chat conversation stream
    pub fn set_conversation_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.conversation_id = input;
        self
    }

    /// Unique identifier for the chat conversation stream
    pub fn get_conversation_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.conversation_id
    }

    /// Unique identifier for remote workspace
    pub fn workspace_id(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.workspace_id = ::std::option::Option::Some(input.into());
        self
    }

    /// Unique identifier for remote workspace
    pub fn set_workspace_id(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.workspace_id = input;
        self
    }

    /// Unique identifier for remote workspace
    pub fn get_workspace_id(&self) -> &::std::option::Option<::std::string::String> {
        &self.workspace_id
    }

    /// Appends an item to `history`.
    ///
    /// To override the contents of this collection use [`set_history`](Self::set_history).
    ///
    /// Holds the history of chat messages.
    pub fn history(mut self, input: crate::types::ChatMessage) -> Self {
        let mut v = self.history.unwrap_or_default();
        v.push(input);
        self.history = ::std::option::Option::Some(v);
        self
    }

    /// Holds the history of chat messages.
    pub fn set_history(mut self, input: ::std::option::Option<::std::vec::Vec<crate::types::ChatMessage>>) -> Self {
        self.history = input;
        self
    }

    /// Holds the history of chat messages.
    pub fn get_history(&self) -> &::std::option::Option<::std::vec::Vec<crate::types::ChatMessage>> {
        &self.history
    }

    /// Holds the current message being processed or displayed.
    /// This field is required.
    pub fn current_message(mut self, input: crate::types::ChatMessage) -> Self {
        self.current_message = ::std::option::Option::Some(input);
        self
    }

    /// Holds the current message being processed or displayed.
    pub fn set_current_message(mut self, input: ::std::option::Option<crate::types::ChatMessage>) -> Self {
        self.current_message = input;
        self
    }

    /// Holds the current message being processed or displayed.
    pub fn get_current_message(&self) -> &::std::option::Option<crate::types::ChatMessage> {
        &self.current_message
    }

    /// Trigger Reason for Chat
    /// This field is required.
    pub fn chat_trigger_type(mut self, input: crate::types::ChatTriggerType) -> Self {
        self.chat_trigger_type = ::std::option::Option::Some(input);
        self
    }

    /// Trigger Reason for Chat
    pub fn set_chat_trigger_type(mut self, input: ::std::option::Option<crate::types::ChatTriggerType>) -> Self {
        self.chat_trigger_type = input;
        self
    }

    /// Trigger Reason for Chat
    pub fn get_chat_trigger_type(&self) -> &::std::option::Option<crate::types::ChatTriggerType> {
        &self.chat_trigger_type
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn customization_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.customization_arn = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_customization_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.customization_arn = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_customization_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.customization_arn
    }

    /// Consumes the builder and constructs a
    /// [`ConversationState`](crate::types::ConversationState). This method will fail if any of
    /// the following fields are not set:
    /// - [`current_message`](crate::types::builders::ConversationStateBuilder::current_message)
    /// - [`chat_trigger_type`](crate::types::builders::ConversationStateBuilder::chat_trigger_type)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::ConversationState, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::ConversationState {
            conversation_id: self.conversation_id,
            workspace_id: self.workspace_id,
            history: self.history,
            current_message: self.current_message.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "current_message",
                    "current_message was not specified but it is required when building ConversationState",
                )
            })?,
            chat_trigger_type: self.chat_trigger_type.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "chat_trigger_type",
                    "chat_trigger_type was not specified but it is required when building ConversationState",
                )
            })?,
            customization_arn: self.customization_arn,
        })
    }
}
