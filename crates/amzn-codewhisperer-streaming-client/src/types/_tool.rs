// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Information about a tool that can be used.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub enum Tool {
    #[allow(missing_docs)] // documentation missing in model
    CachePoint(crate::types::CachePoint),
    /// The specification for the tool.
    ToolSpecification(crate::types::ToolSpecification),
    /// The `Unknown` variant represents cases where new union variant was received. Consider
    /// upgrading the SDK to the latest available version. An unknown enum variant
    ///
    /// _Note: If you encounter this error, consider upgrading your SDK to the latest version._
    /// The `Unknown` variant represents cases where the server sent a value that wasn't recognized
    /// by the client. This can happen when the server adds new functionality, but the client has
    /// not been updated. To investigate this, consider turning on debug logging to print the
    /// raw HTTP response.
    #[non_exhaustive]
    Unknown,
}
impl Tool {
    /// Tries to convert the enum instance into [`CachePoint`](crate::types::Tool::CachePoint),
    /// extracting the inner [`CachePoint`](crate::types::CachePoint). Returns `Err(&Self)` if
    /// it can't be converted.
    pub fn as_cache_point(&self) -> ::std::result::Result<&crate::types::CachePoint, &Self> {
        if let Tool::CachePoint(val) = &self {
            ::std::result::Result::Ok(val)
        } else {
            ::std::result::Result::Err(self)
        }
    }

    /// Returns true if this is a [`CachePoint`](crate::types::Tool::CachePoint).
    pub fn is_cache_point(&self) -> bool {
        self.as_cache_point().is_ok()
    }

    /// Tries to convert the enum instance into
    /// [`ToolSpecification`](crate::types::Tool::ToolSpecification), extracting the inner
    /// [`ToolSpecification`](crate::types::ToolSpecification). Returns `Err(&Self)` if it can't
    /// be converted.
    pub fn as_tool_specification(&self) -> ::std::result::Result<&crate::types::ToolSpecification, &Self> {
        if let Tool::ToolSpecification(val) = &self {
            ::std::result::Result::Ok(val)
        } else {
            ::std::result::Result::Err(self)
        }
    }

    /// Returns true if this is a [`ToolSpecification`](crate::types::Tool::ToolSpecification).
    pub fn is_tool_specification(&self) -> bool {
        self.as_tool_specification().is_ok()
    }

    /// Returns true if the enum instance is the `Unknown` variant.
    pub fn is_unknown(&self) -> bool {
        matches!(self, Self::Unknown)
    }
}
