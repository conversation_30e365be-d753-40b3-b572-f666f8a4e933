// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct CachePoint {
    #[allow(missing_docs)] // documentation missing in model
    pub r#type: crate::types::CachePointType,
}
impl CachePoint {
    #[allow(missing_docs)] // documentation missing in model
    pub fn r#type(&self) -> &crate::types::CachePointType {
        &self.r#type
    }
}
impl CachePoint {
    /// Creates a new builder-style object to manufacture [`CachePoint`](crate::types::CachePoint).
    pub fn builder() -> crate::types::builders::CachePointBuilder {
        crate::types::builders::CachePointBuilder::default()
    }
}

/// A builder for [`CachePoint`](crate::types::CachePoint).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct CachePointBuilder {
    pub(crate) r#type: ::std::option::Option<crate::types::CachePointType>,
}
impl CachePointBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn r#type(mut self, input: crate::types::CachePointType) -> Self {
        self.r#type = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_type(mut self, input: ::std::option::Option<crate::types::CachePointType>) -> Self {
        self.r#type = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_type(&self) -> &::std::option::Option<crate::types::CachePointType> {
        &self.r#type
    }

    /// Consumes the builder and constructs a [`CachePoint`](crate::types::CachePoint).
    /// This method will fail if any of the following fields are not set:
    /// - [`r#type`](crate::types::builders::CachePointBuilder::type)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::CachePoint, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::CachePoint {
            r#type: self.r#type.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "r#type",
                    "r#type was not specified but it is required when building CachePoint",
                )
            })?,
        })
    }
}
