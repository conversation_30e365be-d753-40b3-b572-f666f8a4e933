// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
#[allow(missing_docs)] // documentation missing in model
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct TokenUsage {
    #[allow(missing_docs)] // documentation missing in model
    pub uncached_input_tokens: i32,
    #[allow(missing_docs)] // documentation missing in model
    pub output_tokens: i32,
    #[allow(missing_docs)] // documentation missing in model
    pub total_tokens: i32,
    #[allow(missing_docs)] // documentation missing in model
    pub cache_read_input_tokens: ::std::option::Option<i32>,
    #[allow(missing_docs)] // documentation missing in model
    pub cache_write_input_tokens: ::std::option::Option<i32>,
}
impl TokenUsage {
    #[allow(missing_docs)] // documentation missing in model
    pub fn uncached_input_tokens(&self) -> i32 {
        self.uncached_input_tokens
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn output_tokens(&self) -> i32 {
        self.output_tokens
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn total_tokens(&self) -> i32 {
        self.total_tokens
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn cache_read_input_tokens(&self) -> ::std::option::Option<i32> {
        self.cache_read_input_tokens
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn cache_write_input_tokens(&self) -> ::std::option::Option<i32> {
        self.cache_write_input_tokens
    }
}
impl TokenUsage {
    /// Creates a new builder-style object to manufacture [`TokenUsage`](crate::types::TokenUsage).
    pub fn builder() -> crate::types::builders::TokenUsageBuilder {
        crate::types::builders::TokenUsageBuilder::default()
    }
}

/// A builder for [`TokenUsage`](crate::types::TokenUsage).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct TokenUsageBuilder {
    pub(crate) uncached_input_tokens: ::std::option::Option<i32>,
    pub(crate) output_tokens: ::std::option::Option<i32>,
    pub(crate) total_tokens: ::std::option::Option<i32>,
    pub(crate) cache_read_input_tokens: ::std::option::Option<i32>,
    pub(crate) cache_write_input_tokens: ::std::option::Option<i32>,
}
impl TokenUsageBuilder {
    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn uncached_input_tokens(mut self, input: i32) -> Self {
        self.uncached_input_tokens = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_uncached_input_tokens(mut self, input: ::std::option::Option<i32>) -> Self {
        self.uncached_input_tokens = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_uncached_input_tokens(&self) -> &::std::option::Option<i32> {
        &self.uncached_input_tokens
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn output_tokens(mut self, input: i32) -> Self {
        self.output_tokens = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_output_tokens(mut self, input: ::std::option::Option<i32>) -> Self {
        self.output_tokens = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_output_tokens(&self) -> &::std::option::Option<i32> {
        &self.output_tokens
    }

    #[allow(missing_docs)] // documentation missing in model
    /// This field is required.
    pub fn total_tokens(mut self, input: i32) -> Self {
        self.total_tokens = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_total_tokens(mut self, input: ::std::option::Option<i32>) -> Self {
        self.total_tokens = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_total_tokens(&self) -> &::std::option::Option<i32> {
        &self.total_tokens
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn cache_read_input_tokens(mut self, input: i32) -> Self {
        self.cache_read_input_tokens = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_cache_read_input_tokens(mut self, input: ::std::option::Option<i32>) -> Self {
        self.cache_read_input_tokens = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_cache_read_input_tokens(&self) -> &::std::option::Option<i32> {
        &self.cache_read_input_tokens
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn cache_write_input_tokens(mut self, input: i32) -> Self {
        self.cache_write_input_tokens = ::std::option::Option::Some(input);
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_cache_write_input_tokens(mut self, input: ::std::option::Option<i32>) -> Self {
        self.cache_write_input_tokens = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_cache_write_input_tokens(&self) -> &::std::option::Option<i32> {
        &self.cache_write_input_tokens
    }

    /// Consumes the builder and constructs a [`TokenUsage`](crate::types::TokenUsage).
    /// This method will fail if any of the following fields are not set:
    /// - [`uncached_input_tokens`](crate::types::builders::TokenUsageBuilder::uncached_input_tokens)
    /// - [`output_tokens`](crate::types::builders::TokenUsageBuilder::output_tokens)
    /// - [`total_tokens`](crate::types::builders::TokenUsageBuilder::total_tokens)
    pub fn build(
        self,
    ) -> ::std::result::Result<crate::types::TokenUsage, ::aws_smithy_types::error::operation::BuildError> {
        ::std::result::Result::Ok(crate::types::TokenUsage {
            uncached_input_tokens: self.uncached_input_tokens.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "uncached_input_tokens",
                    "uncached_input_tokens was not specified but it is required when building TokenUsage",
                )
            })?,
            output_tokens: self.output_tokens.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "output_tokens",
                    "output_tokens was not specified but it is required when building TokenUsage",
                )
            })?,
            total_tokens: self.total_tokens.ok_or_else(|| {
                ::aws_smithy_types::error::operation::BuildError::missing_field(
                    "total_tokens",
                    "total_tokens was not specified but it is required when building TokenUsage",
                )
            })?,
            cache_read_input_tokens: self.cache_read_input_tokens,
            cache_write_input_tokens: self.cache_write_input_tokens,
        })
    }
}
