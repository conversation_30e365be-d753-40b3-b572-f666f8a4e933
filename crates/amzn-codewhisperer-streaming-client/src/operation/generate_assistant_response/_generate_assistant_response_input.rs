// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.

/// Structure to represent a new generate assistant response request.
#[non_exhaustive]
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::fmt::Debug)]
pub struct GenerateAssistantResponseInput {
    /// Structure to represent the current state of a chat conversation.
    pub conversation_state: ::std::option::Option<crate::types::ConversationState>,
    #[allow(missing_docs)] // documentation missing in model
    pub profile_arn: ::std::option::Option<::std::string::String>,
    #[allow(missing_docs)] // documentation missing in model
    pub agent_mode: ::std::option::Option<::std::string::String>,
}
impl GenerateAssistantResponseInput {
    /// Structure to represent the current state of a chat conversation.
    pub fn conversation_state(&self) -> ::std::option::Option<&crate::types::ConversationState> {
        self.conversation_state.as_ref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(&self) -> ::std::option::Option<&str> {
        self.profile_arn.as_deref()
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn agent_mode(&self) -> ::std::option::Option<&str> {
        self.agent_mode.as_deref()
    }
}
impl GenerateAssistantResponseInput {
    /// Creates a new builder-style object to manufacture
    /// [`GenerateAssistantResponseInput`](crate::operation::generate_assistant_response::GenerateAssistantResponseInput).
    pub fn builder() -> crate::operation::generate_assistant_response::builders::GenerateAssistantResponseInputBuilder {
        crate::operation::generate_assistant_response::builders::GenerateAssistantResponseInputBuilder::default()
    }
}

/// A builder for
/// [`GenerateAssistantResponseInput`](crate::operation::generate_assistant_response::GenerateAssistantResponseInput).
#[derive(::std::clone::Clone, ::std::cmp::PartialEq, ::std::default::Default, ::std::fmt::Debug)]
#[non_exhaustive]
pub struct GenerateAssistantResponseInputBuilder {
    pub(crate) conversation_state: ::std::option::Option<crate::types::ConversationState>,
    pub(crate) profile_arn: ::std::option::Option<::std::string::String>,
    pub(crate) agent_mode: ::std::option::Option<::std::string::String>,
}
impl GenerateAssistantResponseInputBuilder {
    /// Structure to represent the current state of a chat conversation.
    /// This field is required.
    pub fn conversation_state(mut self, input: crate::types::ConversationState) -> Self {
        self.conversation_state = ::std::option::Option::Some(input);
        self
    }

    /// Structure to represent the current state of a chat conversation.
    pub fn set_conversation_state(mut self, input: ::std::option::Option<crate::types::ConversationState>) -> Self {
        self.conversation_state = input;
        self
    }

    /// Structure to represent the current state of a chat conversation.
    pub fn get_conversation_state(&self) -> &::std::option::Option<crate::types::ConversationState> {
        &self.conversation_state
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn profile_arn(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.profile_arn = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_profile_arn(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.profile_arn = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_profile_arn(&self) -> &::std::option::Option<::std::string::String> {
        &self.profile_arn
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn agent_mode(mut self, input: impl ::std::convert::Into<::std::string::String>) -> Self {
        self.agent_mode = ::std::option::Option::Some(input.into());
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn set_agent_mode(mut self, input: ::std::option::Option<::std::string::String>) -> Self {
        self.agent_mode = input;
        self
    }

    #[allow(missing_docs)] // documentation missing in model
    pub fn get_agent_mode(&self) -> &::std::option::Option<::std::string::String> {
        &self.agent_mode
    }

    /// Consumes the builder and constructs a
    /// [`GenerateAssistantResponseInput`](crate::operation::generate_assistant_response::GenerateAssistantResponseInput).
    pub fn build(
        self,
    ) -> ::std::result::Result<
        crate::operation::generate_assistant_response::GenerateAssistantResponseInput,
        ::aws_smithy_types::error::operation::BuildError,
    > {
        ::std::result::Result::Ok(
            crate::operation::generate_assistant_response::GenerateAssistantResponseInput {
                conversation_state: self.conversation_state,
                profile_arn: self.profile_arn,
                agent_mode: self.agent_mode,
            },
        )
    }
}
