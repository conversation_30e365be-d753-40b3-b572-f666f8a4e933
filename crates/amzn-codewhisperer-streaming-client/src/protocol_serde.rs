// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub(crate) fn type_erase_result<O, E>(
    result: ::std::result::Result<O, E>,
) -> ::std::result::Result<
    ::aws_smithy_runtime_api::client::interceptors::context::Output,
    ::aws_smithy_runtime_api::client::orchestrator::OrchestratorError<
        ::aws_smithy_runtime_api::client::interceptors::context::Error,
    >,
>
where
    O: ::std::fmt::Debug + ::std::marker::Send + ::std::marker::Sync + 'static,
    E: ::std::error::Error + std::fmt::Debug + ::std::marker::Send + ::std::marker::Sync + 'static,
{
    result
        .map(|output| ::aws_smithy_runtime_api::client::interceptors::context::Output::erase(output))
        .map_err(|error| ::aws_smithy_runtime_api::client::interceptors::context::Error::erase(error))
        .map_err(::std::convert::Into::into)
}

pub(crate) mod shape_export_result_archive;

pub fn parse_http_error_metadata(
    _response_status: u16,
    response_headers: &::aws_smithy_runtime_api::http::Headers,
    response_body: &[u8],
) -> ::std::result::Result<
    ::aws_smithy_types::error::metadata::Builder,
    ::aws_smithy_json::deserialize::error::DeserializeError,
> {
    crate::json_errors::parse_error_metadata(response_body, response_headers)
}

pub(crate) mod shape_generate_assistant_response;

pub(crate) mod shape_generate_task_assist_plan;

pub(crate) mod shape_send_message;

pub(crate) fn or_empty_doc(data: &[u8]) -> &[u8] {
    if data.is_empty() { b"{}" } else { data }
}

pub(crate) mod shape_access_denied_error;

pub(crate) mod shape_conflict_exception;

pub(crate) mod shape_dry_run_operation_exception;

pub(crate) mod shape_export_result_archive_input;

pub(crate) mod shape_export_result_archive_output;

pub(crate) mod shape_generate_assistant_response_input;

pub(crate) mod shape_generate_assistant_response_output;

pub(crate) mod shape_generate_task_assist_plan_input;

pub(crate) mod shape_generate_task_assist_plan_output;

pub(crate) mod shape_internal_server_error;

pub(crate) mod shape_resource_not_found_exception;

pub(crate) mod shape_send_message_input;

pub(crate) mod shape_send_message_output;

pub(crate) mod shape_service_quota_exceeded_error;

pub(crate) mod shape_service_unavailable_exception;

pub(crate) mod shape_throttling_error;

pub(crate) mod shape_validation_error;

pub fn parse_event_stream_error_metadata(
    payload: &::bytes::Bytes,
) -> ::std::result::Result<
    ::aws_smithy_types::error::metadata::Builder,
    ::aws_smithy_json::deserialize::error::DeserializeError,
> {
    crate::json_errors::parse_error_metadata(payload, &::aws_smithy_runtime_api::http::Headers::new())
}

pub(crate) mod shape_conversation_state;

pub(crate) mod shape_export_context;

pub(crate) mod shape_workspace_state;

pub(crate) mod shape_assistant_response_event;

pub(crate) mod shape_binary_metadata_event;

pub(crate) mod shape_binary_payload_event;

pub(crate) mod shape_chat_message;

pub(crate) mod shape_citation_event;

pub(crate) mod shape_code_event;

pub(crate) mod shape_code_reference_event;

pub(crate) mod shape_followup_prompt_event;

pub(crate) mod shape_intents_event;

pub(crate) mod shape_interaction_components_event;

pub(crate) mod shape_invalid_state_event;

pub(crate) mod shape_message_metadata_event;

pub(crate) mod shape_metadata_event;

pub(crate) mod shape_programming_language;

pub(crate) mod shape_supplementary_web_links_event;

pub(crate) mod shape_tool_result_event;

pub(crate) mod shape_tool_use_event;

pub(crate) mod shape_transformation_export_context;

pub(crate) mod shape_unit_test_generation_export_context;

pub(crate) mod shape_assistant_response_message;

pub(crate) mod shape_user_input_message;

pub(crate) mod shape_cache_point;

pub(crate) mod shape_citation_target;

pub(crate) mod shape_client_cache_config;

pub(crate) mod shape_followup_prompt;

pub(crate) mod shape_image_block;

pub(crate) mod shape_intent_map;

pub(crate) mod shape_interaction_component_entry_list;

pub(crate) mod shape_reference;

pub(crate) mod shape_references;

pub(crate) mod shape_supplementary_web_link;

pub(crate) mod shape_supplementary_web_links;

pub(crate) mod shape_token_usage;

pub(crate) mod shape_tool_result;

pub(crate) mod shape_tool_use;

pub(crate) mod shape_user_input_message_context;

pub(crate) mod shape_additional_content_entry;

pub(crate) mod shape_app_studio_state;

pub(crate) mod shape_console_state;

pub(crate) mod shape_diagnostic;

pub(crate) mod shape_editor_state;

pub(crate) mod shape_env_state;

pub(crate) mod shape_git_state;

pub(crate) mod shape_image_source;

pub(crate) mod shape_intent_data;

pub(crate) mod shape_interaction_component_entry;

pub(crate) mod shape_shell_state;

pub(crate) mod shape_span;

pub(crate) mod shape_tool;

pub(crate) mod shape_tool_result_content;

pub(crate) mod shape_user_settings;

pub(crate) mod shape_cursor_state;

pub(crate) mod shape_environment_variable;

pub(crate) mod shape_intent_data_type;

pub(crate) mod shape_interaction_component;

pub(crate) mod shape_relevant_text_document;

pub(crate) mod shape_runtime_diagnostic;

pub(crate) mod shape_shell_history_entry;

pub(crate) mod shape_text_document;

pub(crate) mod shape_text_document_diagnostic;

pub(crate) mod shape_tool_result_content_block;

pub(crate) mod shape_tool_specification;

pub(crate) mod shape_action;

pub(crate) mod shape_alert;

pub(crate) mod shape_code_description;

pub(crate) mod shape_diagnostic_related_information;

pub(crate) mod shape_document_symbol;

pub(crate) mod shape_infrastructure_update;

pub(crate) mod shape_position;

pub(crate) mod shape_progress;

pub(crate) mod shape_range;

pub(crate) mod shape_resource;

pub(crate) mod shape_resource_list;

pub(crate) mod shape_section;

pub(crate) mod shape_step;

pub(crate) mod shape_suggestions;

pub(crate) mod shape_task_details;

pub(crate) mod shape_task_reference;

pub(crate) mod shape_text;

pub(crate) mod shape_tool_input_schema;

pub(crate) mod shape_alert_component_list;

pub(crate) mod shape_diagnostic_location;

pub(crate) mod shape_infrastructure_update_transition;

pub(crate) mod shape_module_link;

pub(crate) mod shape_progress_component_list;

pub(crate) mod shape_resources;

pub(crate) mod shape_section_component_list;

pub(crate) mod shape_step_component_list;

pub(crate) mod shape_suggestion_list;

pub(crate) mod shape_task_action_list;

pub(crate) mod shape_task_component_list;

pub(crate) mod shape_task_overview;

pub(crate) mod shape_web_link;

pub(crate) mod shape_alert_component;

pub(crate) mod shape_cloud_watch_troubleshooting_link;

pub(crate) mod shape_progress_component;

pub(crate) mod shape_section_component;

pub(crate) mod shape_step_component;

pub(crate) mod shape_suggestion;

pub(crate) mod shape_task_action;

pub(crate) mod shape_task_component;

pub(crate) mod shape_task_action_confirmation;

pub(crate) mod shape_task_action_note;

pub(crate) mod shape_task_action_payload;
