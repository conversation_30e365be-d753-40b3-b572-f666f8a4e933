// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_user_input_message(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::UserInputMessage,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    {
        object.key("content").string(input.content.as_str());
    }
    if let Some(var_1) = &input.user_input_message_context {
        #[allow(unused_mut)]
        let mut object_2 = object.key("userInputMessageContext").start_object();
        crate::protocol_serde::shape_user_input_message_context::ser_user_input_message_context(&mut object_2, var_1)?;
        object_2.finish();
    }
    if let Some(var_3) = &input.user_intent {
        object.key("userIntent").string(var_3.as_str());
    }
    if let Some(var_4) = &input.origin {
        object.key("origin").string(var_4.as_str());
    }
    if let Some(var_5) = &input.images {
        let mut array_6 = object.key("images").start_array();
        for item_7 in var_5 {
            {
                #[allow(unused_mut)]
                let mut object_8 = array_6.value().start_object();
                crate::protocol_serde::shape_image_block::ser_image_block(&mut object_8, item_7)?;
                object_8.finish();
            }
        }
        array_6.finish();
    }
    if let Some(var_9) = &input.model_id {
        object.key("modelId").string(var_9.as_str());
    }
    if let Some(var_10) = &input.cache_point {
        #[allow(unused_mut)]
        let mut object_11 = object.key("cachePoint").start_object();
        crate::protocol_serde::shape_cache_point::ser_cache_point(&mut object_11, var_10)?;
        object_11.finish();
    }
    if let Some(var_12) = &input.client_cache_config {
        #[allow(unused_mut)]
        let mut object_13 = object.key("clientCacheConfig").start_object();
        crate::protocol_serde::shape_client_cache_config::ser_client_cache_config(&mut object_13, var_12)?;
        object_13.finish();
    }
    Ok(())
}
