// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_assistant_response_message(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::AssistantResponseMessage,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.message_id {
        object.key("messageId").string(var_1.as_str());
    }
    {
        object.key("content").string(input.content.as_str());
    }
    if let Some(var_2) = &input.supplementary_web_links {
        let mut array_3 = object.key("supplementaryWebLinks").start_array();
        for item_4 in var_2 {
            {
                #[allow(unused_mut)]
                let mut object_5 = array_3.value().start_object();
                crate::protocol_serde::shape_supplementary_web_link::ser_supplementary_web_link(&mut object_5, item_4)?;
                object_5.finish();
            }
        }
        array_3.finish();
    }
    if let Some(var_6) = &input.references {
        let mut array_7 = object.key("references").start_array();
        for item_8 in var_6 {
            {
                #[allow(unused_mut)]
                let mut object_9 = array_7.value().start_object();
                crate::protocol_serde::shape_reference::ser_reference(&mut object_9, item_8)?;
                object_9.finish();
            }
        }
        array_7.finish();
    }
    if let Some(var_10) = &input.followup_prompt {
        #[allow(unused_mut)]
        let mut object_11 = object.key("followupPrompt").start_object();
        crate::protocol_serde::shape_followup_prompt::ser_followup_prompt(&mut object_11, var_10)?;
        object_11.finish();
    }
    if let Some(var_12) = &input.tool_uses {
        let mut array_13 = object.key("toolUses").start_array();
        for item_14 in var_12 {
            {
                #[allow(unused_mut)]
                let mut object_15 = array_13.value().start_object();
                crate::protocol_serde::shape_tool_use::ser_tool_use(&mut object_15, item_14)?;
                object_15.finish();
            }
        }
        array_13.finish();
    }
    if let Some(var_16) = &input.cache_point {
        #[allow(unused_mut)]
        let mut object_17 = object.key("cachePoint").start_object();
        crate::protocol_serde::shape_cache_point::ser_cache_point(&mut object_17, var_16)?;
        object_17.finish();
    }
    Ok(())
}
