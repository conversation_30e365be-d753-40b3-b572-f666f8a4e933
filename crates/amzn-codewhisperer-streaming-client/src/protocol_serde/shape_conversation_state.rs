// Code generated by software.amazon.smithy.rust.codegen.smithy-rs. DO NOT EDIT.
pub fn ser_conversation_state(
    object: &mut ::aws_smithy_json::serialize::JsonObjectWriter,
    input: &crate::types::ConversationState,
) -> ::std::result::Result<(), ::aws_smithy_types::error::operation::SerializationError> {
    if let Some(var_1) = &input.conversation_id {
        object.key("conversationId").string(var_1.as_str());
    }
    if let Some(var_2) = &input.workspace_id {
        object.key("workspaceId").string(var_2.as_str());
    }
    if let Some(var_3) = &input.history {
        let mut array_4 = object.key("history").start_array();
        for item_5 in var_3 {
            {
                #[allow(unused_mut)]
                let mut object_6 = array_4.value().start_object();
                crate::protocol_serde::shape_chat_message::ser_chat_message(&mut object_6, item_5)?;
                object_6.finish();
            }
        }
        array_4.finish();
    }
    {
        #[allow(unused_mut)]
        let mut object_7 = object.key("currentMessage").start_object();
        crate::protocol_serde::shape_chat_message::ser_chat_message(&mut object_7, &input.current_message)?;
        object_7.finish();
    }
    {
        object.key("chatTriggerType").string(input.chat_trigger_type.as_str());
    }
    if let Some(var_8) = &input.customization_arn {
        object.key("customizationArn").string(var_8.as_str());
    }
    Ok(())
}
