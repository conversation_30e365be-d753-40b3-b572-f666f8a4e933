## User settings
xcuserdata/

## gcc Patch
/*.gcno

# Misc
.DS_Store
node_modules/
.eslintcache
yarn-error.log
yarn.lock
package-lock.json
*.info
*.swp
*.swo
*.pending-snap
*.sig
*.asc
coverage/
.turbo

# Python
__pycache__
.venv

# Ignore all compiled protobuf artifacts
**/*.pb.ts

target/
build
book/
.vscode-test/

# ide folders
.vscode/
.fleet/
.idea/

# yarn v2
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

.env*

run-build.sh
